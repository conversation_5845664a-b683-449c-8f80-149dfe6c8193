part of 'offer_clone_bloc.dart';

abstract class OfferCloneState extends Equatable {
  /// Identificador de la oferta que deseamos clonar.
  final String offerToCloneId;

  OfferCloneState({required this.offerToCloneId}) : super();

  @override
  List<Object> get props => [offerToCloneId];
}

/// Inicialización (cargar formulario, datos asociados...)
class OfferCloneInitState extends OfferCloneState {
  OfferCloneInitState({required String offerToCloneId}) : super(offerToCloneId: offerToCloneId);
}

class OfferCloneYesOrNotState extends OfferCloneState {
  final OfferDto offerToClone;
  final List<OfferversiontypeDto> offerversionTypes;
  OfferCloneYesOrNotState(
      {required offerToCloneId, required OfferDto this.offerToClone, required this.offerversionTypes})
      : super(offerToCloneId: offerToCloneId);
}

class OfferCloneClonedState extends OfferCloneState {
  final OfferDto createdOffer;
  OfferCloneClonedState({required String offerToCloneId, required this.createdOffer})
      : super(offerToCloneId: offerToCloneId);
}

class OfferCloneFailureState extends OfferCloneState {
  final String error;
  OfferCloneFailureState({required String offerToCloneId, required this.error}) : super(offerToCloneId: offerToCloneId);

  @override
  List<Object> get props => [error, this.offerToCloneId];
}
