import 'dart:async';
import 'dart:math';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:agentor_repositoryns/models/offer_version_dto.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';

part 'offer_clone_event.dart';
part 'offer_clone_state.dart';

const _MORTAGE_YEARS = 30;
const double _MORTAGE_RATE = 2;

class OfferCloneBloc extends Bloc<OfferCloneEvent, OfferCloneState> {
  final AppModelsNS appModels;

  OfferCloneBloc({required this.appModels, required String offerToCloneId, required ModelsChannelBloc modelsChannel})
      : super(OfferCloneInitState(offerToCloneId: offerToCloneId));

  factory OfferCloneBloc.create({required BuildContext context, required String OfferCloneId}) {
    return OfferCloneBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      offerToCloneId: OfferCloneId,
    );
  }

  @override
  Stream<Transition<OfferCloneEvent, OfferCloneState>> transformEvents(
    Stream<OfferCloneEvent> events,
    TransitionFunction<OfferCloneEvent, OfferCloneState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<OfferCloneState> mapEventToState(OfferCloneEvent event) async* {
    if (event is OfferCloneOnLoadEvent)
      yield* _mapOnLoad(state as OfferCloneInitState, event);
    else if (state is OfferCloneYesOrNotState && event is OfferCloneOnCloneEvent)
      yield* _mapOnClone(state as OfferCloneYesOrNotState, event);
  }

  Stream<OfferCloneState> _mapOnLoad(OfferCloneInitState state, OfferCloneOnLoadEvent event) async* {
    final offerToClone = await appModels.readOffer(state.offerToCloneId);

    if (offerToClone != null) {
      final offerversionTypes = await appModels.getOfferVersiontypes();
      yield OfferCloneYesOrNotState(
        offerToCloneId: state.offerToCloneId,
        offerToClone: offerToClone,
        offerversionTypes: offerversionTypes,
      );
    } else {
      yield OfferCloneFailureState(
        offerToCloneId: state.offerToCloneId,
        error: "No se ha encontrado la oferta a clonar",
      );
    }
  }

  Stream<OfferCloneState> _mapOnClone(OfferCloneYesOrNotState state, OfferCloneOnCloneEvent event) async* {
    try {
      final versiontype_code = event.versiontype_code;
      final version_disclaimer = state.offerversionTypes.firstWhere((i) => i.code.v == versiontype_code);
      final version_disclaimer_localized = version_disclaimer.disclaimer.vn?.localized ?? "";
      final sale_amount = state.offerToClone.sale.vn?.amount.vn ?? 0;

      //Calculo de cuota mensual
      final double monthlyPayment = _calcMonthlyPayment(sale_amount, _MORTAGE_RATE, _MORTAGE_YEARS);

      final newOfferData = state.offerToClone.copyWith(
        status: Some(OfferstatusDto(code: Some(OfferstatusCode.draft))),
        version: Some(
          OfferVersionDto(
            of: Some(OfferDto(id: state.offerToClone.id)),
            type: Some(OfferversiontypeDto(code: Some(versiontype_code))),
            disclaimer: _buildComputedDisclaimer(
                    version_disclaimer_localized, sale_amount, _MORTAGE_RATE, _MORTAGE_YEARS, monthlyPayment)
                .toSome(),
          ),
        ),
        sale: Some(OfferSaleDto(
            allowed: True, monthlyPayment: Some(monthlyPayment), amount: Some(state.offerToClone.sale.vn?.amount.vn))),
      );
      final createdOffer = await appModels.createOffer(newOfferData);

      yield OfferCloneClonedState(offerToCloneId: state.offerToCloneId, createdOffer: createdOffer);
    } on Exception {
      yield OfferCloneFailureState(offerToCloneId: state.offerToCloneId, error: "Problemas al clonar la oferta");
    }
  }
}

double _calcMonthlyPayment(double homePrice, double interest, int loanLength) {
  int n = 12 * loanLength;
  double c = interest / 12.0 / 100.0;
  double monthlyPayment = 0.0;

  // Calculate the monthly payment
  monthlyPayment = homePrice * c * pow(1 + c, n) / (pow(1 + c, n) - 1);

  // Round to the nearest 10
  monthlyPayment = ((monthlyPayment.round() / 10).ceil() * 10).toDouble();

  return monthlyPayment;
}

String _buildComputedDisclaimer(
    String disclaimer, double sale_amount, double interest, int loanLength, double monthlyPayment) {
  return disclaimer
      .replaceAll("[[sale_amount]]", sale_amount.toString() + " €")
      .replaceAll("[[sale_monthlyPayment]]", monthlyPayment.toString() + " €")
      .replaceAll("[[interest_rate]]", interest.toString() + "%")
      .replaceAll("[[mortgage_years]]", loanLength.toString());
}
