part of 'offer_clone_bloc.dart';

abstract class OfferCloneEvent extends Equatable {
  const OfferCloneEvent();

  @override
  List<Object> get props => [];
}

///
/// Evento inicial
///
class OfferCloneOnLoadEvent extends OfferCloneEvent {
  OfferCloneOnLoadEvent() : super();

  @override
  List<Object> get props => [];
  @override
  String toString() => "${super.toString()}";
}

///
/// Seleccionar tipo de clon
///
class OfferCloneOnTypeEvent extends OfferCloneEvent {
  OfferCloneOnTypeEvent() : super();
}

///
/// Empezar  clonación
///
class OfferCloneOnCloneEvent extends OfferCloneEvent {
  final versiontype_code;
  OfferCloneOnCloneEvent({required this.versiontype_code}) : super();
}

///
/// La oferta hasido conada.  La propiedad informa de la nueva oferta
///
class OfferCloneOnClonedEvent extends OfferCloneEvent {
  final OfferDto createdOffer;
  OfferCloneOnClonedEvent({required this.createdOffer}) : super();
}

///
/// Se ha producido un error procesando otro evento
///
class OfferCloneOnFailureEvent extends OfferCloneEvent {
  final String message;
  OfferCloneOnFailureEvent({required this.message}) : super();
}
