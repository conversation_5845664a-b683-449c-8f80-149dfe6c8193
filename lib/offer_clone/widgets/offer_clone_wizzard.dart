import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/generated/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/common/localizations_helper.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
//import 'package:topbrokers/cloudoffers_export/reports/cloudoffers_export_reports.dart';
import 'package:topbrokers/common/widgets/select_contact_field.dart';
import 'package:topbrokers/offer_clone/bloc/offer_clone_bloc.dart';

const c_any = "any";

class OfferCloneWizzard extends StatefulWidget {
  final String offerId;

  final void Function(String offerId) onEnded;
  final void Function() onCanceled;

  OfferCloneWizzard({
    Key? key,
    required this.offerId,
    required this.onEnded,
    required this.onCanceled,
  }) : super(key: key ?? AgentorKeys.offerImportPage);

  @override
  _OfferCloneWizzardState createState() => _OfferCloneWizzardState();
}

class _OfferCloneWizzardState extends State<OfferCloneWizzard> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();
  String? selected_versiontype_code = null;

  // Form data

  //
  _OfferCloneWizzardState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));

    return BlocProvider<OfferCloneBloc>(
      create: (context) {
        return OfferCloneBloc.create(context: context, OfferCloneId: widget.offerId)..add(OfferCloneOnLoadEvent());
      },
      child: BlocConsumer<OfferCloneBloc, OfferCloneState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          final textTheme = Theme.of(context).textTheme;
          final apploc = context.apploc;

          if (state is OfferCloneFailureState) {
            return _unexpectedError(context, state);
          } else if (state is OfferCloneYesOrNotState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: EdgeInsets.all(20),
                    child: Center(
                      child: Text(apploc.offer_cloneDialog_message(int.parse(state.offerToCloneId)),
                          style: textTheme.headlineSmall),
                    ),
                  ),
                  Divider(height: 10.0, color: Colors.blue),
                  SelectFieldEntry<String>(
                    getValue: () => this.selected_versiontype_code,
                    setValue: (selectedCode) {
                      setState(() {
                        this.selected_versiontype_code = selectedCode;
                      });
                    },
                    isRequired: true,
                    options: state.offerversionTypes.map((vtype) {
                      return SelectOption<String>(value: vtype.code.v, label: vtype.label.vn?.localized ?? "");
                    }).toList(),
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: <Widget>[
                    TextButton.icon(
                      icon: Icon(Icons.cancel),
                      label: Text(apploc.offer_cloneDialog_not),
                      onPressed: () {
                        widget.onCanceled();
                      },
                    ),
                    TextButton.icon(
                      icon: Icon(Icons.check),
                      label: Text(apploc.offer_cloneDialog_yes),
                      onPressed: () {
                        if (selected_versiontype_code != null) {
                          BlocProvider.of<OfferCloneBloc>(context)
                              .add(OfferCloneOnCloneEvent(versiontype_code: this.selected_versiontype_code));
                        }
                      },
                    ),
                  ]),
                ],
              ),
            );
          } else if (state is OfferCloneClonedState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: EdgeInsets.all(20),
                    child: Center(
                      child: Text("Oferta clonada :-)", style: textTheme.headlineSmall),
                    ),
                  ),
                  Divider(height: 40.0, color: Colors.transparent),
                  TextButton.icon(
                    icon: Icon(Icons.check),
                    label: Text(apploc.cloudoffers_import_seeDetailsLabel),
                    onPressed: () {
                      widget.onEnded(state.createdOffer.id.v);
                    },
                  ),
                ],
              ),
            );
          } else {
            return Container(child: Center(child: Text('Estado desconocido')));
          }
        },
      ),
    );
  }

  Widget _unexpectedError(BuildContext context, OfferCloneFailureState state) {
    final apploc = context.apploc;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              apploc.cloudoffers_import_err_importing,
              style: textTheme.titleLarge,
            ),
          ),
          Divider(height: 20.0, color: Colors.transparent),
          Center(
            child: Text(
              state.error,
              style: textTheme.bodyMedium,
            ),
          ),
          Divider(height: 40.0, color: Colors.transparent),
          Padding(
            padding: EdgeInsets.all(5),
            child: TextButton.icon(
              icon: Icon(Icons.check),
              label: Text(apploc.common_Close),
              onPressed: () {
                widget.onCanceled();
              },
            ),
          ),
        ],
      ),
    );
  }
}
