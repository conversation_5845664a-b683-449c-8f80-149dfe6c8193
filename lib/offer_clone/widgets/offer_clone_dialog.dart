import 'package:flutter/material.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/offer_clone/widgets/offer_clone_wizzard.dart';
import 'package:topbrokers/offer_clone/widgets/widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:topbrokers/common/localizations_helper.dart';

Future<String?> offerCloneDialog(
  context, {
  required String cloudOfferId,
}) {
  return showGeneralDialog<String?>(
      context: context,
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      pageBuilder: (
        BuildContext context,
        Animation animation,
        Animation secondaryAnimation,
      ) {
        return AlertDialog(
          insetPadding: EdgeInsets.all(5.0),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          contentPadding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            //crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              OfferCloneWizzard(
                offerId: cloudOfferId,
                onCanceled: () {
                  Navigator.of(context).pop(null);
                },
                onEnded: (newOfferId) {
                  Navigator.of(context).pop(newOfferId);
                },
              ),
            ],
          ),
          actions: [],
        );
      });
}
