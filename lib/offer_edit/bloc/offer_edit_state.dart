part of 'offer_edit_bloc.dart';

class OfferEditState extends Equatable {
  @override
  List<Object> get props => [];
}

class OfferEditLoading extends OfferEditState {}

class OfferEditLoaded extends OfferEditState {
  final OfferDto offer;
  final bool imIndividual;
  final OfferstatusCode originalStatusCode;
  final Map<String, FieldDefDto> definibleFields;
  final List<PropertytypeDto> propertyTypes;

  OfferEditLoaded({
    required this.offer,
    required this.imIndividual,
    required this.definibleFields,
    required this.propertyTypes,
    required this.originalStatusCode,
  }) : super();

  @override
  List<Object> get props => [offer];
}

class OfferEditLoadFailure extends OfferEditState {
  final String error;

  OfferEditLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

class OfferEditSaved extends OfferEditState {
  final OfferDto offer;

  OfferEditSaved({required this.offer}) : super();

  @override
  List<Object> get props => [offer];
}

class OfferEditSaveFailure extends OfferEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  OfferEditSaveFailure({
    required OfferDto offer,
    required bool imIndividual,
    required Map<String, FieldDefDto> definibleFields,
    required List<PropertytypeDto> propertyTypes,
    required OfferstatusCode originalStatusCode,
    required this.error,
  }) : super(
          offer: offer,
          imIndividual: imIndividual,
          originalStatusCode: originalStatusCode,
          definibleFields: definibleFields,
          propertyTypes: propertyTypes,
        );

  factory OfferEditSaveFailure.fromLoadedState(OfferEditLoaded loadedStatus, String error) {
    return OfferEditSaveFailure(
      offer: loadedStatus.offer,
      imIndividual: loadedStatus.imIndividual,
      definibleFields: loadedStatus.definibleFields,
      propertyTypes: loadedStatus.propertyTypes,
      originalStatusCode: loadedStatus.originalStatusCode,
      error: error,
    );
  }
  @override
  List<Object> get props => [lastError, offer, error];
}

class OfferEditValidationFailure extends OfferEditLoaded {
  final lastError = new DateTime.now();
  final String error;

  OfferEditValidationFailure({
    required OfferDto offer,
    required bool imIndividual,
    required Map<String, FieldDefDto> definibleFields,
    required List<PropertytypeDto> propertyTypes,
    required OfferstatusCode originalStatusCode,
    required this.error,
  }) : super(
          offer: offer,
          imIndividual: imIndividual,
          definibleFields: definibleFields,
          propertyTypes: propertyTypes,
          originalStatusCode: originalStatusCode,
        );

  factory OfferEditValidationFailure.fromLoadedState(OfferEditLoaded loadedStatus, String error) {
    return OfferEditValidationFailure(
      offer: loadedStatus.offer,
      imIndividual: loadedStatus.imIndividual,
      definibleFields: loadedStatus.definibleFields,
      propertyTypes: loadedStatus.propertyTypes,
      originalStatusCode: loadedStatus.originalStatusCode,
      error: error,
    );
  }

  @override
  List<Object> get props => [lastError, offer, error];
}
