import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/offer_edit/offer_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';

part 'offer_edit_event.dart';
part 'offer_edit_state.dart';

class OfferEditBloc extends Bloc<OfferEditEvent, OfferEditState> {
  final AppModelsNS appModels;
  final SessionBloc sessionBloc;
  OfferEditBloc({required this.appModels, required this.sessionBloc}) : super(OfferEditState());

  factory OfferEditBloc.create(BuildContext context) {
    final sessionBloc = BlocProvider.of<SessionBloc>(context, listen: false);
    final appModels = Provider.of<AppModelsNS>(context, listen: false);
    return OfferEditBloc(appModels: appModels, sessionBloc: sessionBloc);
  }

  @override
  Stream<Transition<OfferEditEvent, OfferEditState>> transformEvents(
    Stream<OfferEditEvent> events,
    TransitionFunction<OfferEditEvent, OfferEditState> transitionFn,
  ) =>
      super.transformEvents(
        events.debounceTime(const Duration(milliseconds: 100)),
        transitionFn,
      );

  @override
  Stream<OfferEditState> mapEventToState(OfferEditEvent event) async* {
    final state = this.state;
    if (event is OfferEditOnEditEvent) {
      yield* _mapOnEditEventToState(state, event);
    } else if (event is OfferEditOnNewEvent) {
      yield* _mapOnNewEventToState(state, event);
    } else if (event is OfferEditOnSaveEvent && state is OfferEditLoaded) {
      yield* _mapOnSaveEventToState(state, event);
    } else if (event is OfferEditOnValidationErrorEvent && state is OfferEditLoaded) {
      yield* _mapOnValidationErrorEventToState(state, event);
    }
  }

  Stream<OfferEditState> _mapOnEditEventToState(OfferEditState state, OfferEditOnEditEvent event) async* {
    try {
      final imIndividual = sessionBloc.state.imIndividual;
      final offer = await _readOffer(event.offerId);
      final definibleFields = await _readDefinibleFields();
      final propertyTypes = await _listPropertytypes();
      

      if (offer == null)
        yield OfferEditLoadFailure(error: "La oferta no existe");
      else
        yield OfferEditLoaded(
          offer: offer,
          imIndividual: imIndividual,
          definibleFields: definibleFields,
          propertyTypes: propertyTypes,
          originalStatusCode: offer.status.vn?.code.vn ?? OfferstatusCode.news,
        );
    } on Exception {
      yield OfferEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<OfferEditState> _mapOnNewEventToState(OfferEditState state, OfferEditOnNewEvent event) async* {
    try {
      final offer = OfferUtils.emptyOffer();
      if (event.customerId != null) {
        offer.customer = (await appModels.readContact(event.customerId as String)).toSome();
      }
      final definibleFields = await _readDefinibleFields();
      final propertyTypes = await _listPropertytypes();
      final imIndividual = sessionBloc.state.imIndividual;
      yield OfferEditLoaded(
        offer: offer,
        imIndividual: imIndividual,
        definibleFields: definibleFields,
        propertyTypes: propertyTypes,
        originalStatusCode: offer.status.vn?.code.vn ?? OfferstatusCode.news,
      );
    } on Exception {
      yield OfferEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<OfferEditState> _mapOnSaveEventToState(OfferEditLoaded state, OfferEditOnSaveEvent event) async* {
    try {
      final imIndividual = state.imIndividual;
      // Lógica "por defecto" para particulares
      if (imIndividual) {
        // Mandato "por defecto"
        if (event.offer.status.vn?.code.vn == OfferstatusCode.commercialization) {
          // Aseguramos, si es un particular, que hayun mandato
          final start = event.offer.mandate.vn?.start.vn ?? DateTime.now();
          final end = event.offer.mandate.vn?.end.vn ?? start.add(Duration(days: 90));
          event.offer.mandate = Some(OffermandateDto(
            type: Some(
              OffermandatetypeDto(
                code: Some(event.offer.mandate.vn?.type.vn?.code.vn ?? OffermandatetypeCode.other),
              ),
            ),
            start: Some(start),
            end: Some(end),
          ));
        } else
          event.offer.mandate = Some(null);
      }

      if (event.offer.id.vn == null) {
        final created = await _createOffer(event.offer);
        if (created != null) yield OfferEditSaved(offer: created);
      } else {
        final updated = await _updateOffer(event.offer);
        if (updated != null) yield OfferEditSaved(offer: updated);
      }
    } on Exception catch (e) {
      print("*+*+ Error _mapOnSaveEventToState $e");
      final msg = event.offer.id.vn == null ? "Problemas guardando la nueva oferta" : "Problemas guardando cambios";
      yield OfferEditSaveFailure.fromLoadedState(state, msg);
    }
  }

  Stream<OfferEditState> _mapOnValidationErrorEventToState(
    OfferEditLoaded state,
    OfferEditOnValidationErrorEvent event,
  ) async* {
    yield OfferEditValidationFailure.fromLoadedState(state, event.error);
  }

  Future<OfferDto?> _readOffer(String id) {
    return appModels.readOffer(id);
  }

  Future<OfferDto?> _updateOffer(OfferDto offer) {
    return appModels.updateOffer(offer);
  }

  Future<OfferDto?> _createOffer(OfferDto offer) {
    return appModels.createOffer(offer);
  }

    Future<Map<String, FieldDefDto>> _readDefinibleFields() async {
    final result = await appModels.listOfferFielddefs();
    return Map.fromEntries(result.map((fieldDef) => MapEntry(fieldDef.code, fieldDef)));
  }

  Future<List<PropertytypeDto>> _listPropertytypes() {
    return appModels.listPropertytypes(filter: PropertytypesListFilter(includeSubtypes: True));
  }
}
