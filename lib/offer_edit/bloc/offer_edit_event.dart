part of 'offer_edit_bloc.dart';

abstract class OfferEditEvent extends Equatable {
  const OfferEditEvent();

  @override
  List<Object> get props => [];
}

class OfferEditOnEditEvent extends OfferEditEvent {
  final String offerId;
  OfferEditOnEditEvent({required this.offerId});
  @override
  List<Object> get props => [offerId];
}

class OfferEditOnNewEvent extends OfferEditEvent {
  final String? customerId;
  OfferEditOnNewEvent({this.customerId}) : super();
  @override
  List<Object> get props => [customerId??""];
}

///
/// El usuario quiere guardar los cambios
///
class OfferEditOnSaveEvent extends OfferEditEvent {
  final OfferDto offer;
  OfferEditOnSaveEvent({required this.offer}) : super();

  @override
  List<Object> get props => [offer];
}

class OfferEditOnValidationErrorEvent extends OfferEditEvent {
  final String error;
  OfferEditOnValidationErrorEvent({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

///
/// Los cambios se han guardado corréctamente
///
class OfferEditOnSavedEvent extends OfferEditEvent {}
