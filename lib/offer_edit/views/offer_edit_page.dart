import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:form_builder/form_builder.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contact_edit/models/contact_edit_page_result.dart';
import 'package:topbrokers/contacts_list/widgets/widgets.dart';
import 'package:topbrokers/global/auth_agent_provider.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/offer_edit/bloc/offer_edit_bloc.dart';
import 'package:topbrokers/offer_edit/models/offer_edit_page_params.dart';
import 'package:topbrokers/offer_edit/models/offer_edit_page_result.dart';
import 'package:topbrokers/offer_edit/views/property_form_group.dart';
import 'package:topbrokers/routes.dart';

class OfferEditPage extends StatefulWidget {
  final bool isNewOffer;

  final OfferEditPageParams params;

  OfferEditPage({
    Key? key,
    required this.params,
  })  : isNewOffer = params is NewOfferEditParams,
        super(
          key: key ?? AgentorKeys.newOfferPage,
        );

  @override
  _OfferEditPageState createState() => _OfferEditPageState();
}

class _OfferEditPageState extends State<OfferEditPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) {
        if (this.widget.params is ExistingOfferEditParams) {
          final params = this.widget.params as ExistingOfferEditParams;
          return OfferEditBloc.create(context)..add(OfferEditOnEditEvent(offerId: params.id));
        } else {
          final params = this.widget.params as NewOfferEditParams;
          return OfferEditBloc.create(context)..add(OfferEditOnNewEvent(customerId: params.customerId));
        }
      },
      child: BlocConsumer<OfferEditBloc, OfferEditState>(
        listener: (context, state) {
          if (state is OfferEditSaved) {
            Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));
          }
        },
        builder: (context, state) {
          final localizations = context.getAppLocalizationsOrThrow();
          if (state is OfferEditLoaded) {
            return Scaffold(
              appBar: AppBar(
                title: Text(!widget.isNewOffer ? "${localizations.offer_title} #${state.offer.id.vn ?? ""}" : localizations.offer_titleNew),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: BlocConsumer<OfferEditBloc, OfferEditState>(
                  listener: (context, state) {
                    if (state is OfferEditSaveFailure) {
                      context.showError(state.error);
                    } else if (state is OfferEditValidationFailure) {
                      context.showError(state.error);
                    }
                  },
                  builder: (context, state) => _buildForm(
                    context,
                    (state as OfferEditLoaded).offer,
                    state.imIndividual,
                    state.definibleFields,
                    state.propertyTypes,
                  ),
                ),
              ),
              floatingActionButton: FloatingActionButton(
                key: AgentorKeys.saveNewOffer,
                tooltip: widget.isNewOffer ? localizations.offer_createBtn : localizations.offer_saveBtn,
                child: Icon(Icons.save_outlined, semanticLabel: "Save"),
                onPressed: () async {
                  if (_formKey.currentState?.validate() ?? false) {
                    _formKey.currentState?.save();
                    _validateUnpublishing(
                      context,
                      state.offer,
                      state.originalStatusCode,
                      () {
                        BlocProvider.of<OfferEditBloc>(context).add(OfferEditOnSaveEvent(offer: state.offer));
                      },
                      () {},
                    );
                  } else {
                    BlocProvider.of<OfferEditBloc>(context).add(OfferEditOnValidationErrorEvent(error: localizations.common_formWithErrors));
                  }
                },
              ),
            );
          } else if (state is OfferEditLoadFailure) {
            return Scaffold(
              appBar: AppBar(
                title: Text(!widget.isNewOffer ? localizations.offer_title : localizations.offer_titleNew),
                centerTitle: true,
              ),
              body: Center(child: Text(state.error)),
            );
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  Widget _buildForm(
    BuildContext context,
    OfferDto offer,
    bool imIndividual,
    Map<String, FieldDefDto> fieldsDefinitions,
    List<PropertytypeDto> propertyTypes,
  ) {
    return Container(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child:
            Column(children: [_offerEntries(context, offer, imIndividual, fieldsDefinitions, propertyTypes).buildForm(key: _formKey, context: context), SizedBox.fromSize(size: Size.fromHeight(64))]),
      ),
    );
  }

  List<Widget> _offerEntries(
    BuildContext context,
    OfferDto offer,
    bool imIndividual,
    Map<String, FieldDefDto> fieldsDefinitions,
    List<PropertytypeDto> propertyTypes,
  ) {
    final localizations = context.getAppLocalizationsOrThrow();
    // Los particulares no puede crear ofertas en estado "news"
    final statusFieldDefinition = !imIndividual
        ? fieldsDefinitions[FielddefsSrv.c_offer_status]
        : fieldsDefinitions[FielddefsSrv.c_offer_status]
            ?.copyWith(values: fieldsDefinitions[FielddefsSrv.c_offer_status]?.values?.where((v) => v.code.vn != OfferstatusCode.news.enumToString()).toList());
    return [
      GroupEntry(label: localizations.offer_mainHeading, children: [
        // Confiamos que el campo existe... no necesitamos añadir un IF
        if (statusFieldDefinition != null)
          statusFieldDefinition.buildEntry<String>(
            getValue: () => (offer.status.v.code.vn ?? OfferstatusCode.draft).enumToString(),
            setValue: (String? code) {
              setState(() {
                // offer.status.v.code = YSome(code?.toOfferStatusCode());  Pero por claridad los eparamos en líneas
                if (code == null)
                  offer.status.v.code = None();
                else {
                  final statusCode = code.toOfferStatusCode();
                  offer.status.v.code = Some(statusCode);
                  offer.historic = statusCode == OfferstatusCode.historic ? Some(OfferhistoricDto()) : None();
                }
              });
            },
            isRequired: true,
          ),
        if (offer.historic.vn != null)
          fieldsDefinitions[FielddefsSrv.c_offer_historic_cause_type]!.buildEntry(
            getValue: () => offer.historic.vn?.cause.vn?.code.vn,
            setValue: (String? code) {
              setState(() {
                if (code == null)
                  offer.historic.v!.cause = None();
                else
                  offer.historic.v!.cause = Some(OfferhistoriccauseDto(code: Some(code)));
              });
            },
            isRequired: true,
          ),
        if (!imIndividual)
          SearchFieldEntry<ContactDto>(
            label: fieldsDefinitions[FielddefsSrv.c_offer_customer]!.label?.localized,
            getValue: () => offer.customer.vn,
            setValue: (ContactDto? value) {
              setState(() {
                offer.customer = Some(value);
              });
            },
            onSearch: (String search) => api.listContacts(filter: ContactsListFilter(search: Some(search))),
            onAdd: () async {
              final result = await Navigator.pushNamed(context, AgentorRoutes.addContact);
              if (result is ContactEditPageResult && result.id != null) {
                try {
                  return await api.getContact(result.id as String);
                } on Exception {
                  print("Problemas obteniendo/asignando contacto");
                }
              }
              return null;
            },
            itemBuilder: (context, contact, {bool isSelected = false}) => ContactListItem(contact: contact),
            valueToString: (contact) => contact != null ? contact.name.vn ?? "" : "Seleccione un contacto",
            isRequired: false, //offer.status.vn?.code.vn == OfferstatusCode.commercialization,
            rightWidget: Row(
              children: [
                if (offer.customer.vn?.id.vn != null)
                  InkWell(
                    child: Icon(Icons.open_in_new),
                    onTap: () async {
                      final customerId = offer.customer.v!.id.v;
                      Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: customerId);
                    },
                  ),
              ],
            ),
          ),
        if (imIndividual == False)
          fieldsDefinitions[FielddefsSrv.c_offer_urgency]!.buildEntry(
              getValue: () => offer.urgency.vn,
              setValue: (int? value) {
                setState(() => offer.urgency = Some(value));
              }),
        fieldsDefinitions[FielddefsSrv.c_offer_type]!.buildEntry(
          getValue: () => offer.sale.vn?.allowed == True
              ? "sale"
              : offer.rent.vn?.allowed == True
                  ? "rent"
                  : null,
          setValue: (String? value) {
            setState(() {
              if (value == "sale") {
                offer.sale = Some(OfferSaleDto.emptySale());
                offer.rent = Some(OfferRentDto.emptyNoRent());
              } else if (value == "rent") {
                offer.sale = Some(OfferSaleDto.emptyNoSale());
                offer.rent = Some(OfferRentDto.emptyRent());
              } else {
                offer.sale = Some(OfferSaleDto.emptyNoSale());
                offer.rent = Some(OfferRentDto.emptyNoRent());
              }
            });
          },
          isRequired: true,
        ),
        if (offer.sale.vn?.allowed == True)
          fieldsDefinitions[FielddefsSrv.c_offer_sale_amount]!.buildEntry(
              getValue: () => offer.sale.vn?.amount.vn,
              setValue: (double? v) {
                setState(() {
                  offer.sale.v.amount = Some(v);
                });
              },
              isRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization),
        if (offer.sale.vn?.allowed == True)
          fieldsDefinitions[FielddefsSrv.c_offer_sale_marketAmount]!.buildEntry(
              getValue: () => offer.sale.v.marketAmount.vn,
              setValue: (double? v) {
                setState(() {
                  offer.sale.v.marketAmount = Some(v);
                });
              }),
        if (offer.rent.vn?.allowed == True)
          fieldsDefinitions[FielddefsSrv.c_offer_rent_amount]!.buildEntry(
            getValue: () => offer.rent.v.amount.v,
            setValue: (double? v) {
              setState(() {
                offer.rent.v.amount = Some(v);
              });
            },
            isRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization,
          ),
        if (offer.rent.vn?.allowed == True)
          fieldsDefinitions[FielddefsSrv.c_offer_rent_marketAmount]!.buildEntry(
            getValue: () => offer.rent.v.marketAmount.vn,
            setValue: (double? v) {
              setState(() => offer.rent.v.marketAmount = Some(v));
            },
          ),
        if (offer.version.vn != null)
          fieldsDefinitions[FielddefsSrv.c_offer_sale_monthlyPayment]!.buildEntry(
              getValue: () => offer.sale.v.monthlyPayment.vn,
              setValue: (double? v) {
                setState(() {
                  offer.sale.v.monthlyPayment = Some(v);
                });
              }),
        if (offer.version.vn != null)
          fieldsDefinitions[FielddefsSrv.c_offer_version_disclaimer]!.buildEntry(
            getValue: () => offer.version.v?.disclaimer.vn,
            setValue: (String? v) {
              setState(() => offer.version.v?.disclaimer = Some(v));
            },
          ),

        fieldsDefinitions[FielddefsSrv.c_offer_description]!.buildEntry(
          getValue: () => offer.description.vn,
          setValue: (String? v) {
            setState(() => offer.description = Some(v));
          },
        ),
      ]),
      if (offer.property.vn != null)
        PropertyFormGroup(
          property: offer.property.v,
          addressIsRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization,
          subtypeIsRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization,
          qualityInfoIsRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization,
          fieldsDefinitions: fieldsDefinitions,
          propertyTypes: propertyTypes,
        ),
      GroupEntry(
        label: localizations.offer_internalsHeading,
        children: <FormEntry?>[
          if (!imIndividual)
            if (offer.sale is Some && offer.sale.v.allowed == True)
              fieldsDefinitions[FielddefsSrv.c_offer_sale_fee_type]?.buildEntry(
                getValue: () => offer.sale.vn?.fee.vn?.type.vn?.code.vn?.enumToString(),
                setValue: (String? value) {
                  setState(() {
                    if (offer.sale.v.fee.vn == null) {
                      offer.sale.v.fee = Some(SaleFeeDto.fromJson({
                        "type": {"code": value},
                        "value": 0
                      }));
                    } else {
                      offer.sale.v.fee.v!.type = Some(SalefeetypeDto.fromJson({"code": value}));
                    }
                  });
                },
              ),
          if (!imIndividual)
            if (offer.sale is Some && offer.sale.v.allowed == True)
              if (offer.sale.v.fee.vn?.type.vn?.code.vn == SalefeetypeCode.percent)
                fieldsDefinitions[FielddefsSrv.c_offer_sale_fee_percentValue]?.buildEntry(
                  getValue: () => offer.sale.v.fee.v?.value.vn,
                  setValue: (double? value) {
                    setState(() {
                      offer.sale.v.fee.v!.value = Some(value);
                    });
                  },
                  getValueComment: () {
                    return '${((offer.sale.v.fee.v!.value.vn ?? 0) * (offer.sale.v.amount.vn ?? 0) / 100.0).round()} €';
                  },
                )
              else if (offer.sale.v.fee.vn?.type.vn?.code.vn == SalefeetypeCode.fixed)
                fieldsDefinitions[FielddefsSrv.c_offer_sale_fee_fixedValue]?.buildEntry(
                  getValue: () => offer.sale.v.fee.v!.value.vn,
                  setValue: (double? value) {
                    setState(() {
                      offer.sale.v.fee.v!.value = Some(value);
                    });
                  },
                ),
          if (!imIndividual)
            fieldsDefinitions[FielddefsSrv.c_offer_mandate_type]?.buildEntry(
              getValue: () => offer.mandate.vn?.type.vn?.code.vn?.enumToString(),
              setValue: (String? code) {
                setState(() {
                  if (code == null) {
                    offer.mandate = Some(null);
                  } else {
                    if (offer.mandate.vn == null) {
                      offer.mandate = Some(OffermandateDto());
                    }
                    offer.mandate.v!.type = Some(OffermandatetypeDto(code: Some(code.toOffermandatetypeCode())));
                  }
                });
              },
              isRequired: offer.status.vn?.code == Some(OfferstatusCode.commercialization),
            ),
          if (!imIndividual)
            if (offer.mandate.vn != null)
              fieldsDefinitions[FielddefsSrv.c_offer_mandate_start]?.buildEntry(
                getValue: () => offer.mandate.vn?.start.vn,
                setValue: (DateTime? date) {
                  setState(() {
                    offer.mandate.v!.start = Some(date);
                  });
                },
                isRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization,
              ),
          if (!imIndividual)
            if (offer.mandate.vn != null)
              fieldsDefinitions[FielddefsSrv.c_offer_mandate_end]?.buildEntry(
                getValue: () => offer.mandate.vn?.end.vn,
                setValue: (DateTime? date) {
                  setState(() {
                    offer.mandate.v!.end = Some(date?.at(20, 0, 0));
                  });
                },
                isRequired: offer.status.vn?.code.vn == OfferstatusCode.commercialization,
              ),
          fieldsDefinitions[FielddefsSrv.c_offer_notes]?.buildEntry(
            getValue: () => offer.notes.vn,
            setValue: (String? notes) {
              setState(() {
                offer.notes = Some(notes);
              });
            },
          )
        ].where((w) => w != null).map((FormEntry? w) => w as FormEntry),
      )
    ];
  }

  void _validateUnpublishing(
    BuildContext context,
    OfferDto off,
    OfferstatusCode originalStatusCode,
    void Function() fYes,
    void Function() fNo,
  ) {
    if (off.status.vn?.code.vn != originalStatusCode && originalStatusCode == OfferstatusCode.commercialization)
      showDialog(
        context: context,
        builder: (BuildContext context) {
          final bodyText2 = Theme.of(context).textTheme.bodyMedium;
          return AlertDialog(
            title: Text('Cambio de estado'),
            content: HTML.toRichText(
              context,
              [
                "<p>",
                "Sólo las ofertas en comercialización pueden publicarse en portales inmobiliarios.<br/>",
                "Si continúa, la oferta se retirará de todos los portales en los que esté publicada.",
                "</p>",
                "<p>¿Desea continuar?</p>",
              ].join(""),
              overrideStyle: Map<String, TextStyle>()
                ..addEntries([
                  if (bodyText2 != null) MapEntry<String, TextStyle>("p", bodyText2),
                ]),
            ),
            buttonPadding: EdgeInsets.fromLTRB(20, 0, 20, 0),
            actions: <Widget>[
              ElevatedButton(
                child: Text("Sí, continuar"),
                onPressed: () {
                  fYes();
                  Navigator.of(context).pop();
                },
              ),
              ElevatedButton(
                child: Text("No, cancelar"),
                onPressed: () {
                  fNo();
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    else
      fYes();
  }
}
