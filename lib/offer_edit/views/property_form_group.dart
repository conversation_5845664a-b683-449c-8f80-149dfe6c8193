import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/app_constants.dart';
import 'package:topbrokers/offer_edit/widgets/city_list_item.dart';
import 'package:topbrokers/propertyzoneslist/propertyzoneslist.dart';
import 'package:flutter/material.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/common/helpers.dart';

class PropertyFormGroup extends StatefulWidget {
  final PropertyDto property;

  ///
  /// Address street and number are required
  ///
  final bool addressIsRequired;
  final bool subtypeIsRequired;
  final bool qualityInfoIsRequired;
  final Map<String, FieldDefDto> fieldsDefinitions;
  final List<PropertytypeDto> propertyTypes;

  PropertyFormGroup({
    required this.property,
    this.addressIsRequired = false,
    this.subtypeIsRequired = false,
    this.qualityInfoIsRequired = false,
    required this.fieldsDefinitions,
    required this.propertyTypes,
  }) : super();

  @override
  _PropertyFormGroupState createState() => _PropertyFormGroupState();
}

class _PropertyFormGroupState extends State<PropertyFormGroup> {
  final api = Deps.solve<ApiServices>();

  @override
  Widget build(BuildContext context) {
    return propertyFormEntries(
      context,
      fieldsDefinitions: widget.fieldsDefinitions,
      propertyTypes: widget.propertyTypes,
      property: widget.property,
      addressIsRequired: widget.addressIsRequired,
      subtypeIsRequired: widget.subtypeIsRequired,
      qualityInfoIsRequired: widget.subtypeIsRequired,
    );
  }

  GroupEntry propertyFormEntries(
    BuildContext context, {
    required PropertyDto property,
    required bool addressIsRequired,
    required bool subtypeIsRequired,
    required bool qualityInfoIsRequired,
    required Map<String, FieldDefDto> fieldsDefinitions,
    required List<PropertytypeDto> propertyTypes,
  }) {
    return GroupEntry(label: "Inmueble", children: [
      // Tipo
      if (fieldsDefinitions[FielddefsSrv.c_prop_type] != null)
        SelectFieldEntry<String>(
          getValue: () => property.type.vn?.code.v,
          setValue: (String? value) {
            this.setState(() {
              // Si el tipo seleccionado es distinto al anterior, anulamos el subtipo.
              if (property.type.vn?.code.v != value) {
                property.subtype = Some(null);
              }
              property.type = Some(PropertytypeDto.fromJson({"code": value}));
            });
          },
          isRequired: true,
          label: fieldsDefinitions[FielddefsSrv.c_prop_type]?.label?.localized,
          options: typesToSelectOptions(propertyTypes),
        ),
      if (fieldsDefinitions[FielddefsSrv.c_prop_subtype] != null)
        SelectFieldEntry<String>(
          getValue: () => property.subtype.vn?.code.vn,
          setValue: (String? value) {
            this.setState(() {
              property.subtype = Some(PropertysubtypeDto.fromJson({"code": value}));
            });
          },
          isRequired: qualityInfoIsRequired || subtypeIsRequired,
          label: fieldsDefinitions[FielddefsSrv.c_prop_subtype]?.label?.localized,
          options: subtypesToSelectOptions(propertyTypes, property.type.vn!.code.v),
        ),
      GroupEntry(
        label: "Dirección",
        isSubgroup: true,
        children: [
          if (fieldsDefinitions[FielddefsSrv.c_prop_address_city] != null)
            SearchFieldEntry<CityDto>(
              label: fieldsDefinitions[FielddefsSrv.c_prop_address_city]?.label?.localized,
              getValue: () => property.address.v.city.vn,
              setValue: (CityDto? value) {
                // Dado que es Required, no debería recibirse null
                if (value != null) {
                  this.setState(() => property.address.v.city = Some(value));
                }
              },
              onSearch: (String search) => api.getCities(filter: CitiesListFilter(search: Some(search))),
              itemBuilder: (context, city, {bool? isSelected}) => CityListItem(city: city),
              valueToString: (CityDto? city) => city?.label.vn?.localized ?? "Seleccione una ciudad",
              isRequired: true,
            ),
          if (fieldsDefinitions[FielddefsSrv.c_prop_address_postcode] != null)
            fieldsDefinitions[FielddefsSrv.c_prop_address_postcode]!.buildEntry(
              getValue: () => property.address.v.postcode.vn,
              setValue: (String? value) {
                this.setState(() => property.address.v.postcode = Some(value));
              },
              isRequired: qualityInfoIsRequired || addressIsRequired,
            ),
          if (fieldsDefinitions[FielddefsSrv.c_prop_address_streettype] != null)
            fieldsDefinitions[FielddefsSrv.c_prop_address_streettype]!.buildEntry(
              getValue: () => property.address.v.streettype.vn?.id.vn,
              setValue: (String? value) {
                this.setState(() => property.address.v.streettype = Some(StreettypeDto.fromJson({"id": value})));
              },
              isRequired: qualityInfoIsRequired || addressIsRequired,
            ),
          if (fieldsDefinitions[FielddefsSrv.c_prop_address_streetname] != null)
            fieldsDefinitions[FielddefsSrv.c_prop_address_streetname]!.buildEntry(
              getValue: () => property.address.v.streetname.v,
              setValue: (String? value) {
                this.setState(() => property.address.v.streetname = Some(value));
              },
              isRequired: qualityInfoIsRequired || addressIsRequired,
            ),
          if (fieldsDefinitions[FielddefsSrv.c_prop_address_number] != null)
            fieldsDefinitions[FielddefsSrv.c_prop_address_number]!.buildEntry(
              getValue: () => property.address.v.number.v,
              setValue: (String? value) {
                this.setState(() => property.address.v.number = Some(value));
              },
              isRequired: qualityInfoIsRequired || addressIsRequired,
            ),
          if (fieldsDefinitions[FielddefsSrv.c_prop_address_detail] != null)
            fieldsDefinitions[FielddefsSrv.c_prop_address_detail]!.buildEntry(
              getValue: () => property.address.vn?.detail.vn,
              setValue: (String? value) {
                this.setState(() => property.address.v.detail = Some(value));
              },
            ),
          if (Deps.solve<AppConstants>().zonesEnabled && fieldsDefinitions[FielddefsSrv.c_prop_zone] != null)
            CustomSelectFieldEntry<PropertyzoneDto>(
              label: fieldsDefinitions[FielddefsSrv.c_prop_zone]!.label?.localized,
              valueToString: (PropertyzoneDto? value) => value?.composedName ?? "",
              onSelect: () {
                return selectPropertyzoneDialog(context, initialSelectedId: property.zone.vn?.id ?? const None());
              },
              getValue: () => property.zone.vn,
              setValue: (PropertyzoneDto? value) {
                this.setState(() => property.zone = Some(value));
              },
              isRequired: false,
            ),
        ],
      ),
      GroupEntry(
        label: "Características",
        isSubgroup: true,
        children: _propertyAtributesEntries(property.attributes.v, fieldsDefinitions, qualityInfoIsRequired),
      )
    ]);
  }

  Iterable<FormEntry> _propertyAtributesEntries(
    PropertyAttributesDto attributes,
    Map<String, FieldDefDto> fieldsDefinitions,
    bool qualityInfoIsRequired,
  ) {
    return [
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_status]?.buildEntry(
        getValue: () => attributes.statusCode.vn,
        setValue: (String? v) {
          this.setState(() => attributes.statusCode = Some(v));
        },
        isRequired: qualityInfoIsRequired,
      ),
      // m² totales
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_totalSurface]?.buildEntry(
        isRequired: true,
        getValue: () => attributes.totalSurfaceM2.vn,
        setValue: (double? v) {
          this.setState(() => attributes.totalSurfaceM2 = Some(v ?? 0)); // En realidad nunca es null... pero el compi
        },
      ),
      // m² útiles
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_usefulSurface]?.buildEntry(
        getValue: () => attributes.usefulSurfaceM2.vn,
        setValue: (double? v) {
          this.setState(() => attributes.usefulSurfaceM2 = Some(v));
        },
      ),
      // m² solar
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_solarSurface]?.buildEntry(
        getValue: () => attributes.solarSurfaceM2.vn,
        setValue: (double? v) {
          this.setState(() => attributes.solarSurfaceM2 = Some(v));
        },
      ),
      // Año construcción
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_constructionYear]?.buildEntry(
        getValue: () => attributes.constructionYear.v,
        setValue: (int? v) {
          this.setState(() => attributes.constructionYear = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_conservationStatus]?.buildEntry(
        getValue: () => attributes.conservationStatusCode.v,
        setValue: (String? v) {
          this.setState(() => attributes.conservationStatusCode = Some(v));
        },
        isRequired: qualityInfoIsRequired,
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_individualBedroomsCount]?.buildEntry(
        getValue: () => attributes.individualBedroomsCount.v,
        setValue: (int? v) {
          this.setState(() {
            attributes.individualBedroomsCount = Some(v);
            recalculateTotalBedrooms(attributes);
          });
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_doubleBedroomsCount]?.buildEntry(
        getValue: () => attributes.doubleBedroomsCount.v,
        setValue: (int? v) {
          this.setState(() {
            attributes.doubleBedroomsCount = Some(v);
            recalculateTotalBedrooms(attributes);
          });
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_suiteBedroomsCount]?.buildEntry(
        getValue: () => attributes.suiteBedroomsCount.v,
        setValue: (int? v) {
          this.setState(() {
            attributes.suiteBedroomsCount = Some(v);
            recalculateTotalBedrooms(attributes);
          });
        },
      ),
      if (fieldsDefinitions[FielddefsSrv.c_prop_attrs_totalBedroomsCount] != null)
        if (totalBedroomsIsComputed(fieldsDefinitions))
          fieldsDefinitions[FielddefsSrv.c_prop_attrs_totalBedroomsCount]!
              .copyWith(readOnly: true)
              .buildEntry(getValue: () => attributes.totalBedroomsCount.v, setValue: (_) {})
        else
          fieldsDefinitions[FielddefsSrv.c_prop_attrs_totalBedroomsCount]?.buildEntry(
            getValue: () => attributes.totalBedroomsCount.v,
            setValue: (int? v) {
              this.setState(() => attributes.suiteBedroomsCount = Some(v));
            },
          ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_bathroomsCount]?.buildEntry(
        getValue: () => attributes.bathroomsCount.v,
        setValue: (int? v) {
          this.setState(() => attributes.bathroomsCount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_toiletsCount]?.buildEntry(
        getValue: () => attributes.toiletsCount.vn,
        setValue: (int? v) {
          this.setState(() => attributes.toiletsCount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_builtInCabinetsCount]?.buildEntry(
        getValue: () => attributes.builtInCabinetsCount.vn,
        setValue: (int? v) {
          this.setState(() => attributes.builtInCabinetsCount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_buddleHas]?.buildEntry(
        getValue: () => attributes.buddleHas.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.buddleHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_dinningRoomHas]?.buildEntry(
        getValue: () => attributes.dinningRoomHas.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.dinningRoomHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_storageRoomHas]?.buildEntry(
        getValue: () => attributes.storageRoomHas.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.storageRoomHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_balconyHas]?.buildEntry(
        getValue: () => attributes.balconyHas.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.balconyHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_terraceHas]?.buildEntry(
        getValue: () => attributes.terraceHas.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.terraceHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_externalJoinery]?.buildEntry(
        getValue: () => attributes.externalJoineryCode.vn,
        setValue: (String? v) {
          this.setState(() => attributes.externalJoineryCode = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_ground]?.buildEntry(
        getValue: () => attributes.groundCodes.v,
        setValue: (List<String>? v) {
          this.setState(() => attributes.groundCodes = Some(v ?? <String>[]));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_waterSupplyHas]?.buildEntry<bool>(
        getValue: () => attributes.waterSupplyHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.waterSupplyHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_powerSupplyHas]?.buildEntry<bool>(
        getValue: () => attributes.powerSupplyHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.powerSupplyHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_gasSupplyHas]?.buildEntry<bool>(
        getValue: () => attributes.gasSupplyHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.gasSupplyHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_airConditioning]?.buildEntry<String>(
        getValue: () => attributes.airConditioningCode.v,
        setValue: (String? v) {
          this.setState(() => attributes.airConditioningCode = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_heating]?.buildEntry<String>(
        getValue: () => attributes.heatingCode.v,
        setValue: (String? v) {
          this.setState(() => attributes.heatingCode = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_fireplaceHas]?.buildEntry<bool>(
        getValue: () => attributes.fireplaceHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.fireplaceHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_intercomHas]?.buildEntry<bool>(
        getValue: () => attributes.intercomHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.intercomHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_reinforcedDoorHas]?.buildEntry<bool>(
        getValue: () => attributes.reinforcedDoorHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.reinforcedDoorHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_alarmSystemHas]?.buildEntry<bool>(
        getValue: () => attributes.alarmSystemHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.alarmSystemHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_elevatorHas]?.buildEntry<bool>(
        getValue: () => attributes.elevatorHas.v,
        setValue: (bool? v) {
          this.setState(() => attributes.elevatorHas = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_handicappedAccessibleIs]?.buildEntry<bool>(
        getValue: () => attributes.handicappedAccessibleIs.v,
        setValue: (bool? v) {
          this.setState(() => attributes.handicappedAccessibleIs = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_furnishedIs]?.buildEntry<bool>(
        getValue: () => attributes.furnishedIs.v,
        setValue: (bool? v) {
          this.setState(() => attributes.furnishedIs = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_garden]?.buildEntry<String>(
        getValue: () => attributes.gardenCode.v,
        setValue: (String? v) {
          this.setState(() => attributes.gardenCode = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_outsideArea]?.buildEntry(
        getValue: () => attributes.outsideAreaCode.v,
        setValue: (String? v) {
          this.setState(() => attributes.outsideAreaCode = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_swimmingPool]?.buildEntry(
        getValue: () => attributes.swimmingPoolCode.vn,
        setValue: (String? v) {
          this.setState(() => attributes.swimmingPoolCode = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_parkingPlacesCount]?.buildEntry(
        getValue: () => attributes.parkingPlacesCount.vn,
        setValue: (int? v) {
          this.setState(() => attributes.parkingPlacesCount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_optionalParkingIs]?.buildEntry(
        getValue: () => attributes.optionalParkingIs.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.optionalParkingIs = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_facade]?.buildEntry(
        getValue: () => attributes.facadeCodes.vn,
        setValue: (List<String>? v) {
          this.setState(() => attributes.facadeCodes = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_orientation]?.buildEntry(
        getValue: () => attributes.orientationCodes.vn,
        setValue: (List<String>? v) {
          this.setState(() => attributes.orientationCodes = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_sunnyIs]?.buildEntry(
        getValue: () => attributes.sunnyIs.vn,
        setValue: (bool? v) {
          this.setState(() => attributes.sunnyIs = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_communityFeesAmount]?.buildEntry(
        getValue: () => attributes.communityFeesAmount.vn,
        setValue: (double? v) {
          this.setState(() => attributes.communityFeesAmount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_neighborsPerFloorCount]?.buildEntry(
        getValue: () => attributes.neighborsPerFloorCount.vn,
        setValue: (int? v) {
          this.setState(() => attributes.neighborsPerFloorCount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_buildingFloorsCount]?.buildEntry(
        getValue: () => attributes.buildingFloorsCount.vn,
        setValue: (int? v) {
          this.setState(() => attributes.buildingFloorsCount = Some(v));
        },
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_floor]?.buildEntry(
        getValue: () => attributes.floorCode.vn,
        setValue: (String? v) {
          this.setState(() => attributes.floorCode = Some(v));
        },
        isRequired: qualityInfoIsRequired,
      ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_energyCertificate]?.buildEntry(
        getValue: () => attributes.energyCertificateCode.vn,
        setValue: (String? v) {
          this.setState(() => attributes.energyCertificateCode = Some(v));
        },
        isRequired: qualityInfoIsRequired,
      ),
      if (attributes.energyCertificateCode.vn == "available")
        fieldsDefinitions[FielddefsSrv.c_prop_attrs_consumptionLevel]?.buildEntry(
          getValue: () => attributes.consumptionLevelCode.vn,
          setValue: (String? v) {
            this.setState(() => attributes.consumptionLevelCode = Some(v));
          },
          isRequired: qualityInfoIsRequired,
        ),
      fieldsDefinitions[FielddefsSrv.c_prop_attrs_emissionLevel]?.buildEntry(
        getValue: () => attributes.emissionLevelCode.vn,
        setValue: (String? v) {
          this.setState(() => attributes.emissionLevelCode = Some(v));
        },
      ),
    ].where((entry) => entry != null).map((FormEntry? x) => x as FormEntry);
  }

  void recalculateTotalBedrooms(PropertyAttributesDto attributes) {
    final totalBedrooms = (attributes.individualBedroomsCount.vn ?? 0) +
        (attributes.doubleBedroomsCount.vn ?? 0) +
        (attributes.suiteBedroomsCount.vn ?? 0);
    attributes.totalBedroomsCount = Some(totalBedrooms);
  }

  bool totalBedroomsIsComputed(Map<String, FieldDefDto> fieldsDefinitions) =>
      (fieldsDefinitions[FielddefsSrv.c_prop_attrs_individualBedroomsCount] != null ||
          fieldsDefinitions[FielddefsSrv.c_prop_attrs_doubleBedroomsCount] != null ||
          fieldsDefinitions[FielddefsSrv.c_prop_attrs_suiteBedroomsCount] != null);

  List<SelectOption<String>> typesToSelectOptions(List<PropertytypeDto> propertyTypes) {
    return propertyTypes.map((t) => SelectOption<String>(value: t.code.v, label: t.label.v.localized)).toList();
  }

  List<SelectOption<String>> subtypesToSelectOptions(List<PropertytypeDto> propertyTypes, String? typeCode) {
    if (typeCode == null)
      return [];
    else {
      final propertyType = fromCode(propertyTypes, typeCode);
      return propertyType.vn?.subtypes.vn
              ?.map((t) => SelectOption<String>(
                    value: t.code.v,
                    label: t.label.v.localized,
                  ))
              .toList() ??
          [];
    }
  }

  Optional<PropertytypeDto> fromCode(List<PropertytypeDto> propertyTypes, String code) {
    return propertyTypes.oFirstWhere((pt) => pt.code.vn == code);
  }
}
