import 'dart:collection';

import 'package:topbrokers/common/widgets/gravatar.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:flutter/material.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class OfferListItemAvatar extends StatelessWidget {
  OfferListItemAvatar({Key? key, required this.offer, this.showStatus = true, this.showMatchings = true}) : super(key: key);

  final OfferDto offer;
  final bool showStatus;
  final bool showMatchings;

  final Map<OfferstatusCode, Color> _statusBgColors = HashMap.fromEntries([
    MapEntry(OfferstatusCode.news, Colors.green[500] ?? Colors.green),
    MapEntry(OfferstatusCode.draft, Colors.orange[500] ?? Colors.orange),
    MapEntry(OfferstatusCode.commercialization, Colors.indigo[400] ?? Colors.indigo),
    MapEntry(OfferstatusCode.historic, Colors.grey[500] ?? Colors.grey),
  ]);

  @override
  Widget build(BuildContext context) {
    final alertNum = offer.matchingsinfo.vn?.notReviewedCount.vn ?? 0;
    final alertText = alertNum > 0 ? "$alertNum" : "";

    final pictureUri = getFavouritePictureUrl(offer);
    final statusColor = _statusBgColors[offer.status.vn?.code.vn ?? OfferstatusCode.historic];
    if (pictureUri != null) {
      return Gravatar(
        size: 40,
        ledColor: statusColor,
        led: showStatus ? Icons.circle : null,
        ledSize: 10,
        alertText: showMatchings ? alertText : "",
        //alertColor: Colors.green,
        childUrl: pictureUri,
      );
    } else {
      return Gravatar(
        size: 40,
        ledColor: statusColor,
        led: showStatus ? Icons.circle : null,
        ledSize: 10,
        alertText: showMatchings ? alertText : "",
        //alertColor: Colors.green,
        child: Icon(CustomAppIcons.offer),
      );
    }
  }

  String? getFavouritePictureUrl(OfferDto offer) {
    final favouritePicture = offer.property.vn?.favouritePicture.vn;
    return favouritePicture?.thumbnail.vn?.url.vn; // ?? favouritePicture?.original?.vn?.url?.vn;
  }
}
