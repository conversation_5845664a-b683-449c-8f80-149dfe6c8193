import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class OfferUtils {
  static OfferDto emptyOffer() {
    return OfferDto(
      customer: Some(null),
      status: Some(
        OfferstatusDto(code: Some(OfferstatusCode.draft)),
      ),
      sale: Some(OfferSaleDto(
        allowed: Some(true),
        amount: Some(null),
      )),
      rent: Some(OfferRentDto(
        allowed: Some(false),
        amount: Some(null),
      )),
      currency: Some(CurrencyDto(
        code: Some("EUR"),
        symbol: Some("€"),
      )),
      property: Some(
        PropertyDto(
          id: None(),
          type: Some(PropertytypeDto(
            code: Some("flat"),
            label: None(),
          )),
          subtype: Some(null),
          address: Some(PropertyAddressDto(
            postcode: Some(null),
            city: None(),
            streettype: Some(null),
            streetname: Some(null),
            number: Some(null),
            detail: Some(null),
          )),
          attributes: Some(PropertyAttributesDto.initWithNulls()),
          cadastralReference: Some(null),
        ),
      ),
    );
  }
}
