import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'fav_cloudoffers_list_event.dart';
part 'fav_cloudoffers_list_state.dart';

class FavCloudoffersListBloc extends Bloc<FavCloudoffersListEvent, FavCloudoffersListState> {
  static const _PAGE_SIZE = 30;

  final AppModelsNS appModels;

  /// Cuando deseamos la oportunidades de un agente
  CloudoffersListFilter filter;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;

  FavCloudoffersListBloc({
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
    required this.filter,
  }) : super(FavCloudoffersListState.create(filter: filter)) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelCreatedState<OfferDto> || state is ModelsChannelUpdatedState<OfferDto>) {
        this.add(FavCloudoffersListOnRefresh());
      } else if (state is ModelsChannelOnAddedOfferToMyFavouritesState ||
          state is ModelsChannelOnRemovedOfferFromMyFavouritesState) {
        this.add(FavCloudoffersListOnRefresh());
      }
    });
  }

  factory FavCloudoffersListBloc.create(
    BuildContext context, {
    CloudoffersListFilter filter = const CloudoffersListFilter(includeFavourites: True, includeNotFavourites: False),
  }) {
    return FavCloudoffersListBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      filter: filter,
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    await _modelsChannelSubscription.cancel();
    return super.close();
  }

  @override
  Stream<Transition<FavCloudoffersListEvent, FavCloudoffersListState>> transformEvents(
    Stream<FavCloudoffersListEvent> events,
    TransitionFunction<FavCloudoffersListEvent, FavCloudoffersListState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<FavCloudoffersListState> mapEventToState(FavCloudoffersListEvent event) async* {
    if (event is FavCloudoffersListOnFetch) {
      yield* _mapOnFetch(state, event);
    } else if (event is FavCloudoffersListOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is FavCloudoffersListOnOfferChanged) {
      yield* _mapOnOfferChanged(state, event);
    } else if (event is FavCloudoffersListOnFilterChanged) {
      yield* _mapOnFilterChanged(state, event);
    } else if (event is FavCloudoffersListOnOrderByChanged) {
      yield* _mapOnOrderByChanged(state, event);
    }
  }

  Stream<FavCloudoffersListState> _mapOnFilterChanged(
      FavCloudoffersListState state, FavCloudoffersListOnFilterChanged event) async* {
    try {
      final newFilter = event.filter;
      yield state.copyWith(status: FavCloudoffersListStatus.initial);
      final offers = await _listOffers(filter: newFilter, orderBy: state.orderBy);
      yield state.copyWith(
        status: FavCloudoffersListStatus.success,
        filter: newFilter,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: FavCloudoffersListStatus.failure);
    }
  }

  Stream<FavCloudoffersListState> _mapOnOrderByChanged(
      FavCloudoffersListState state, FavCloudoffersListOnOrderByChanged event) async* {
    try {
      final newOrderBy = event.orderBy;
      yield state.copyWith(status: FavCloudoffersListStatus.initial);
      final offers = await _listOffers(filter: state.filter, orderBy: newOrderBy);
      yield state.copyWith(
        status: FavCloudoffersListStatus.success,
        orderBy: newOrderBy,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: FavCloudoffersListStatus.failure);
    }
  }

  Stream<FavCloudoffersListState> _mapOnFetch(FavCloudoffersListState state, FavCloudoffersListOnFetch event) async* {
    if (state.hasReachedMax) yield state;
    try {
      if (state.status == FavCloudoffersListStatus.initial) {
        final offers = await _listOffers(filter: state.filter, orderBy: state.orderBy);
        yield state.copyWith(
            status: FavCloudoffersListStatus.success, offers: offers, hasReachedMax: offers.length != _PAGE_SIZE);
      } else {
        // Este punto sirve tanto en estado success como failure si se reintentase
        final offers = await _listOffers(filter: state.filter, orderBy: state.orderBy, offset: state.offers.length);
        yield offers.isEmpty
            ? state.copyWith(hasReachedMax: true)
            : state.copyWith(
                status: FavCloudoffersListStatus.success,
                offers: List.of(state.offers)..addAll(offers),
                hasReachedMax: false,
              );
      }
    } on Exception {
      yield state.copyWith(status: FavCloudoffersListStatus.failure);
    }
  }

  Stream<FavCloudoffersListState> _mapOnRefresh(
      FavCloudoffersListState state, FavCloudoffersListOnRefresh event) async* {
    try {
      yield state.copyWith(status: FavCloudoffersListStatus.initial);
      final offers = await _listOffers(filter: state.filter, orderBy: state.orderBy);
      yield state.copyWith(
          status: FavCloudoffersListStatus.success, offers: offers, hasReachedMax: offers.length != _PAGE_SIZE);
    } on Exception {
      yield state.copyWith(status: FavCloudoffersListStatus.failure);
    }
  }

  ///
  /// Parcheamos la oportunidad asociada a la oferta sin necesidad de tener que recargar la lista entera
  /// Debido a que el orden de la lista puede cambiar, de momento no lo usamos (preferimos refrescar lista entera)
  Stream<FavCloudoffersListState> _mapOnOfferChanged(
      FavCloudoffersListState state, FavCloudoffersListOnOfferChanged event) async* {
    try {
      final offerId = Some(event.offerId);
      final offerIndex = state.offers.indexWhere((o) => o.id == offerId);
      if (offerIndex != -1) {
        final offer = await appModels.readOffer(offerId.v);
        if (offer != null) {
          yield state.copyWith(
            offers: state.offers.map((o) => o.id == offerId ? offer : o).toList(),
          );
        }
      }
    } on Exception {
      yield state.copyWith(status: FavCloudoffersListStatus.failure);
    }
  }

  Future<List<OfferDto>> _listOffers({
    required CloudoffersListFilter filter,
    required CloudoffersListOrderBy orderBy,
    int offset = 0,
  }) {
    return appModels.listCloudOffers(filter: filter, offset: offset, orderBy: orderBy, limit: _PAGE_SIZE);
  }
}
