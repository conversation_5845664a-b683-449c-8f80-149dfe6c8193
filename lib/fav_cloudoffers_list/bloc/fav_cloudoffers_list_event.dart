part of 'fav_cloudoffers_list_bloc.dart';

int _id = 0;

enum CloudoffersListExportCode {
  phonenumbers,
  basicinfo
}
abstract class FavCloudoffersListEvent extends Equatable {
  const FavCloudoffersListEvent();

  @override
  List<Object> get props => [];
}

class FavCloudoffersListOnOfferChanged extends FavCloudoffersListEvent {
  final String offerId;

  const FavCloudoffersListOnOfferChanged({required this.offerId});
  @override
  List<Object> get props => [offerId];
}

class FavCloudoffersListOnRefresh extends FavCloudoffersListEvent {}


class FavCloudoffersListOnFetch extends FavCloudoffersListEvent {
  final _myid = ++_id;

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}

class FavCloudoffersListOnFilterChanged extends FavCloudoffersListEvent {
  final CloudoffersListFilter filter;
  const FavCloudoffersListOnFilterChanged(this.filter);
  @override
  List<Object> get props => [filter];

  @override
  String toString() => "FavCloudoffersListOnFilterChanged { new filter: $filter }";
}

class FavCloudoffersListOnOrderByChanged extends FavCloudoffersListEvent {
  final CloudoffersListOrderBy orderBy;
  const FavCloudoffersListOnOrderByChanged(this.orderBy);
  @override
  List<Object> get props => [orderBy];

  @override
  String toString() => "FavCloudoffersListOnOrderByChanged { new orderBy: $orderBy }";
}
