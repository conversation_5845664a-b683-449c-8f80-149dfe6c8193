import 'package:agentor_repositoryns/services/services.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/fav_cloudoffers_list/fav_cloudoffers.dart';

class CloudofferListOrderButton extends StatelessWidget {
  final bool visible;

  CloudofferListOrderButton({
    required this.visible,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FavCloudoffersListBloc, FavCloudoffersListState>(builder: (context, state) {
      final button = _Button(
        onSelected: (CloudoffersListOrderBy value) {
          BlocProvider.of<FavCloudoffersListBloc>(context).add(FavCloudoffersListOnOrderByChanged(value) );
        },
        selectedOrder: state.orderBy,
        //activeStyle: activeStyle,
        //defaultStyle: defaultStyle,
      );
      return AnimatedOpacity(
        opacity: visible ? 1.0 : 0.0,
        duration: Duration(milliseconds: 150),
        child: visible ? button : IgnorePointer(child: button),
      );
    });
  }
}

class _Button extends StatelessWidget {
  const _Button({
    Key? key,
    required this.onSelected,
    required this.selectedOrder,
  }) : super(key: key);

  final PopupMenuItemSelected<CloudoffersListOrderBy> onSelected;
  final CloudoffersListOrderBy selectedOrder;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<CloudoffersListOrderBy>(
      padding: const EdgeInsets.all(0.0), 
      key: AgentorKeys.cloudoffersFilterButton,
      icon: Icon(Icons.filter_list),
      tooltip: "Ordenar",
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<CloudoffersListOrderBy>(
          //key: AgentorKeys.allFilter,
          value: CloudoffersListOrderBy.amount_desc,
          child: _buildMenuItemWidget(
            "Precio más alto",
            selectedOrder==CloudoffersListOrderBy.amount_desc,
          ),
        ),
        PopupMenuItem<CloudoffersListOrderBy>(
          //key: AgentorKeys.allFilter,
          value: CloudoffersListOrderBy.amount_asc,
          child: _buildMenuItemWidget(
            "Precio más bajo",
            selectedOrder==CloudoffersListOrderBy.amount_asc,
          ),
        ),
        PopupMenuItem<CloudoffersListOrderBy>(
          //key: AgentorKeys.allFilter,
          value: CloudoffersListOrderBy.created_at_desc,
          child: _buildMenuItemWidget(
            "Recientes",
            selectedOrder==CloudoffersListOrderBy.created_at_desc,
          ),
        )
      ],
    );
  }

  static Widget _buildMenuItemWidget(String text, bool active) {
    return Row(
      children: [
        if (active)
          Icon(Icons.radio_button_on_outlined, color: Colors.grey)
        else
          Icon(Icons.radio_button_off_outlined, color: Colors.grey),
        Text(text)
      ],
    );
  }
}
