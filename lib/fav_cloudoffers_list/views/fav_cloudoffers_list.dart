import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/cloudoffers_list/cloudoffers.dart';
import 'package:topbrokers/fav_cloudoffers_list/fav_cloudoffers.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/routes.dart';

///
///  Muestra la lista de ofertas actualmente "activas", para ello consume los estados generados FavCloudoffersListBloc
///  que DEBE EXISTIR antes de crear este widget.
///  Existe un bloque "global" creado por AgentorApp (es un bloque "raíz")
///
class FavCloudoffersList extends StatefulWidget {
  @override
  _FavCloudoffersListState createState() => _FavCloudoffersListState();
}

class _FavCloudoffersListState extends State<FavCloudoffersList> {
  final _scrollController = ScrollController();
  late FavCloudoffersListBloc _offersBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _offersBloc = context.bloc<FavCloudoffersListBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    return BlocConsumer<FavCloudoffersListBloc, FavCloudoffersListState>(
      listener: (context, state) {
        if (!state.hasReachedMax && _isBottom) {
          _offersBloc.add(FavCloudoffersListOnFetch());
        }
      },
      builder: (context, state) {
        switch (state.status) {
          case FavCloudoffersListStatus.failure:
            return Center(
              child: Text(localization.offers_errLoading),
            );
          case FavCloudoffersListStatus.success:
            return state.offers.isEmpty
                ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          localization.offers_noOffers,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: () async {
                      _offersBloc.add(FavCloudoffersListOnRefresh());
                    },
                    child: ListView.builder(
                      key: AgentorKeys.offersList,
                      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                      itemBuilder: (BuildContext context, int index) {
                        if (index >= state.offers.length) {
                          return BottomLoader();
                        } else {
                          return CloudofferListItem(
                            offer: state.offers[index],
                            isSelected: state.selectedOfferId == state.offers[index].id.vn,
                            // Solo usamos este widget para mostrar ofertas del cloud: desactivamos el status (siempre son noticia) y los matchings (ya que muestra número de matchings de cualquier agente... no solo míos)
                            showStatus: false,
                            showMatchings: false,
                            onTap: () {
                              final offer = state.offers[index];
                              Navigator.pushNamed(
                                context,
                                AgentorRoutes.showOffer,
                                arguments: offer.id.v,
                              );
                            },
                          );
                        }
                      },
                      itemCount: state.hasReachedMax ? state.offers.length : state.offers.length + 1,
                      controller: _scrollController,
                    ),
                  );
          default:
            return const Center(
              child: CircularProgressIndicator(),
            );
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _offersBloc.add(FavCloudoffersListOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}