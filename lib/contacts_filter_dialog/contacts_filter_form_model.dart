import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class ContactsFilterFormModel {
  /** Búsqueda libre */
  Optional<String> search;
  /** Ofertante de una propiedad */
  Optional<bool> isOfferCustomer;
  /** Es un bank servicer */ 
  Optional<bool> isBankServicer;
  /** Tiene un site asociado */
  Optional<bool> hasSite;
  
  ContactsFilterFormModel({
    this.search = const None(),
    this.isOfferCustomer = const None(),
    this.isBankServicer = const None(),
    this.hasSite = const None()
  });
}
