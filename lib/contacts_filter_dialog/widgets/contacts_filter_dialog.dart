import 'dart:math';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/contacts_filter_dialog/widgets/widgets.dart';


Future<ContactsListFilter?> showContactsFilterDialog(
  context, {
  ContactsListFilter filter = const ContactsListFilter(),
}) {
  final width = MediaQuery.of(context).size.width;
  
  return showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    pageBuilder: (
      BuildContext context,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      return ChangeNotifierProvider<_ChangeContactsFilterModel>(
        create: (_) => _ChangeContactsFilterModel(),
        child: Consumer<_ChangeContactsFilterModel>(builder: (context, filterModel, child) {
          return AlertDialog(
            insetPadding: EdgeInsets.all(5.0),
            actionsAlignment: MainAxisAlignment.spaceEvenly,
            contentPadding: const EdgeInsets.fromLTRB(15, 15, 15, 1),
            content: SingleChildScrollView(
              child: Container(
                width: min(492, width),
                // Ojo a esto: Fijamos una altgura en pixels en la que quepa todo
                // el forumario.
                // Si el valor es demasiado pequeño:
                //    - En web para escritorio no hay problema (internamente "crece" hasta poder mostrar todo el contenido).
                //    - En web para smartphone (ios/android web) el contenido "excesivo" no es alcanzable por scroll (Se puede "tirar" del sinlechildscrollview con el dedo y ver... pero al soltar se vuelve a ir fuera de la vista )
                height: 320,
                child: Padding(
                  padding: EdgeInsets.all(5.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: ContactsFilterForm(
                          actualFilter: filter,
                          onChanged: (changedFilter) {
                            filter = changedFilter;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              TextButton.icon(
                icon: Icon(Icons.check),
                label: Text("Aceptar"),
                onPressed: () {
                  Navigator.pop(context, filter);
                },
              ),
              TextButton(
                child: Text("Cancelar"),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          );
        }),
      );
    },
  );
}

class _ChangeContactsFilterModel extends ChangeNotifier {
  late ContactsListFilter filter;

  _ChangeContactsFilterModel() : super();

  void changeFilter(ContactsListFilter newFilter) {
    this.filter = newFilter;
    notifyListeners();
  }
}
