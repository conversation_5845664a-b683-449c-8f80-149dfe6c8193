import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/offer_edit/widgets/city_list_item.dart';
import 'package:topbrokers/contacts_filter_dialog/contacts_filter.dart';

import '../../app_constants.dart';
import '../../propertyzoneslist/selectPropertyzoneDialog.dart';

class ContactsFilterForm extends StatefulWidget {
  final ContactsListFilter actualFilter;
  //final void Function(OpportunitiesListFilter changedFilter) onAccept;
  final void Function(ContactsListFilter changedFilter) onChanged;

  ContactsFilterForm({
    Key? key,
    required this.actualFilter,
    required this.onChanged,
  }) : super(key: key ?? AgentorKeys.contactsFilterPage);

  @override
  _ContactsFilterFormState createState() => _ContactsFilterFormState();
}

class _ContactsFilterFormState extends State<ContactsFilterForm> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  //ContactsFilterFormModel _formData;

  _ContactsFilterFormState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));
    late ContactsFilterFormBloc _formBloc;

    return BlocProvider<ContactsFilterFormBloc>(
      create: (context) {
        _formBloc = ContactsFilterFormBloc.create(context)
          ..add(ContactsFilterFormOnLoadEvent(filterModel: widget.actualFilter));
        return _formBloc;
      },
      child: BlocConsumer<ContactsFilterFormBloc, ContactsFilterFormState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          if (state is ContactsFilterFormFailureState) {
            return Center(
              child: Text(localizations.contactsfilter_errLoading),
            );
          } else if (state is ContactsFilterFormLoadedState) {
            //this._formData = state.formData;
            return SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  _buildForm(context, state.imIndividual, state.formData, () {
                    widget.onChanged(_formDataToListFilter(state.formData, state.originalFilter));
                  }),
                ],
              ),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(
      BuildContext context, bool imIndividual, ContactsFilterFormModel formData, void Function() onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [_formEntries(context, imIndividual, formData, onChanged).buildForm(key: _formKey, context: context)],
    );
  }

  ContactsListFilter _formDataToListFilter(
    ContactsFilterFormModel formData,
    ContactsListFilter originalFilter,
  ) =>
      originalFilter.copyWith(
        hasSite: formData.hasSite,
        isOfferCustomer: formData.isOfferCustomer,
        isBankServicer: formData.isBankServicer,
        search: formData.search,
      );

  List<Widget> _formEntries(
    BuildContext context,
    bool isIndividual,
    ContactsFilterFormModel formData,
    Function() onChanged,
  ) {
    const c_any = "any";
    const c_yes = "yes";
    const c_not = "not";

    final localizations = context.getAppLocalizationsOrThrow();

    return [
      SimpleFieldEntry<String>(
        label: localizations.contactsfilter_searchLabel,
        isMultiline: false,
        maxLength: 40,
        getValue: () {
          if (formData.search.vn != null)
            return formData.search.v;
          else
            return "";
        },
        setValue: (String? search) {
          setState(() {
            if (search == null || search.length == 0)
              formData.search = None();
            else
              formData.search = Some(search);
          });
          onChanged();
        },
      ),
      SelectFieldEntry<String>(
        label: localizations.contactsfilter_hasSiteLabel,
        getValue: () {
          switch (formData.hasSite.vn) {
            case true:
              return c_yes;
            case false:
              return c_not;
            default:
              return c_any;
          }
        },
        setValue: (String? code) {
          setState(() {
            formData.hasSite = (code == c_yes)
                ? True
                : (code == c_not)
                    ? False
                    : None();
          });
          onChanged();
        },
        options: [
          SelectOption(label: "", value: c_any),
          SelectOption(label: localizations.common_YesLabel, value: c_yes),
          SelectOption(label: localizations.common_NotLabel, value: c_not),
        ],
      ),
      SelectFieldEntry<String>(
        label: localizations.contactsfilter_isOfferCustomerLabel,
        getValue: () {
          switch (formData.isOfferCustomer.vn) {
            case true:
              return c_yes;
            case false:
              return c_not;
            default:
              return c_any;
          }
        },
        setValue: (String? code) {
          setState(() {
            formData.isOfferCustomer = (code == c_yes)
                ? True
                : (code == c_not)
                    ? False
                    : None();
          });
          onChanged();
        },
        options: [
          SelectOption(label: "", value: c_any),
          SelectOption(label: localizations.common_YesLabel, value: c_yes),
          SelectOption(label: localizations.common_NotLabel, value: c_not),
        ],
      ),
      if (!isIndividual)
        SelectFieldEntry<String>(
          label: localizations.contactsfilter_isBankServicerLabel,
          getValue: () {
            switch (formData.isBankServicer.vn) {
              case true:
                return c_yes;
              case false:
                return c_not;
              default:
                return c_any;
            }
          },
          setValue: (String? code) {
            setState(() {
              formData.isBankServicer = (code == c_yes)
                  ? True
                  : (code == c_not)
                      ? False
                      : None();
            });
            onChanged();
          },
          options: [
            SelectOption(label: "", value: c_any),
            SelectOption(label: localizations.common_YesLabel, value: c_yes),
            SelectOption(label: localizations.common_NotLabel, value: c_not),
          ],
        ),
    ];
  }
}
