part of 'contacts_filter_form_bloc.dart';

abstract class ContactsFilterFormState extends Equatable {
  final bool imIndividual;
  final ContactsListFilter originalFilter;

  ContactsFilterFormState({this.originalFilter = const ContactsListFilter(), this.imIndividual = false}) : super();
  @override
  List<Object> get props => [];
}

class ContactsFilterFormLoadedState extends ContactsFilterFormState {
  final ContactsFilterFormModel formData;
  ContactsFilterFormLoadedState({
    required this.formData,
    required ContactsListFilter originalFilter,
    required bool imIndividual,
  }) : super(originalFilter: originalFilter, imIndividual: imIndividual);

  ContactsFilterFormLoadedState copyWith({ContactsFilterFormModel? formData}) {
    return ContactsFilterFormLoadedState(
        formData: formData ?? this.formData, originalFilter: this.originalFilter, imIndividual: this.imIndividual);
  }

  @override
  List<Object> get props => [formData];
}

class ContactsFilterFormFailureState extends ContactsFilterFormState {
  final String error;
  ContactsFilterFormFailureState({
    required this.error,
    required ContactsListFilter originalFilter,
    required bool imIndividual,
  }) : super(originalFilter: originalFilter, imIndividual: imIndividual);
  @override
  List<Object> get props => [error];
}

class ContactsListFilterInitState extends ContactsFilterFormState {}
