part of 'contacts_filter_form_bloc.dart';

int _id = 0;

abstract class ContactsFilterFormEvent extends Equatable {
  const ContactsFilterFormEvent();

  @override
  List<Object> get props => [];
}

class ContactsFilterFormOnLoadEvent extends ContactsFilterFormEvent {
  final _myid = ++_id;

  final ContactsListFilter filterModel;

  ContactsFilterFormOnLoadEvent({required this.filterModel}) : super();

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}
