import 'dart:async';

//import 'package:topbrokers/cloudcontacts_export/contacts_filter.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/global/session_bloc.dart';

import 'package:topbrokers/contacts_filter_dialog/contacts_filter.dart';

part 'contacts_filter_form_event.dart';
part 'contacts_filter_form_state.dart';

class ContactsFilterFormBloc extends Bloc<ContactsFilterFormEvent, ContactsFilterFormState> {
  final AppModelsNS appModels;
  final SessionBloc globalBloc;

  ContactsFilterFormBloc({required this.appModels, required this.globalBloc}) : super(ContactsListFilterInitState());

  factory ContactsFilterFormBloc.create(BuildContext context) {
    return ContactsFilterFormBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      globalBloc: BlocProvider.of<SessionBloc>(context, listen: false),
    );
  }

  @override
  Stream<Transition<ContactsFilterFormEvent, ContactsFilterFormState>> transformEvents(
    Stream<ContactsFilterFormEvent> events,
    TransitionFunction<ContactsFilterFormEvent, ContactsFilterFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ContactsFilterFormState> mapEventToState(ContactsFilterFormEvent event) async* {
    if (event is ContactsFilterFormOnLoadEvent) {
      yield* _mapOnLoad(state, event);
    }
  }

  Stream<ContactsFilterFormState> _mapOnLoad(ContactsFilterFormState state, ContactsFilterFormOnLoadEvent event) async* {
    try {
      final filterModel = event.filterModel;
      yield ContactsFilterFormLoadedState(
        originalFilter: filterModel,
        imIndividual: globalBloc.state.agent.vn?.isIndividual == True,
        formData: ContactsFilterFormModel(
          hasSite: filterModel.hasSite,
          search: filterModel.search,
          isOfferCustomer: filterModel.isOfferCustomer,
          isBankServicer: filterModel.isBankServicer,
        ),
      );
    } on Exception {
      yield ContactsFilterFormFailureState(originalFilter: event.filterModel, imIndividual: state.imIndividual, error: "Problemas al obtener datos");
    }
  }
}
