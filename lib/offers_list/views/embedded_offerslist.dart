import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/offers_list/widgets/offer_list_item.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:topbrokers/offers_list/offers.dart';

class EmbeddedOffersList extends StatefulWidget {
  final Optional<String> customerId;
  final Optional<bool> inlcudeHistoric;

  EmbeddedOffersList({
    this.customerId = const None(),
    this.inlcudeHistoric = True,
  }) : super();

  @override
  _EmbeddedOffersListState createState() => _EmbeddedOffersListState();
}

class _EmbeddedOffersListState extends State<EmbeddedOffersList> {
  final _scrollController = ScrollController();
  late OffersListBloc _offersListBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    return BlocProvider<OffersListBloc>(
      create: (context) {
        _offersListBloc = OffersListBloc.create(
          context,
          customerId: widget.customerId,
          filterOptions: widget.inlcudeHistoric.v ? OfferslistFilterOptions.all : OfferslistFilterOptions.notHistoric,
        )..add(OffersListOnFetch());
        return _offersListBloc;
      },
      child: BlocConsumer<OffersListBloc, OffersListState>(
        listener: (context, state) {
          if (!state.hasReachedMax && _isBottom) {
            _offersListBloc.add(OffersListOnFetch());
          }
        },
        builder: (context, state) {
          switch (state.status) {
            case OffersListStatus.failure:
              return Center(
                child: Text(localization.offers_errLoading),
              );
            case OffersListStatus.success:
              return ConstrainedBox(
                constraints: BoxConstraints(maxHeight: 160, minHeight: 0),
                child: state.offers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              localization.offers_noOffers,
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: () async {
                          _offersListBloc.add(OffersListOnRefresh());
                        },
                        child: ListView.builder(
                          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                          itemBuilder: (BuildContext context, int index) {
                            if (index >= state.offers.length) {
                              return BottomLoader();
                            } else {
                              return OfferListItem(
                                offer: state.offers[index],
                                isSelected: state.selectedOfferId == state.offers[index].id.vn,
                                showCustomer: widget.customerId.vn == null,
                                onTap: () {
                                  final offer = state.offers[index];

                                  Navigator.pushNamed(
                                    context,
                                    AgentorRoutes.showOffer,
                                    arguments: offer.id.v,
                                  );
                                },
                              );
                            }
                          },
                          itemCount: state.hasReachedMax ? state.offers.length : state.offers.length + 1,
                          controller: _scrollController,
                        ),
                      ),
              );

            default:
              return const Center(
                child: CircularProgressIndicator(),
              );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _offersListBloc.add(OffersListOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
