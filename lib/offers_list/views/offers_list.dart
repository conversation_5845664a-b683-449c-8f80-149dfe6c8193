import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/offers_list/offers.dart';
import 'package:topbrokers/routes.dart';

class OffersList extends StatefulWidget {
  @override
  _OffersListState createState() => _OffersListState();
}

class _OffersListState extends State<OffersList> {
  final _scrollController = ScrollController();
  late OffersListBloc _opportunitesBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _opportunitesBloc = context.bloc<OffersListBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    return BlocConsumer<OffersListBloc, OffersListState>(
      listener: (context, state) {
        if (!state.hasReachedMax && _isBottom) {
          _opportunitesBloc.add(OffersListOnFetch());
        }
      },
      builder: (context, state) {
        switch (state.status) {
          case OffersListStatus.failure:
            return Center(
              child: Text(localization.offers_errLoading),
            );
          case OffersListStatus.success:
            return state.offers.isEmpty
                ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          localization.offers_noOffers,
                          style: TextStyle(color: Colors.grey),
                        ),
                        Divider(height: 42),
                        ElevatedButton.icon(
                          icon: Icon(CustomAppIcons.offer, size: 16),
                          label: Text(localization.offers_addFirstOffer),
                          onPressed: () {
                            Navigator.pushNamed(context, AgentorRoutes.addOffer);
                          },
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    triggerMode: RefreshIndicatorTriggerMode.anywhere,
                    onRefresh: () async {
                      _opportunitesBloc.add(OffersListOnRefresh());
                    },
                    child: ListView.builder(
                      key: AgentorKeys.offersList,
                      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                      itemBuilder: (BuildContext context, int index) {
                        if (index >= state.offers.length) {
                          return BottomLoader();
                        } else {
                          return OfferListItem(
                            offer: state.offers[index],
                            isSelected: state.selectedOfferId == state.offers[index].id.vn,
                            onTap: () {
                              final offer = state.offers[index];
                                Navigator.pushNamed(
                                  context,
                                  AgentorRoutes.showOffer,
                                  arguments: offer.id.v,
                                );
                              
                            },
                          );
                        }
                      },
                      itemCount: state.hasReachedMax ? state.offers.length : state.offers.length + 1,
                      controller: _scrollController,
                    ),
                    //),
                  );
          default:
            return const Center(
              child: CircularProgressIndicator(),
            );
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _opportunitesBloc.add(OffersListOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
