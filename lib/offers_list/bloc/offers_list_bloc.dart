import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/common/set_helper.dart';
import 'package:topbrokers/offers_list/offers.dart';

part 'offers_list_event.dart';
part 'offers_list_state.dart';

class OffersListBloc extends Bloc<OffersListEvent, OffersListState> {
  static const _PAGE_SIZE = 30;

  final AppModelsNS appModels;

  /// Cuando deseamos las ofertas de un cliente
  final Optional<String> customerId;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;

  OffersListBloc({
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
    List<OfferslistFilterOption>? filterOptions,
    OffersListFilter filterX = const OffersListFilter(),
    this.customerId = const None(),
  }) : super(
          OffersListState.create(
            filterOptions: Set.from(filterOptions ?? OfferslistFilterOptions.workingOn),
            filter: filterX,
          ),
        ) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelCreatedState<OfferDto> || state is ModelsChannelUpdatedState<OfferDto>)
        this.add(OffersListOnRefresh());
      else if (state is ModelsChannelPropertymediaCreatedState)
        this.add(OffersListOnOfferChanged(offerId: state.offerId));
      else if (state is ModelsChannelPropertymediaUpdatedState)
        this.add(OffersListOnOfferChanged(offerId: state.offerId));
      else if (state is ModelsChannelPropertymediaDeletedState)
        this.add(OffersListOnOfferChanged(offerId: state.offerId));
    });
  }

  factory OffersListBloc.create(
    BuildContext context, {
    Optional<String> customerId = const None(),
    List<OfferslistFilterOption> filterOptions = OfferslistFilterOptions.workingOn,
  }) {
    return OffersListBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      customerId: customerId,
      filterOptions: filterOptions,
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    await _modelsChannelSubscription.cancel();
    return super.close();
  }

  @override
  Stream<Transition<OffersListEvent, OffersListState>> transformEvents(
    Stream<OffersListEvent> events,
    TransitionFunction<OffersListEvent, OffersListState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<OffersListState> mapEventToState(OffersListEvent event) async* {
    if (event is OffersListOnFetch) {
      yield* _mapOnFetched(state, event);
    } else if (event is OffersListOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is OffersListOnOfferChanged) {
      yield* _mapOnOfferChanged(state, event);
    } else if (event is OffersListOnItemSelected) {
      yield* _mapOnItemSelected(state, event);
    } else if (event is OffersListOnFilterOptionSelected) {
      yield* _mapOnFilterOptionSelected(state, event);
    } else if (event is OffersListOnFilterChanged) {
      yield* _mapOnFilterChanged(state, event);
    }
  }

  Stream<OffersListState> _mapOnFilterOptionSelected(
      OffersListState state, OffersListOnFilterOptionSelected event) async* {
    try {
      yield state.copyWith(status: OffersListStatus.initial);
      Set<OfferslistFilterOption> newFilterOptions = state.filterOptions.contains(event.filterOption)
          ? state.filterOptions.copyRemoving(event.filterOption)
          : state.filterOptions.copyAdding(event.filterOption);

      final offers = await _listOffers(filterOptions: newFilterOptions, filter: state.filter);
      yield state.copyWith(
        status: OffersListStatus.success,
        filterOptions: newFilterOptions,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception catch (e) {
      print("$e");
      yield state.copyWith(status: OffersListStatus.failure);
    }
  }

  Stream<OffersListState> _mapOnFilterChanged(
    OffersListState state,
    OffersListOnFilterChanged event,
  ) async* {
    try {
      yield state.copyWith(status: OffersListStatus.initial);
      final offers = await _listOffers(filterOptions: state.filterOptions, filter: event.filter);
      yield state.copyWith(
        status: OffersListStatus.success,
        filter: event.filter,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception catch (e) {
      print("$e");
      yield state.copyWith(status: OffersListStatus.failure);
    }
  }

  Stream<OffersListState> _mapOnItemSelected(OffersListState state, OffersListOnItemSelected event) async* {
    try {
      // No tenemos en cuenta los datos de la nueva oferta:  recargamos la lista
      yield state.copyWith(selectedOpportunityId: event.offer.id.vn ?? "");
    } on Exception {
      yield state.copyWith(status: OffersListStatus.failure);
    }
  }

  Stream<OffersListState> _mapOnFetched(OffersListState state, OffersListOnFetch event) async* {
    if (state.hasReachedMax) yield state;
    try {
      if (state.status == OffersListStatus.initial) {
        final offers = await _listOffers(filterOptions: state.filterOptions, filter: state.filter);
        yield state.copyWith(
          status: OffersListStatus.success,
          offers: offers,
          hasReachedMax: offers.length != _PAGE_SIZE,
        );
      } else {
        // Este punto sirve tanto en estado success como failure si se reintentase
        final offers = await _listOffers(
          filterOptions: state.filterOptions,
          filter: state.filter,
          offset: state.offers.length,
        );
        yield offers.isEmpty
            ? state.copyWith(
                hasReachedMax: true,
              )
            : state.copyWith(
                status: OffersListStatus.success,
                offers: List.of(state.offers)..addAll(offers),
                hasReachedMax: false,
              );
      }
    } on Exception {
      yield state.copyWith(status: OffersListStatus.failure);
    }
  }

  Stream<OffersListState> _mapOnRefresh(OffersListState state, OffersListOnRefresh event) async* {
    try {
      yield state.copyWith(status: OffersListStatus.initial);
      final offers = await _listOffers(filterOptions: state.filterOptions, filter: state.filter);
      yield state.copyWith(
        status: OffersListStatus.success,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: OffersListStatus.failure);
    }
  }

  ///
  /// Parcheamos la oportunidad asociada a la oferta sin necesidad de tener que recargar la lista entera
  /// Debido a que el orden de la lista puede cambiar, de momento no lo usamos (preferimos refrescar lista entera)
  Stream<OffersListState> _mapOnOfferChanged(OffersListState state, OffersListOnOfferChanged event) async* {
    try {
      final offerId = event.offerId;
      final opportunityIndex = state.offers.indexWhere((o) => o.id.vn == offerId);
      if (opportunityIndex != -1) {
        final offer = await appModels.readOffer(offerId);
        if (offer != null) {
          yield state.copyWith(
            offers: state.offers.map((o) => o.id.vn == offerId ? offer : o).toList(),
          );
        }
      }
    } on Exception {
      yield state.copyWith(status: OffersListStatus.failure);
    }
  }

  Future<List<OfferDto>> _listOffers({
    required Set<OfferslistFilterOption> filterOptions,
    required OffersListFilter filter,
    int offset = 0,
  }) async {
    
    final srvFilter = filter.copyWith(
      // Si la lista obliga a mostrar las ofertas de un cliente concreto, no hacemos caso de lo que se indique en el filtro
      customerId: customerId is! None ? customerId : filter.customerId,
      statuses: {
        if (filterOptions.contains(OfferslistFilterOption.offer_news)) OfferstatusCode.news,
        if (filterOptions.contains(OfferslistFilterOption.offer_draft)) OfferstatusCode.draft,
        if (filterOptions.contains(OfferslistFilterOption.offer_commercialization)) OfferstatusCode.commercialization,
        if (filterOptions.contains(OfferslistFilterOption.offer_historic)) OfferstatusCode.historic,
      },
    );

    final offers = await appModels.listOffers(filter: srvFilter, offset: offset, limit: _PAGE_SIZE);
    return offers;
  }
}
