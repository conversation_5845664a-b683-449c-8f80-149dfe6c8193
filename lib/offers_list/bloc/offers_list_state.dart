part of 'offers_list_bloc.dart';

enum OffersListStatus { initial, success, failure }

int _idGenerator = 0;

class OffersListState extends Equatable {
  const OffersListState({
    this.status = OffersListStatus.initial,
    this.offers = const <OfferDto>[],
    this.hasReachedMax = false,
    required this.filterOptions,
    required this.filter,
    this.selectedOfferId = "",
    required this.id,
  });

  // Actual status of the screen
  final OffersListStatus status;

  // *+*+* Nombre temporal hasta que se migre complétamente todo a un único filtro
  final OffersListFilter filter;
  // Wich filter are we applying to the oportunities of kind "oofers"
  final Set<OfferslistFilterOption> filterOptions;
  // The list of actual fetched offers
  final List<OfferDto> offers;
  // There is no more offers to be fetched
  final bool hasReachedMax;
  // Identifier of the selected opportunity
  final String selectedOfferId;

  final int id;

  factory OffersListState.create({
    required Set<OfferslistFilterOption> filterOptions,
    required OffersListFilter filter,
  }) =>
      OffersListState(
        filterOptions: filterOptions,
        filter: filter,
        id: ++_idGenerator,
      );

  OffersListState copyWith({
    OffersListStatus? status,
    Set<OfferslistFilterOption>? filterOptions,
    OffersListFilter? filter,
    List<OfferDto>? offers,
    bool? hasReachedMax,
    String? selectedOpportunityId,
  }) =>
      OffersListState(
        status: status ?? this.status,
        filterOptions: filterOptions ?? this.filterOptions,
        filter: filter ?? this.filter,
        offers: offers ?? this.offers,
        hasReachedMax: hasReachedMax ?? this.hasReachedMax,
        selectedOfferId: selectedOpportunityId ?? this.selectedOfferId,
        id: ++_idGenerator,
      );

  @override
  List<Object> get props => [id, status, offers, hasReachedMax, filterOptions, selectedOfferId];
  @override
  String toString() => "${super.toString()}$id";
}
