part of 'offers_list_bloc.dart';

int _id = 0;

abstract class OffersListEvent extends Equatable {
  const OffersListEvent();

  @override
  List<Object> get props => [];
}

class OffersListOnOfferChanged extends OffersListEvent {
  final String offerId;

  const OffersListOnOfferChanged({required this.offerId});
  @override
  List<Object> get props => [offerId];
}

class OffersListOnDemandChanged extends OffersListEvent {
  final String demandId;

  const OffersListOnDemandChanged({required this.demandId});
  @override
  List<Object> get props => [demandId];
}

class OffersListOnRefresh extends OffersListEvent {}

class OffersListOnRefreshOneOffer extends OffersListEvent {
  final String offerId;
  OffersListOnRefreshOneOffer({required this.offerId}) : super();
}

class OffersListOnFetch extends OffersListEvent {
  final _myid = ++_id;

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}

class OffersListOnFilterOptionSelected extends OffersListEvent {
  final OfferslistFilterOption filterOption;
  const OffersListOnFilterOptionSelected(this.filterOption);
  @override
  List<Object> get props => [filterOption];

  @override
  String toString() => "OffersListOnFilterOptionSelected { new filter: $filterOption }";
}

class OffersListOnFilterChanged extends OffersListEvent {
  final OffersListFilter filter;
  const OffersListOnFilterChanged(this.filter);
  @override
  List<Object> get props => [filter];

  @override
  String toString() => "OffersListOnFilterChanged { filter: $filter }";
}

class OffersListOnItemSelected extends OffersListEvent {
  final OfferDto offer;
  const OffersListOnItemSelected(this.offer);

  @override
  List<Object> get props => [offer];

  @override
  String toString() => "OpportunitySelected { opportunity: $offer  }";
}
