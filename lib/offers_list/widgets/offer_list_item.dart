import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/common/widgets/agent_link.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/offer_edit/widgets/offer_list_item_picture.dart';
import 'package:topbrokers/common/widgets/contact_link.dart';
import 'package:topbrokers/common/helpers.dart';

import 'package:flutter/material.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

const isDense = true;
const c_cloudagent_id = "0";

class OfferListItem extends StatelessWidget {
  const OfferListItem({
    Key? key,
    required this.offer,
    this.isSelected = false,
    this.onTap,
    this.showCustomer = true,
    this.showAgent = false,
  }) : super(key: key);

  final OfferDto offer;
  final bool isSelected;
  final void Function()? onTap;
  final bool showCustomer;
  final bool showAgent;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customer = offer.customer.vn;
    final agent = offer.agent.vn;

    List<Text> typePart = _buildPropertyType(context, theme, offer.property.vn);
    List<Text> saleRentPart = _buildSaleRent(context, theme, offer);
    Widget? cityPart = _buildCity(context, theme, offer.property.vn?.address.vn);
    List<Widget> attributesPart = _buildPropertyAttributes(context, theme, offer.property.vn?.attributes.vn);
    List<Text> saleRentAmountPart = _buildSaleRentAmount(context, theme, offer);
    List<Text> versionData = _buildVersionData(context, theme, offer);

    return BlocConsumer<SessionBloc, SessionState>(
      listener: (context, state) {},
      builder: (context, state) {
        final myAgent = state.agent.vn;

        if (agent == null || myAgent == null || agent.id.vn == null || myAgent.id.vn == null)
          return Card();
        else {
          bool isMine = myAgent.id == agent.id;
          bool isVersion = offer.version.vn?.type.vn?.code == null ? false : true;
          Color? colorFondo = isMine ? null : Colors.indigo[5];
          if (isVersion) {
            colorFondo = Color.fromARGB(255, 189, 207, 205);
          }
          return Card(
            color: colorFondo,
            //elevation: _statusToElevation(offer.status.vn?.code.vn ?? OfferstatusCode.news),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 8),
              leading: Stack(
                children: [
                  OfferListItemAvatar(
                    offer: offer,
                  ),
                  if (offer.agent.vn?.id.vn == c_cloudagent_id)
                    Positioned(
                      left: 1,
                      top: 1,
                      height: 14,
                      width: 14,
                      child: Icon(Icons.cloud_sharp, size: 14, color: Colors.white),
                    ),
                  if (offer.agent.vn?.id.vn == c_cloudagent_id)
                    Positioned(
                      left: 2,
                      top: 2,
                      height: 12,
                      width: 12,
                      child: Icon(Icons.cloud_sharp, size: 12, color: Colors.grey),
                    ),
                ],
              ),
              title: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: _addSeparationWhiteSpace(typePart, saleRentPart),
                        ),
                        Row(children: saleRentAmountPart),
                      ],
                    ),
                  ),
                  if (cityPart != null)
                    Expanded(
                      child: cityPart,
                    ),
                ],
              ),
              isThreeLine: false,
              subtitle: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            Row(
                              children: attributesPart,
                            ),
                          ],
                        ),
                      ),
                      if (showCustomer && customer != null && customer.id.vn != null)
                        Expanded(
                          child: ContactLink(contact: customer, isDense: true, isEnabled: isMine),
                        ),
                    ],
                  ),
                  if (!isMine)
                    Row(
                      children: [
                        Expanded(
                          child: AgentLink(agent: agent, isDense: true, isEnabled: isMine),
                        ),
                      ],
                    ),
                  SizedBox(height: 4),
                  Row(
                    children: versionData,
                  )
                ],
              ),
              dense: true,
              selected: isSelected,
              onTap: onTap,
            ),
          );
        }
      },
    );
  }

  static List<Text> _buildSaleRent(BuildContext context, ThemeData theme, OfferDto offer) {
    final textTheme = theme.textTheme;

    final bool isSale = offer.sale.vn?.allowed.vn ?? false;
    final bool isRent = offer.rent.vn?.allowed.vn ?? false;

    final String txt = (isSale && isRent)
        ? '${context.apploc.common_sale}/${context.apploc.common_rent}'
        : (isSale)
            ? context.apploc.common_sale
            : context.apploc.common_rent;

    return <Text>[Text(txt, style: textTheme.bodyText1)];
  }

  static List<Text> _buildSaleRentAmount(BuildContext context, ThemeData theme, OfferDto? offer) {
    final txt = (() {
      final saleAmount = offer?.sale.vn?.amount.vn;
      final rentAmount = offer?.rent.vn?.amount.vn;
      final monthlyPayment = offer?.sale.vn?.monthlyPayment.vn;
      if (saleAmount != null && rentAmount != null) {
        return '$saleAmount/$rentAmount €';
      } else if (saleAmount != null) {
        return '$saleAmount €';
      } else if (rentAmount != null) {
        return '$rentAmount €';
      } else {
        return null;
      }
    })();

    return txt != null ? [Text(txt)] : [];
  }

  static List<Text> _buildPropertyType(BuildContext context, ThemeData theme, PropertyDto? property) {
    final textTheme = theme.textTheme;
    final typeName = property?.type.vn?.label.vn?.localized ?? "";
    return [Text('$typeName', style: textTheme.bodyText1)];
  }

  static List<Widget> _buildPropertyAttributes(BuildContext context, ThemeData theme, PropertyAttributesDto? attributes) {
    final totalSurfaceM2 = attributes?.totalSurfaceM2.vn;
    final totalBedroomsCount = attributes?.totalBedroomsCount.vn;
    final isExterior = attributes?.facadeCodes.vn?.contains("exterior");

    final vdivider = VerticalDivider(color: Colors.black54, indent: 0, endIndent: 0, width: 6, thickness: 1);

    List<Widget> result = [];
    if (totalSurfaceM2 != null) {
      result.add(Text(context.apploc.offers_item_surfaceM2(totalSurfaceM2.toInt())));
    }
    if (totalBedroomsCount != null && totalBedroomsCount > 0) {
      if (result.length != 0) {
        result.add(vdivider);
      }
      result.add(Text(context.apploc.offers_item_bethroomsCount(totalBedroomsCount)));
    }
    if (isExterior != null && isExterior) {
      if (result.length != 0) {
        result.add(vdivider);
      }
      result.add(Text(context.apploc.offers_item_exteriorTxt));
    }
    return result;
  }

  static Widget? _buildCity(BuildContext context, ThemeData theme, PropertyAddressDto? address) {
    if (address == null) {
      return null;
    } else {
      final textTheme = theme.textTheme;
      final cityLabel = address.city.vn?.label.vn?.localized ?? "";
      //final provinceLabel = city?.vn?.province?.vn?.label?.vn?.localized ?? "";
      return Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
        Text(cityLabel, style: textTheme.bodyText1, overflow: TextOverflow.ellipsis),
        Text(
          address.composedStreetLine.vn ?? "", // provinceLabel,
          style: textTheme.bodyText2,
          overflow: TextOverflow.ellipsis,
        )
      ]);
    }
  }

  static List<Text> _addSeparationWhiteSpace(List<Text> a, List<Text> b) {
    bool aIsWhite = a.length == 0 || a[0].data == null || a[0].data!.length == 0 || a[0].data == " ";
    bool bIsWhite = b.length == 0 || b[0].data == null || b[0].data!.length == 0 || b[0].data == " ";

    if (!aIsWhite && !bIsWhite)
      return a + [Text(" ")] + b;
    else
      return a + b;
  }

  static List<Text> _buildVersionData(BuildContext context, ThemeData theme, OfferDto? offer) {
    if (offer?.version.vn?.type.vn?.code.vn != null) {
      final code = offer?.version.vn?.type.vn?.code;
      final versionLabel = offer?.version.vn?.type.vn?.label.vn?.localized ?? "";
      final monthlyPayment = offer?.sale.vn?.monthlyPayment.vn;
      return [
        Text(
          '$versionLabel | $monthlyPayment €',
          overflow: TextOverflow.clip,
          style: theme.textTheme.labelMedium,
        )
      ];
    } else {
      return [];
    }
  }
}
