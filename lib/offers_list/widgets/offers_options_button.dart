import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/offers_list/offers.dart';
import 'package:topbrokers/global/session.dart';

class OffersOptionsButton extends StatelessWidget {
  final bool visible;

  OffersOptionsButton({
    Key? key,
    this.visible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OffersListBloc, OffersListState>(
      builder: (context, state) {
        final button = _Button(
          onSelected: (OfferslistFilterOption filterOption) {
            BlocProvider.of<OffersListBloc>(context).add(OffersListOnFilterOptionSelected(filterOption));
          },
          activeOptions: state.filterOptions,
        );
        return AnimatedOpacity(
          opacity: visible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 150),
          child: visible ? button : IgnorePointer(child: button),
        );
      },
    );
  }
}

class _But<PERSON> extends StatelessWidget {
  const _Button({
    Key? key,
    required this.onSelected,
    required this.activeOptions,
  }) : super(key: key);

  final PopupMenuItemSelected<OfferslistFilterOption> onSelected;
  final Set<OfferslistFilterOption> activeOptions;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<OfferslistFilterOption>(
      key: AgentorKeys.offersFilterButton,
      tooltip: "Filter",
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => [
        if (!context.imIndividual())
          PopupMenuItem<OfferslistFilterOption>(
            //key: AgentorKeys.allFilter,
            value: OfferslistFilterOption.offer_news,
            child: _buildMenuItemWidget(
              "noticia",
              activeOptions.contains(OfferslistFilterOption.offer_news),
            ),
          ),
        PopupMenuItem<OfferslistFilterOption>(
          //key: AgentorKeys.allFilter,
          value: OfferslistFilterOption.offer_draft,
          child: _buildMenuItemWidget(
            "borrador",
            activeOptions.contains(OfferslistFilterOption.offer_draft),
          ),
        ),
        PopupMenuItem<OfferslistFilterOption>(
          //key: ArchSampleKeys.activeFilter,
          value: OfferslistFilterOption.offer_commercialization,
          child: _buildMenuItemWidget(
            "disponible",
            activeOptions.contains(OfferslistFilterOption.offer_commercialization),
          ),
        ),
        PopupMenuItem<OfferslistFilterOption>(
          //key: ArchSampleKeys.activeFilter,
          value: OfferslistFilterOption.offer_historic,
          child: _buildMenuItemWidget(
            "papelera",
            activeOptions.contains(OfferslistFilterOption.offer_historic),
          ),
        ),
      ],
      icon: Icon(Icons.filter_list),
    );
  }

  Widget _buildMenuItemWidget(String text, bool active) {
    return Row(children: [
      if (active)
        Icon(Icons.check_box_outlined, color: Colors.grey)
      else
        Icon(Icons.check_box_outline_blank, color: Colors.grey),
      Text(text)
    ]);
  }
}
