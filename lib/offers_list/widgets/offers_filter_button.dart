import 'package:topbrokers/offers_filter_dialog/offers_filter.dart';
import 'package:topbrokers/offers_list/offers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

///
/// Botón que despliega diálogo de parámetros de filtrado de una lista de oportunidades.
/// Cuando se obtiene el nuevo filtro, se genera un evento OffersListOnFilterChangedOnFilterChanged
///
class OffersFilterButton extends StatelessWidget {
  final bool visible;

  OffersFilterButton({
    Key? key,
    this.visible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OffersListBloc, OffersListState>(
      builder: (context, state) => IconButton(
        icon: const Icon(Icons.search),
        tooltip: 'Filtrar',
        onPressed: () async {
          final newFilter = await showOffersFilterDialog(context, filter: state.filter);
          if (newFilter != null) {
            BlocProvider.of<OffersListBloc>(context).add(OffersListOnFilterChanged(newFilter));
          }
        },
      ),
    );
  }
}
