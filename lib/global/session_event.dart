part of 'session_bloc.dart';

abstract class SessionEvent extends Equatable {
  const SessionEvent();

  @override
  List<Object> get props => [];
}

///
/// Se ha realizado una autenticación... se nos informa del token obtenido.
///
class SessionOnLoggedIn extends SessionEvent {
  final String token;
    
  SessionOnLoggedIn({required this.token});
}

class SessionOnLoggedOut extends SessionEvent {
  SessionOnLoggedOut();
}

///
/// Ordena la inicialización:
/// - Establecer si hay token
///
class SessionOnInit extends SessionEvent {
   
}

class SessionOnRefreshAgent extends SessionEvent {

}
class SessionOnRefreshEcomAccount extends SessionEvent {

}

class SessionOnRefresh extends SessionEvent {
  
}

