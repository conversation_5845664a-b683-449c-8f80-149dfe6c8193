part of 'session_bloc.dart';

enum SessionStatus { initial, authentified, notAthentified, failure }

class SessionState extends Equatable {
  final SessionStatus status;
  final Optional<String> userToken;
  final Optional<AgentDto> agent;
  final Optional<EcomAccountDto> ecomAccount;

  final int version;

  const SessionState({
    this.status = SessionStatus.initial,
    this.userToken = const None(),
    this.agent = const None(),
    this.ecomAccount = const None(),
    this.version = 1,
  });

  SessionState copyWith({
    SessionStatus? status,
    Optional<String>? userToken,
    Optional<AgentDto>? agent,
    Optional<EcomAccountDto>? ecomAccount,
  }) {
    return SessionState(
      status: status ?? this.status,
      userToken: userToken ?? this.userToken,
      agent: agent ?? this.agent,
      ecomAccount: ecomAccount ?? this.ecomAccount,
      version: this.version + 1,
    );
  }
  /**
   * ¿Soy un particular?
   * Agente de la sesión es un particular
   */
  bool get imIndividual => agent.vn?.isIndividual==True;

  @override
  List<Object> get props => [version, status, userToken];
}
