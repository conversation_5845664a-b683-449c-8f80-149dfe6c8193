import 'package:agentor_repositoryns/models/models.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/common/helpers.dart';

class AuthAgentProvider extends StatelessWidget {
  
  final Widget Function (BuildContext, AgentDto) builder;
  
  AuthAgentProvider({required this.builder}) : super();
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SessionBloc, SessionState>(
      listener: (context, state) {},
      builder: (context, globalState) {
        switch (globalState.status) {
          case SessionStatus.authentified:
            return this.builder(context, globalState.agent.v);
          default:
            context.showError("No estás autenticado");
            return Icon(Icons.security_update_warning );
        }
      },
    );
  }
}
