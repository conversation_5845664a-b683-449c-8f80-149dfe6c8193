import 'dart:async';

import 'package:agentor_deps/agentor_deps.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agentor_session/agentor_session.dart';
import 'package:rxdart/rxdart.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

part 'session_event.dart';
part 'session_state.dart';

class SessionBloc extends Bloc<SessionEvent, SessionState> {
  final api = Deps.solve<ApiServices>();
  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;

  SessionBloc({required ModelsChannelBloc modelsChannel}) : super(SessionState()) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((state) {
      if (state is ModelsChannelUpdatedState<AgentDto>) {
        if (state.entities.length != 0 && state.entities[0].id == super.state.agent.vn?.id) {
          this.add(SessionOnRefreshAgent());
        }
      } else if (state is ModelsChannelWorkgroupOfferCreatedState || state is ModelsChannelWorkgroupOfferDeletedState) {
        // Como no podemos detectar si internamente se ha modificado el balance de la cuenta, nos basamos en los eventos
        // que PUEDEN tener asociado un cambio de balance de la cuenta
        this.add(SessionOnRefreshEcomAccount());
      } else {
        // NADA
      }
    });
  }

  factory SessionBloc.create({required BuildContext context}) {
    return SessionBloc(
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _modelsChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<SessionEvent, SessionState>> transformEvents(
    Stream<SessionEvent> events,
    TransitionFunction<SessionEvent, SessionState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<SessionState> mapEventToState(SessionEvent event) async* {
    if (event is SessionOnInit) {
      yield* _mapOnInit(state, event);
    } else if (event is SessionOnLoggedIn) {
      yield* _mapOnAuthenticated(state, event);
    } else if (event is SessionOnLoggedOut) {
      yield* _mapOnLoggedOut(state, event);
    } else if (event is SessionOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is SessionOnRefreshEcomAccount) {
      yield* _mapOnRefreshEcomAccount(state, event);
    } else if (event is SessionOnRefreshAgent) {
      yield* _mapOnRefreshAgent(state, event);
    }
  }

  Stream<SessionState> _mapOnInit(SessionState state, SessionOnInit event) async* {
    try {
      final token = await _initUserToken();
      if (token != null) {
        final agent = await api.getMyAgent();
        final account = await api.getMyEcomAccount();

        yield state.copyWith(
          status: SessionStatus.authentified,
          userToken: Some(token),
          agent: agent == null ? None() : Some(agent),
          ecomAccount: account == null ? None() : Some(account),
        ); //, definibleFields: definibleFields);
      } else {
        yield state.copyWith(
            status: SessionStatus.notAthentified, userToken: None(), agent: None(), ecomAccount: None());
      }
    } on Exception {
      yield state.copyWith(status: SessionStatus.failure);
    }
  }

  Stream<SessionState> _mapOnAuthenticated(SessionState state, SessionOnLoggedIn event) async* {
    try {
      _setUserToken(event.token);
      final agent = await api.getMyAgent();
      final account = await api.getMyEcomAccount();
      yield state.copyWith(
        status: SessionStatus.authentified,
        userToken: Some(event.token),
        agent: agent == null ? None() : Some(agent),
        ecomAccount: account == null ? None() : Some(account),
      );
    } on Exception {
      yield state.copyWith(status: SessionStatus.failure);
    }
  }

  ///
  /// Equivale a onInit, pero lo diferenciamos semánticamente para que onInit se ejecute una única vez
  ///
  Stream<SessionState> _mapOnRefresh(SessionState state, SessionOnRefresh event) async* {
    try {
      final token = await _initUserToken();
      if (token != null) {
        final agent = await api.getMyAgent();
        final ecomAccount = await api.getMyEcomAccount();
        yield state.copyWith(
          status: SessionStatus.authentified,
          userToken: Some(token),
          agent: agent == null ? None() : Some(agent),
          ecomAccount: ecomAccount == null ? None() : Some(ecomAccount),
        ); //, definibleFields: definibleFields);
      } else {
        yield state.copyWith(
          status: SessionStatus.notAthentified,
          userToken: None(),
          agent: None(),
          ecomAccount: None(),
        );
      }
    } on Exception {
      yield state.copyWith(status: SessionStatus.failure);
    }
  }

  ///
  /// Equivale a onInit, pero lo diferenciamos semánticamente para que onInit se ejecute una única vez
  ///
  Stream<SessionState> _mapOnRefreshAgent(SessionState state, SessionOnRefreshAgent event) async* {
    try {
      final agent = await api.getMyAgent();
      if (agent == null)
        yield* _mapOnRefresh(state, new SessionOnRefresh());
      else
        yield state.copyWith(agent: Some(agent));
    } on Exception {
      yield* _mapOnRefresh(state, new SessionOnRefresh());
    }
  }

  Stream<SessionState> _mapOnRefreshEcomAccount(SessionState state, SessionOnRefreshEcomAccount event) async* {
    try {
      final ecomAccount = await api.getMyEcomAccount();
      if (ecomAccount == null)
        yield* _mapOnRefresh(state, new SessionOnRefresh());
      else
        yield state.copyWith(ecomAccount: Some(ecomAccount));
    } on Exception {
      yield* _mapOnRefresh(state, new SessionOnRefresh());
    }
  }

  Stream<SessionState> _mapOnLoggedOut(SessionState state, SessionOnLoggedOut event) async* {
    try {
      await _cleanUserToken();
      yield state.copyWith(status: SessionStatus.notAthentified, userToken: None(), agent: None(), ecomAccount: None());
    } on Exception {
      yield state.copyWith(status: SessionStatus.failure);
    }
  }

  Future<String?> _initUserToken() async {
    try {
      final ses = FlutterSession();
      final String? token = await ses.get("token");
      if (token != null && token != "") {
        final renewed = await api.renewToken(token);
        await ses.set("token", renewed);
        api.userToken = renewed;
        return renewed;
      } else {
        return null;
      }
    } on ApiForbiddenException {
      return null;
    }
  }

  Future _cleanUserToken() async {
    // Asignar null produce un error en FlutterSession
    // por eso usamos ""
    await FlutterSession().set("token", "");
    api.userToken = "";
  }

  Future _setUserToken(String token) async {
    FlutterSession().set("token", token);
    api.userToken = token;
  }
}
