import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/demand_edit/demand_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/global/session_bloc.dart';

part 'demand_edit_event.dart';
part 'demand_edit_state.dart';

class DemandEditBloc extends Bloc<DemandEditEvent, DemandEditState> {
  final AppModelsNS appModels;
  final SessionBloc globalBloc;

  DemandEditBloc({required this.appModels, required this.globalBloc}) : super(DemandEditState());

  factory DemandEditBloc.create(BuildContext context) => DemandEditBloc(
        globalBloc: BlocProvider.of<SessionBloc>(context, listen: false),
        appModels: Provider.of<AppModelsNS>(context, listen: false),
      );

  @override
  Stream<Transition<DemandEditEvent, DemandEditState>> transformEvents(
    Stream<DemandEditEvent> events,
    TransitionFunction<DemandEditEvent, DemandEditState> transitionFn,
  ) =>
      super.transformEvents(
        events.debounceTime(const Duration(milliseconds: 100)),
        transitionFn,
      );

  @override
  Stream<DemandEditState> mapEventToState(DemandEditEvent event) async* {
    final state = this.state;
    if (event is DemandEditOnEditEvent) {
      yield* _mapOnEditEventToState(state, event);
    } else if (event is DemandEditOnNewEvent) {
      yield* _mapOnNewEventToState(state, event);
    } else if (event is DemandEditOnSaveEvent && state is DemandEditLoaded) {
      yield* _mapOnSaveEventToState(state, event);
    } else if (event is DemandEditOnValidationErrorEvent && state is DemandEditLoaded) {
      yield* _mapOnValidationErrorEventToState(state, event);
    }
  }

  Stream<DemandEditState> _mapOnEditEventToState(DemandEditState state, DemandEditOnEditEvent event) async* {
    try {
      final demand = await _readDemand(event.demandId);
      if (demand == null) {
        yield DemandEditLoadFailure(error: "La demanda no existe");
      } else {
        final definibleFields = await _readDefinibleFields();
        final propertyTypes = await appModels.listPropertytypes(filter: PropertytypesListFilter(includeSubtypes: True));
        yield DemandEditLoaded(
          demand: demand,
          definibleFields: definibleFields,
          propertyTypes: propertyTypes,
        );
      }
    } on Exception catch (e) {
      print("*+*+* Error _mapOnEditEventToState $e");
      yield DemandEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<DemandEditState> _mapOnNewEventToState(DemandEditState state, DemandEditOnNewEvent event) async* {
    try {
      final demand = DemandUtils.emptyDemand();
      if (event.customerId != null) {
        final contact = await appModels.readContact(event.customerId!);
        if (contact != null) {
          demand.customer = Some(contact);
        }
      }
      final definibleFields = await _readDefinibleFields();
      final propertyTypes = await appModels.listPropertytypes(filter: PropertytypesListFilter(includeSubtypes: True));

      yield DemandEditLoaded(
        demand: demand,
        definibleFields: definibleFields,
        propertyTypes: propertyTypes,
      );
    } on Exception catch (e) {
      print("*+*+* Error en _mapOnNewEventToState: $e");
      yield DemandEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<DemandEditState> _mapOnSaveEventToState(DemandEditLoaded state, DemandEditOnSaveEvent event) async* {
    try {
      if (event.demand.id.vn == null) {
        final created = await _createDemand(event.demand);
        yield DemandEditSaved(demand: created);
        // Notificamos a quien le interese que la oferta ha cambiado.

      } else {
        final updated = await _updateDemand(event.demand);
        yield DemandEditSaved(demand: updated);
        // Notificamos a quien le interese que la oferta ha cambiado.

      }
    } on Exception {
      final msg = event.demand.id.vn == null ? "Problemas guardando la nueva oferta" : "Problemas guardando cambios";
      yield DemandEditSaveFailure.fromLoadedState(state, msg);
    }
  }

  Stream<DemandEditState> _mapOnValidationErrorEventToState(
    DemandEditLoaded state,
    DemandEditOnValidationErrorEvent event,
  ) async* {
    yield DemandEditValidationFailure.fromLoadedState(state, event.error);
  }

  Future<DemandDto?> _readDemand(String id) {
    return appModels.readDemand(id);
  }

  Future<DemandDto> _updateDemand(DemandDto demand) {
    return appModels.updateDemand(demand);
  }

  Future<DemandDto> _createDemand(DemandDto demand) {
    return appModels.createDemand(demand);
  }

  Future<Map<String, FieldDefDto>> _readDefinibleFields() async {
    final result = await appModels.listDemandFielddefs();
    return Map.fromEntries(result.map((fieldDef) => MapEntry(fieldDef.code, fieldDef)));
  }
}
