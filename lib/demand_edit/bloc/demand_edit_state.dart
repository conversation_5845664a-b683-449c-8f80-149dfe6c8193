part of 'demand_edit_bloc.dart';

class DemandEditState extends Equatable {
  @override
  List<Object> get props => [];
}

class DemandEditLoading extends DemandEditState {}

class DemandEditLoaded extends DemandEditState {
  final DemandDto demand;
  final Map<String, FieldDefDto> definibleFields;
  final List<PropertytypeDto> propertyTypes;

  DemandEditLoaded({required this.demand, required this.definibleFields, required this.propertyTypes}) : super();

  @override
  List<Object> get props => [demand];
}

class DemandEditLoadFailure extends DemandEditState {
  final String error;

  DemandEditLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

class DemandEditSaved extends DemandEditState {
  final DemandDto demand;

  DemandEditSaved({required this.demand}) : super();

  @override
  List<Object> get props => [demand];
}

class DemandEditSaveFailure extends DemandEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  DemandEditSaveFailure({
    required DemandDto demand,
    required Map<String, FieldDefDto> definibleFields,
    required List<PropertytypeDto> propertyTypes,
    required this.error,
  }) : super(demand: demand, definibleFields: definibleFields, propertyTypes: propertyTypes);

  factory DemandEditSaveFailure.fromLoadedState(DemandEditLoaded loadedStatus, String error) {
    return DemandEditSaveFailure(
      demand: loadedStatus.demand,
      definibleFields: loadedStatus.definibleFields,
      propertyTypes: loadedStatus.propertyTypes,
      error: error,
    );
  }

  @override
  List<Object> get props => [lastError, demand, error];
}

class DemandEditValidationFailure extends DemandEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  DemandEditValidationFailure({
    required DemandDto demand,
    required Map<String, FieldDefDto> definibleFields,
    required List<PropertytypeDto> propertyTypes,
    required this.error,
  }) : super(demand: demand, definibleFields: definibleFields, propertyTypes: propertyTypes);

  factory DemandEditValidationFailure.fromLoadedState(DemandEditLoaded loadedStatus, String error) {
    return DemandEditValidationFailure(
      demand: loadedStatus.demand,
      definibleFields: loadedStatus.definibleFields,
      propertyTypes: loadedStatus.propertyTypes,
      error: error,
    );
  }
  @override
  List<Object> get props => [lastError, demand, error];
}
