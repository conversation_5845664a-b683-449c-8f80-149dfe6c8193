part of 'demand_edit_bloc.dart';

abstract class DemandEditEvent extends Equatable {
  const DemandEditEvent();

  @override
  List<Object> get props => [];
}

class DemandEditOnEditEvent extends DemandEditEvent {
  final String demandId;
  DemandEditOnEditEvent({required this.demandId});
  @override
  List<Object> get props => [demandId];
}

class DemandEditOnNewEvent extends DemandEditEvent {
  /// El cliente predeterminado de la demanda
  /// Puede dejarse vacío, en cuyo caso el usuario tendrá que elegir uno
  final String? customerId;

  DemandEditOnNewEvent({this.customerId}) : super();

  @override
  List<Object> get props => [customerId ?? ""];
}

///
/// El usuario quiere guardar los cambios
///
class DemandEditOnSaveEvent extends DemandEditEvent {
  final DemandDto demand;
  DemandEditOnSaveEvent({required this.demand}) : super();

  @override
  List<Object> get props => [demand];
}

class DemandEditOnValidationErrorEvent extends DemandEditEvent {
  final String error;
  DemandEditOnValidationErrorEvent({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

///
/// Los cambios se han guardado corréctamente
///
class DemandEditOnSavedEvent extends DemandEditEvent {}
