import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/RefCache.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contacts_list/widgets/widgets.dart';
import 'package:topbrokers/demand_edit/bloc/demand_edit_bloc.dart';
import 'package:topbrokers/demand_edit/models/demand_edit_page_params.dart';
import 'package:topbrokers/demand_edit/models/demand_edit_page_result.dart';
import 'package:topbrokers/global/session.dart';
import 'package:topbrokers/offer_edit/views/property_form_group.dart';

class DemandEditPage extends StatefulWidget {
  final bool isNew;
  final DemandEditPageParams params;

  DemandEditPage({Key? key, required this.params})
      : this.isNew = params is NewDemandEditParams,
        super(key: key ?? AgentorKeys.newDemandPage);

  @override
  _DemandEditPageState createState() => _DemandEditPageState();
}

class _DemandEditPageState extends State<DemandEditPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();
  final refCache = RefCache();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) {
        if (widget.params is ExistingDemandEditParams) {
          final params = this.widget.params as ExistingDemandEditParams;
          return DemandEditBloc.create(context)..add(DemandEditOnEditEvent(demandId: params.id));
        } else {
          final params = this.widget.params as NewDemandEditParams;
          return DemandEditBloc.create(context)..add(DemandEditOnNewEvent(customerId: params.customerId));
        }
      },
      child: BlocConsumer<DemandEditBloc, DemandEditState>(
        listener: (context, state) {
          if (state is DemandEditSaved) {
            Navigator.pop(context, DemandEditPageResult(id: state.demand.id, saved: true));
          }
        },
        builder: (context, state) {
          if (state is DemandEditLoaded) {
            return Scaffold(
              appBar: AppBar(
                title: Text(widget.isNew ? "Nueva alerta" : "Alerta"),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: BlocConsumer<DemandEditBloc, DemandEditState>(
                  listener: (context, state) {
                    if (state is DemandEditSaveFailure) {
                      context.showError(state.error);
                    } else if (state is DemandEditValidationFailure) {
                      context.showError(state.error);
                    }
                  },
                  builder: (context, state) => _buildForm(
                    context,
                    (state as DemandEditLoaded).demand,
                    state.definibleFields,
                    state.propertyTypes,
                  ),
                ),
              ),
              floatingActionButton: FloatingActionButton(
                key: AgentorKeys.saveNewDemand,
                tooltip: !widget.isNew ? "Guardar cambios" : "Crear alerta",
                child: Icon(Icons.save_outlined, semanticLabel: "Save"),
                onPressed: () async {
                  final formState = _formKey.currentState;
                  if (formState != null) {
                    if (formState.validate()) {
                      formState.save();
                      BlocProvider.of<DemandEditBloc>(context).add(
                        DemandEditOnSaveEvent(demand: state.demand),
                      );
                    } else {
                      BlocProvider.of<DemandEditBloc>(context).add(
                        DemandEditOnValidationErrorEvent(error: "El formulario contiene errores.  Revíselo"),
                      );
                    }
                  }
                },
              ),
            );
          } else if (state is DemandEditLoadFailure) {
            return Center(child: Text(state.error));
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  Widget _buildForm(
    BuildContext context,
    
    DemandDto demand,
    Map<String, FieldDefDto> fieldsDefinitions,
    List<PropertytypeDto> propertyTypes,
  ) {
    final imIndividual = context.imIndividual();
    return new Container(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            _demandEntries(imIndividual, demand, fieldsDefinitions, propertyTypes)
                .buildForm(key: _formKey, context: context),
            SizedBox.fromSize(size: Size.fromHeight(64))
          ],
        ),
      ),
    );
  }

  List<Widget> _demandEntries(
    bool imIndividual,
    DemandDto demand,
    Map<String, FieldDefDto> fieldsDefinitions,
    List<PropertytypeDto> propertyTypes,
  ) {
    return <Widget>[
      GroupEntry(label: "Operación", children: [
        fieldsDefinitions[FielddefsSrv.c_demand_status]!.buildEntry(
          getValue: () => (demand.status.v.code.vn ?? DemandstatusCode.active).enumToString(),
          setValue: (String? code) {
            setState(() {
              demand.status.v.code = code == null ? None() : Some(code.toDemandStatusCode());
            });
          },
          isRequired: true,
        ),
        if (!imIndividual)
          SearchFieldEntry<ContactDto>(
            label: fieldsDefinitions[FielddefsSrv.c_demand_customer]!.label?.localized,
            getValue: () => demand.customer.vn,
            setValue: (ContactDto? value) => setState(() => demand.customer = Some(value)),
            onSearch: (String search) => api.listContacts(filter: ContactsListFilter(search: Some(search))),
            itemBuilder: (context, contact, {bool? isSelected}) => ContactListItem(contact: contact),
            valueToString: (contact) => contact != null ? contact.name.vn ?? "" : "Seleccione un contacto",
            isRequired: true,
          ),
        fieldsDefinitions[FielddefsSrv.c_demand_type]!.buildEntry(
          getValue: () => demand.sale.vn?.allowed.vn ?? false
              ? "sale"
              : demand.rent.vn?.allowed.vn ?? false
                  ? "rent"
                  : null,
          setValue: (String? value) {
            setState(() {
              if (value == "sale") {
                demand.sale = Some(OfferSaleDto.emptySale());
                demand.rent = Some(OfferRentDto.emptyNoRent());
              } else if (value == "rent") {
                demand.sale = Some(OfferSaleDto.emptyNoSale());
                demand.rent = Some(OfferRentDto.emptyRent());
              } else {
                demand.sale = Some(OfferSaleDto.emptyNoSale());
                demand.rent = Some(OfferRentDto.emptyNoRent());
              }
            });
          },
          isRequired: true,
        ),
        if (demand.sale.vn?.allowed.vn ?? false)
          fieldsDefinitions[FielddefsSrv.c_demand_sale_amount]!.buildEntry(
            getValue: () => demand.sale.vn?.amount.vn,
            setValue: (double? v) {
              setState(() {
                demand.sale.v.amount = Some(v);
              });
            },
            isRequired: demand.status.vn?.code.vn == DemandstatusCode.active,
          ),
        if (demand.rent.vn?.allowed.vn ?? false)
          fieldsDefinitions[FielddefsSrv.c_demand_rent_amount]!.buildEntry(
            getValue: () => demand.rent.v.amount.v,
            setValue: (double? v) {
              setState(() {
                demand.rent.v.amount = Some(v);
              });
            },
            isRequired: demand.status.vn?.code.vn == DemandstatusCode.active,
          ),
      ]),
      if (demand.property.vn != null)
        PropertyFormGroup(
          property: demand.property.v,
          fieldsDefinitions: fieldsDefinitions,
          propertyTypes: propertyTypes,
        ),
    ];
  }
}
