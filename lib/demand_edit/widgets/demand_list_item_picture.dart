import 'dart:collection';

import 'package:topbrokers/common/widgets/gravatar.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:flutter/material.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class DemandListItemAvatar extends StatelessWidget {
  DemandListItemAvatar({Key? key, required this.demand}) : super(key: key);

  final DemandDto demand;

  final Map<DemandstatusCode, Color> _statusBgColors = HashMap.fromEntries([
    MapEntry(DemandstatusCode.active, Colors.indigo[400] ?? Colors.indigo),
    MapEntry(DemandstatusCode.historic, Colors.grey[500] ?? Colors.grey),
  ]);

  @override
  Widget build(BuildContext context) {
    final alertNum = demand.matchingsinfo.vn?.notReviewedCount.vn ?? 0;
    final alertText = alertNum>0 ? "$alertNum" : "";
    return Gravatar(
      size: 40,
      ledColor: _statusBgColors[demand.status.vn?.code.vn ?? DemandstatusCode.historic],
      led: Icons.circle,
      ledSize: 10,
      alertText: alertText,
      child: Icon(CustomAppIcons.demand),
    );
  }

  String? getFavouritePictureUrl(DemandDto demand) {
    final favouritePicture = demand.property.vn?.favouritePicture.vn;
    return favouritePicture?.thumbnail.vn?.url.vn ?? favouritePicture?.original.vn?.url.vn;
  }
}
