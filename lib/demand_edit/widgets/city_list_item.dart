import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';

class CityListItem extends StatelessWidget {
  final CityDto city;

  const CityListItem({required this.city}) : super();

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            city.label.vn?.localized ?? "",
            style: textTheme.bodyText1,
          ),
          Text(
            city.province.vn?.label.vn?.localized ?? "",
            style: textTheme.bodyText2,
          ),
        ],
      ),
    );
  }
}
