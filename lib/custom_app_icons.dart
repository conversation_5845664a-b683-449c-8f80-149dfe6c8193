import 'package:flutter/material.dart';

class CustomAppIcons {
  CustomAppIcons._();

  static const _kFontFam = 'CustomAppIcons';
  static const _kFontPkg = null;

  static const IconData action = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData delete = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData demand = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData offer = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData life_ring = IconData(0xf1cd, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData whatsapp = IconData(0xf232, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData contact = Icons.face_rounded;
    
}
