import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ContactsOptionsButton extends StatelessWidget {
  final bool visible;

  ContactsOptionsButton({
    Key? key,
    required this.visible,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ContactsBloc, ContactsState>(builder: (context, state) {
      final button = _Button(
        onSelected: (ContactsFilterOptions filterOption) {
          final theNewFilter = filterOption.applyToFilter(state.filter);
          BlocProvider.of<ContactsBloc>(context).add(ContactslistOnFilterChanged(theNewFilter));
        },
        activeFilter: state.filter,
      );
      return AnimatedOpacity(
        opacity: visible ? 1.0 : 0.0,
        duration: Duration(milliseconds: 150),
        child: visible ? button : IgnorePointer(child: button),
      );
    });
  }
}

class _<PERSON><PERSON> extends StatelessWidget {
  const _Button({
    Key? key,
    required this.onSelected,
    required this.activeFilter,
  }) : super(key: key);

  final PopupMenuItemSelected<ContactsFilterOptions> onSelected;
  final ContactsListFilter activeFilter;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<ContactsFilterOptions>(
      key: AgentorKeys.contactsFilterButton,
      icon: Icon(Icons.filter_list),
      tooltip: "Filtro",
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => <PopupMenuItem<ContactsFilterOptions>>[
        PopupMenuItem<ContactsFilterOptions>(
          value: ContactsFilterOptions.isOfferCustomer,
          child: _buildMenuItemWidget(
            "Con ofertas",
            activeFilter.isOfferCustomer.vd(false),
          ),
        ),
        PopupMenuItem<ContactsFilterOptions>(
          value: ContactsFilterOptions.isDemandCustomer,
          child: _buildMenuItemWidget(
            "Con demandas",
            activeFilter.isDemandCustomer.vd(false),
          ),
        ),
        PopupMenuItem<ContactsFilterOptions>(
          //key: AgentorKeys.allFilter,
          value: ContactsFilterOptions.any,
          child: _buildMenuItemWidget(
            "Cualquiera",
            !activeFilter.isDemandCustomer.vd(false) && !activeFilter.isOfferCustomer.vd(false),
          ),
        ),
      ],
    );
  }

  static Widget _buildMenuItemWidget(String text, bool active) {
    return Row(children: [
      if (active)
        Icon(Icons.check_box_outlined, color: Colors.grey)
      else
        Icon(Icons.check_box_outline_blank, color: Colors.grey),
      Text(text),
    ]);
  }
}
