import 'package:flutter/material.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class ContactListItem extends StatelessWidget {
  const ContactListItem({
    Key? key,
    required this.contact,
    this.isSelected = false,
    this.onTap,
  }) : super(key: key);

  final ContactDto contact;
  final bool isSelected;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    List<Text> namePart = _namePart(context, theme, contact);
    return Card(
      child: ListTile(
        leading: Icon(Icons.face),
        title: Row(children: namePart),
        isThreeLine: false,
        subtitle: Row(children: _contacting<PERSON>art(contact)),
        dense: true,
        onTap: onTap,
      ),
    );
  }

  List<Text> _contactingPart(ContactDto contact) {
    final email = contact.email.vn;
    final mobile = contact.mobile.vn;
    if (email != null && mobile != null)
      return [Text(email), Text(" "), Text(mobile)];
    else if (email != null)
      return [Text(email)];
    else if (mobile != null)
      return [Text(mobile)];
    else
      return [];
  }

  List<Text> _namePart(BuildContext context, ThemeData theme, ContactDto contact) {
    final firstName = contact.firstName.vn;
    final lastName = contact.lastName.vn;

    return (firstName != null && lastName != null)
        ? [Text("$firstName $lastName")]
        : firstName != null
            ? [Text(firstName)]
            : lastName != null
                ? [Text(lastName)]
                : [];
  }
}
