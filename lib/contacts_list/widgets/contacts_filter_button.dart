import 'package:topbrokers/contacts_filter_dialog/contacts_filter.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

///
/// Botón que despliega diálogo de parámetros de filtrado de una lista de oportunidades.
/// Cuando se obtiene el nuevo filtro, se genera un evento ContactsOnFilterChangedOnFilterChanged
///
class ContactsFilterButton extends StatelessWidget {
  final bool visible;

  ContactsFilterButton({
    Key? key,
    this.visible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ContactsBloc, ContactsState>(
      builder: (context, state) => IconButton(
        icon: const Icon(Icons.search),
        tooltip: 'Filtrar',
        onPressed: () async {
          final newFilter = await showContactsFilterDialog(context, filter: state.filter);
          if (newFilter != null) {
            BlocProvider.of<ContactsBloc>(context).add(ContactslistOnFilterChanged(newFilter));
          }
        },
      ),
    );
  }
}
