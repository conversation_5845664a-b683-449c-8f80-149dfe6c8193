import 'package:agentor_repositoryns/agentor_repositoryns.dart';

enum ContactsFilterOptions { isDemandCustomer, isOfferCustomer, any }

extension ContactsFilterOptionsExtension on ContactsFilterOptions {
  ///
  /// Activa/desactiva el filtro correspondiente a la opción indicada
  ///
  ContactsListFilter applyToFilter(ContactsListFilter filter) {
    switch (this) {
      case ContactsFilterOptions.isDemandCustomer:
        return filter.copyWith(isDemandCustomer: Some(true), isOfferCustomer: None());
      case ContactsFilterOptions.isOfferCustomer:
        return filter.copyWith(isDemandCustomer: None(), isOfferCustomer: Some(true));
      case ContactsFilterOptions.any:
        return filter.copyWith(isOfferCustomer: None(), isDemandCustomer: None());
      default:
        return filter;
    }
  }
}
