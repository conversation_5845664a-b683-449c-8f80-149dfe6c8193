import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/contacts_list/bloc/contacts_list_bloc.dart';
import 'package:topbrokers/contacts_list/widgets/widgets.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/routes.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ContactsList extends StatefulWidget {
  @override
  _ContactsListState createState() => _ContactsListState();
}

class _ContactsListState extends State<ContactsList> {
  final _scrollController = ScrollController();
  late ContactsBloc _contactsBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _contactsBloc = context.bloc<ContactsBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ContactsBloc, ContactsState>(
      listener: (context, state) {
        if (!state.hasReachedMax && _isBottom) {
          _contactsBloc.add(ContactslistOnFetch());
        }
      },
      builder: (context, state) {
        switch (state.status) {
          case ContactsStatus.failure:
            return const Center(child: Text('Problemas obteniendo contactos del servidor'));
          case ContactsStatus.success:
            return state.contacts.isEmpty
                ? Center(
                    child: Column(mainAxisSize: MainAxisSize.min, children: [
                    Text('Ningún contacto', style: TextStyle(color: Colors.grey)),
                    Divider(height: 42),
                    ElevatedButton.icon(
                      icon: Icon(
                        CustomAppIcons.contact,
                        size: 16,
                      ),
                      label: Text('Registra un Contacto'),
                      onPressed: () {
                        Navigator.pushNamed(context, AgentorRoutes.addContact);
                      },
                    )
                  ]))
                : RefreshIndicator(
                    onRefresh: () async {
                      _contactsBloc.add(ContactslistOnRefresh());
                    },
                    child: ListView.builder(
                      key: AgentorKeys.contactsList,
                      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                      itemBuilder: (BuildContext context, int index) {
                        if (index >= state.contacts.length) {
                          return BottomLoader();
                        } else {
                          return ContactListItem(
                            contact: state.contacts[index],
                            onTap: () {
                              final contactId = state.contacts[index].id.v;
                              Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: contactId);
                            },
                          );
                        }
                      },
                      itemCount: state.hasReachedMax ? state.contacts.length : state.contacts.length + 1,
                      controller: _scrollController,
                    ));

          default:
            return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _contactsBloc.add(ContactslistOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
