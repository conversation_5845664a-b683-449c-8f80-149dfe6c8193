import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:provider/provider.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rxdart/rxdart.dart';

part 'contacts_list_event.dart';
part 'contacts_list_state.dart';

class ContactsBloc extends Bloc<ContactslistEvent, ContactsState> {
  static const _PAGE_SIZE = 30;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;
  AppModelsNS appModels;

  ContactsBloc({
    required ModelsChannelBloc modelsChannel,
    required this.appModels,
  }) : super(ContactsState()) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((state) {
      if (state is ModelsChannelCreatedState<ContactDto> || state is ModelsChannelUpdatedState<ContactDto>) {
        this.add(ContactslistOnRefresh());
      } else {
        // NADA
      }
    });
  }

  factory ContactsBloc.create(BuildContext context) {
    return ContactsBloc(
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      appModels: Provider.of<AppModelsNS>(context, listen: false),
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _modelsChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<ContactslistEvent, ContactsState>> transformEvents(
    Stream<ContactslistEvent> events,
    TransitionFunction<ContactslistEvent, ContactsState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ContactsState> mapEventToState(ContactslistEvent event) async* {
    if (event is ContactslistOnRefresh) {
      yield* _mapOnRefreshToState(state, event);
    } else if (event is ContactslistOnFilterChanged) {
      yield* _mapOnFilterChangedToState(state, event);
    } else if (event is ContactslistOnFetch) {
      yield* _mapOnFetchToState(state, event);
    }
  }

  Stream<ContactsState> _mapOnRefreshToState(ContactsState state, ContactslistOnRefresh event) async* {
    try {
      final contacts = await _listContacts(filter: state.filter, offset: 0);
      yield state.copyWith(
        status: ContactsStatus.success,
        contacts: contacts,
        hasReachedMax: contacts.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: ContactsStatus.failure);
    }
  }

  Stream<ContactsState> _mapOnFilterChangedToState(ContactsState state, ContactslistOnFilterChanged event) async* {
    try {
      final contacts = await _listContacts(filter: event.filter, offset: 0);
      yield state.copyWith(
        status: ContactsStatus.success,
        filter: event.filter,
        contacts: contacts,
        hasReachedMax: contacts.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: ContactsStatus.failure);
    }
  }

  Stream<ContactsState> _mapOnFetchToState(ContactsState state, ContactslistOnFetch event) async* {
    if (state.hasReachedMax)
      yield state;
    else
      try {
        if (state.status == ContactsStatus.initial) {
          final contacts = await _listContacts(filter: state.filter, offset: 0);
          yield state.copyWith(
            status: ContactsStatus.success,
            contacts: contacts,
            hasReachedMax: contacts.length != _PAGE_SIZE,
          );
        } else {
          // Este punto sirve tanto en estado success como failure si se reintentase
          final contacts = await _listContacts(filter: state.filter, offset: state.contacts.length);
          yield contacts.isEmpty
              ? state.copyWith(hasReachedMax: true)
              : state.copyWith(
                  status: ContactsStatus.success,
                  contacts: List.of(state.contacts)..addAll(contacts),
                  hasReachedMax: false,
                );
        }
      } on Exception {
        yield state.copyWith(status: ContactsStatus.failure);
      }
  }

  Future<List<ContactDto>> _listContacts({
    ContactsListFilter filter = const ContactsListFilter(),
    int offset = 0,
  }) {
    return appModels.listContacts(
      filter: filter,
      offset: offset,
      limit: _PAGE_SIZE,
    );
  }
}
