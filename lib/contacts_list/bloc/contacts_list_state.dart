part of 'contacts_list_bloc.dart';

enum ContactsStatus { initial, success, failure }

class ContactsState extends Equatable {
  const ContactsState({
    this.status = ContactsStatus.initial,
    this.contacts = const <ContactDto>[],
    this.hasReachedMax = false,
    this.filter = const ContactsListFilter(),
  });

  // Actual status of the screen
  final ContactsStatus status;
  // The list of actual fetched contacts
  final List<ContactDto> contacts;
  // There is no more contacts to be fetched
  final bool hasReachedMax;
  //
  final ContactsListFilter filter;

  ContactsState copyWith({
    ContactsStatus? status,
    List<ContactDto>? contacts,
    bool? hasReachedMax,
    ContactsListFilter? filter,
  }) {
    return ContactsState(
      status: status ?? this.status,
      contacts: contacts ?? this.contacts,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      filter: filter ?? this.filter,
    );
  }

  @override
  List<Object> get props => [status, contacts];
}

class ContactsLoadSuccessLoadFailure extends ContactsState {}
