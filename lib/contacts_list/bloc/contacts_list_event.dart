part of 'contacts_list_bloc.dart';

abstract class ContactslistEvent extends Equatable {
  const ContactslistEvent();

  @override
  List<Object> get props => [];
}

class ContactslistOnRefresh extends ContactslistEvent {}

class ContactslistOnFetch extends ContactslistEvent {}

class ContactslistOnFilterChanged extends ContactslistEvent {
  final ContactsListFilter filter;
  const ContactslistOnFilterChanged(this.filter);
  @override
  List<Object> get props => [filter];
  @override
  String toString() => "ContactsFilterChanged { new filter: $filter }";
}

class ContactsListOnContactUpdated extends ContactslistEvent {
  final ContactDto contact;

  const ContactsListOnContactUpdated(this.contact);

  @override
  List<Object> get props => [contact];

  @override
  String toString() => "ContactUpdated { $contact }";
}
