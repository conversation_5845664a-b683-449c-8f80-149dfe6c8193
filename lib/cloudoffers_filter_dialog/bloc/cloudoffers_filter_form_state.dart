part of 'cloudoffers_filter_form_bloc.dart';

abstract class CloudoffersFilterFormState extends Equatable {
  final CloudoffersListFilter originalFilter;

  CloudoffersFilterFormState({this.originalFilter=const CloudoffersListFilter()}) : super();
  @override
  List<Object> get props => [];
}

class CloudoffersFilterFormLoadedState extends CloudoffersFilterFormState {
  final CloudoffersFilterFormModel formData;
  CloudoffersFilterFormLoadedState({required this.formData, required CloudoffersListFilter originalFilter})
      : super(originalFilter: originalFilter);

  CloudoffersFilterFormLoadedState copyWith({CloudoffersFilterFormModel? formData}) {
    return CloudoffersFilterFormLoadedState(
      formData: formData ?? this.formData,
      originalFilter: this.originalFilter,
    );
  }

  @override
  List<Object> get props => [formData];
}

class CloudoffersFilterFormFailureState extends CloudoffersFilterFormState {
  final String error;
  CloudoffersFilterFormFailureState({required this.error, required CloudoffersListFilter originalFilter})
      : super(originalFilter: originalFilter);
  @override
  List<Object> get props => [error];
}

class CloudoffersListFilterInitState extends CloudoffersFilterFormState {}
