import 'dart:async';

import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/cloudoffers_filter_dialog/cloudoffers_filter.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

part 'cloudoffers_filter_form_event.dart';
part 'cloudoffers_filter_form_state.dart';

class CloudoffersFilterFormBloc extends Bloc<CloudoffersFilterFormEvent, CloudoffersFilterFormState> {
  final AppModelsNS appModels;

  CloudoffersFilterFormBloc({required this.appModels}) : super(CloudoffersListFilterInitState());

  factory CloudoffersFilterFormBloc.create(BuildContext context) {
    return CloudoffersFilterFormBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
    );
  }

  @override
  Stream<Transition<CloudoffersFilterFormEvent, CloudoffersFilterFormState>> transformEvents(
    Stream<CloudoffersFilterFormEvent> events,
    TransitionFunction<CloudoffersFilterFormEvent, CloudoffersFilterFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<CloudoffersFilterFormState> mapEventToState(CloudoffersFilterFormEvent event) async* {
    if (event is CloudoffersFilterFormOnLoadEvent) {
      yield* _mapOnLoad(state, event); // as CloudoffersFilterFormOnLoadEvent);
    }
  }

  Stream<CloudoffersFilterFormState> _mapOnLoad(
      CloudoffersFilterFormState state, CloudoffersFilterFormOnLoadEvent event) async* {
    try {
      final filterModel = event.filterModel;
      Optional<PropertyzoneDto> zone = None();
      if (filterModel.containerzoneId is! None) {
        final zones = await appModels.listPropertyzones(
          filter: PropertyzonesListFilter(
            id: filterModel.containerzoneId,
            parentId: const None(),
            includeParent: True,
          ),
          limit: 1,
        );
        if (zones.length != 0) {
          zone = Some(zones[0]);
        }
      }

      Optional<CityDto> city = None();

      if (filterModel.propertyAddrCityCode is! None) {
        final cities = await appModels.listCities(
          filter: CitiesListFilter(code: filterModel.propertyAddrCityCode),
          limit: 1,
        );
        if (cities.length != 0) {
          city = Some(cities[0]);
        }
      }

      yield CloudoffersFilterFormLoadedState(
          originalFilter: filterModel,
          formData: CloudoffersFilterFormModel(
            includeIndividual: filterModel.sourceIndividual == True || filterModel.sourceIndividual is None,
            includeProfessional: filterModel.sourceIndividual == False || filterModel.sourceIndividual is None,
            sourceContactPhone: filterModel.sourceContactPhone,
            onlyFavourites: filterModel.includeNotFavourites.vd(true) ? False : True,
            propertytypeCode: filterModel.propertytypeCode,
            propertyM2Min: filterModel.propertyM2Min,
            propertyM2Max: filterModel.propertyM2Max,
            createdAtIntervalCode: filterModel.createdAtInterval,
            createdAtMin: filterModel.createdAtMin,
            createdAtMax: filterModel.createdAtMax,
            rentAllowed: filterModel.rentAllowed,
            rentAmountMin: filterModel.rentAmountMin,
            rentAmountMax: filterModel.rentAmountMax,
            saleAllowed: filterModel.saleAllowed,
            saleAmountMin: filterModel.saleAmountMin,
            saleAmountMax: filterModel.saleAmountMax,
            zone: zone,
            city: city,
          ));
    } on Exception {
      yield CloudoffersFilterFormFailureState(originalFilter: event.filterModel, error: "Problemas al obtener datos");
    }
  }
}
