part of 'cloudoffers_filter_form_bloc.dart';

int _id = 0;

abstract class CloudoffersFilterFormEvent extends Equatable {
  const CloudoffersFilterFormEvent();

  @override
  List<Object> get props => [];
}

class CloudoffersFilterFormOnLoadEvent extends CloudoffersFilterFormEvent {
  final _myid = ++_id;

  final CloudoffersListFilter filterModel;

  CloudoffersFilterFormOnLoadEvent({required this.filterModel}) : super();

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}
