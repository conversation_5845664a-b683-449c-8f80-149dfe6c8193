import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/app_constants.dart';
import 'package:topbrokers/offer_edit/widgets/city_list_item.dart';
import 'package:topbrokers/propertyzoneslist/propertyzoneslist.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/cloudoffers_filter_dialog/cloudoffers_filter.dart';
import 'package:topbrokers/global/session_bloc_utils.dart';

class CloudoffersFilterForm extends StatefulWidget {
  final CloudoffersListFilter actualFilter;
  //final void Function(CloudoffersListFilter changedFilter) onAccept;
  final void Function(CloudoffersListFilter changedFilter) onChanged;

  final GlobalKey<FormState> formKey;

  CloudoffersFilterForm({
    Key? key,
    required this.formKey,
    required this.actualFilter,
    required this.onChanged,
  }) : super(key: key ?? AgentorKeys.offersFilterPage);

  @override
  _CloudoffersFilterFormState createState() => _CloudoffersFilterFormState();
}

class _CloudoffersFilterFormState extends State<CloudoffersFilterForm> {
  final api = Deps.solve<ApiServices>();

  //OffersFilterFormModel _formData;

  _CloudoffersFilterFormState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));
    late CloudoffersFilterFormBloc _formBloc;

    return BlocProvider<CloudoffersFilterFormBloc>(
      create: (context) {
        _formBloc = CloudoffersFilterFormBloc.create(context)
          ..add(CloudoffersFilterFormOnLoadEvent(filterModel: widget.actualFilter));
        return _formBloc;
      },
      child: BlocConsumer<CloudoffersFilterFormBloc, CloudoffersFilterFormState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          if (state is CloudoffersFilterFormFailureState) {
            return Center(
              child: Text(localizations.offersfilter_errLoading),
            );
          } else if (state is CloudoffersFilterFormLoadedState) {
            //this._formData = state.formData;
            return SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  _buildForm(context, state.formData, () {
                    widget.onChanged(_formDataToListFilter(state.formData, state.originalFilter));
                  }),
                ],
              ),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, CloudoffersFilterFormModel formData, void Function() onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.max,
      children: [
        _formEntries(context, formData, onChanged).buildForm(key: widget.formKey, context: context),
      ],
    );
  }

  CloudoffersListFilter _formDataToListFilter(
      CloudoffersFilterFormModel formData, CloudoffersListFilter originalFilter) {
    return originalFilter.copyWith(
      sourceIndividual: formData.includeIndividual && formData.includeProfessional
          ? None()
          : formData.includeIndividual
              ? True
              : False,
      sourceContactPhone: formData.sourceContactPhone,
      includeNotFavourites: (formData.onlyFavourites.vd(false)) ? False : True,
      includeFavourites: True,
      propertytypeCode: formData.propertytypeCode,
      propertyM2Min: formData.propertyM2Min,
      propertyM2Max: formData.propertyM2Max,
      createdAtInterval: formData.createdAtIntervalCode,
      createdAtMin: formData.createdAtMin,
      createdAtMax: formData.createdAtMax,
      rentAllowed: formData.rentAllowed,
      rentAmountMin: formData.rentAmountMin,
      rentAmountMax: formData.rentAmountMax,
      saleAllowed: formData.saleAllowed,
      saleAmountMin: formData.saleAmountMin,
      saleAmountMax: formData.saleAmountMax,
      containerzoneId: formData.zone.vn?.id ?? const None(),
      propertyAddrCityCode: formData.city.vn?.code ?? const None(),
    );
  }

  List<Widget> _formEntries(BuildContext context, CloudoffersFilterFormModel formData, Function() onChanged) {
    final localizations = context.getAppLocalizationsOrThrow();
    const c_any = "any";

    return [
      GroupEntry(
        //label: "Datos del anuncio",
        isSubgroup: true,
        children: [
          SimpleFieldEntry<bool>(
            label: localizations.cloudoffers_filter_includeIndividualLabel,
            getValue: () => formData.includeIndividual,
            setValue: (bool? v) {
              setState(() {
                final checked = v ?? true;
                formData.includeIndividual = checked;
                formData.includeProfessional = formData.includeProfessional || !checked; // Los 2 no pueden ser falsos
              });
              onChanged();
            },
            minLength: 3,
            maxLength: 10,
          ),
          SimpleFieldEntry<bool>(
            label: localizations.cloudoffers_filter_includeProfessionalLabel,
            getValue: () => formData.includeProfessional,
            setValue: (bool? v) {
              setState(() {
                final checked = v ?? true;
                formData.includeProfessional = checked;
                formData.includeIndividual = formData.includeIndividual || !checked; // Los 2 no pueden ser falsos
              });
              onChanged();
            },
            minLength: 3,
            maxLength: 10,
          ),
          if (Deps.solve<AppConstants>().zonesEnabled)
            CustomSelectFieldEntry<PropertyzoneDto>(
              label: localizations.offersfilter_zoneLabel,
              valueToString: (PropertyzoneDto? value) => value?.composedName ?? "",
              onSelect: () =>
                  selectPropertyzoneDialog(context, initialSelectedId: formData.zone.vn?.id ?? const None()),
              getValue: () => formData.zone.vn,
              setValue: (PropertyzoneDto? value) {
                this.setState(() => formData.zone = value == null ? None() : Some(value));
                onChanged();
              },
            )
          else
            SearchFieldEntry<CityDto>(
              label: localizations.offersfilter_cityLabel,
              getValue: () => formData.city.vn,
              setValue: (CityDto? value) {
                this.setState(() => formData.city = value == null ? None() : Some(value));
                onChanged();
              },
              onSearch: (String search) => api.getCities(filter: CitiesListFilter(search: Some(search))),
              itemBuilder: (context, city, {bool isSelected = false}) => CityListItem(city: city),
              valueToString: (city) => city?.label.vn?.localized ?? localizations.offersfilter_cityEmptyText,
            ),
          SelectFieldEntry<String>(
            label: localizations.offersfilter_typeLabel,
            getValue: () => formData.propertytypeCode.vn ?? c_any,
            setValue: (String? code) {
              setState(() {
                if (code == null || code == c_any) {
                  formData.propertytypeCode = None();
                } else {
                  formData.propertytypeCode = Some(code);
                }
              });
              onChanged();
            },
            options: [
              SelectOption(label: localizations.common_AnyLabel, value: c_any),
              SelectOption(label: localizations.common_HouseLabel, value: "house"),
              SelectOption(label: localizations.common_FlatLabel, value: "flat"),
            ],
          ),
          SimpleFieldEntry<double>(
            label: localizations.offersfilter_minM2Label,
            getValue: () => formData.propertyM2Min.vn,
            setValue: (double? v) {
              setState(() {
                formData.propertyM2Min = v == null ? None() : Some(v);
              });
              onChanged();
            },
            max: formData.propertyM2Max.vn,
            isM2: true,
          ),
          SimpleFieldEntry<double>(
            label: localizations.offersfilter_maxM2Label,
            getValue: () => formData.propertyM2Max.vn,
            setValue: (double? v) {
              setState(() {
                formData.propertyM2Max = v == null ? None() : Some(v);
              });
              onChanged();
            },
            min: formData.propertyM2Min.vn ?? 0,
            isM2: true,
          ),
          SelectFieldEntry<String>(
            label: localizations.offersfilter_operationLabel,
            getValue: () => formData.saleAllowed == True
                ? "sale"
                : formData.rentAllowed == True
                    ? "rent"
                    : "sale", //c_any,
            setValue: (String? code) {
              setState(() {
                if (code == "sale") {
                  formData.saleAllowed = True;
                  formData.rentAllowed = False;
                  formData.rentAmountMin = const None();
                  formData.rentAmountMax = const None();
                } else if (code == "rent") {
                  formData.saleAllowed = False;
                  formData.saleAmountMin = const None();
                  formData.saleAmountMax = const None();
                  formData.rentAllowed = True;
                } else {
                  formData.saleAllowed = const None();
                  formData.saleAmountMin = const None();
                  formData.saleAmountMax = const None();
                  formData.rentAllowed = const None();
                  formData.rentAmountMin = const None();
                  formData.rentAmountMax = const None();
                }
              });
              onChanged();
            },
            options: [
              //SelectOption(label: localizations.common_AnyLabel, value: c_any),
              SelectOption(label: localizations.common_SaleLabel, value: "sale"),
              SelectOption(label: localizations.common_RentLabel, value: "rent"),
            ],
            isRequired: true,
          ),
          if (formData.saleAllowed == True)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_saleMinAmountLabel,
              getValue: () => formData.saleAmountMin.vn,
              setValue: (double? v) {
                setState(() {
                  formData.saleAmountMin = v == null ? None() : Some(v);
                });
                onChanged();
              },
              max: formData.saleAmountMax.vn,
              isCurrency: true,
            ),
          if (formData.saleAllowed == True)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_saleMaxAmountLabel,
              getValue: () => formData.saleAmountMax.vn,
              setValue: (double? v) {
                setState(() {
                  formData.saleAmountMax = v == null ? None() : Some(v);
                });
                onChanged();
              },
              min: formData.saleAmountMin.vn ?? 0,
              isCurrency: true,
            ),
          if (formData.rentAllowed == True)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_rentMinAmountLabel,
              getValue: () => formData.rentAmountMin.vn,
              setValue: (double? v) {
                setState(() {
                  formData.rentAmountMin = v == null ? None() : Some(v);
                });
                onChanged();
              },
              max: formData.rentAmountMax.vn,
              isCurrency: true,
            ),
          if (formData.rentAllowed == True)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_rentMaxAmountLabel,
              getValue: () => formData.rentAmountMax.vn,
              setValue: (double? v) {
                setState(() {
                  formData.rentAmountMax = v == null ? None() : Some(v);
                });
                onChanged();
              },
              min: formData.rentAmountMin.vn ?? 0,
              isCurrency: true,
            ),
        ],
      ),
      GroupEntry(
        //label: "Metainformación del anuncio",
        isSubgroup: true,
        children: [
          if (!context.imIndividual())
            SimpleFieldEntry<bool>(
              label: localizations.cloudoffers_filter_favoritesOnlyLabel,
              getValue: () => formData.onlyFavourites.vn ?? false,
              setValue: (bool? v) {
                setState(() {
                  if (v ?? false) {
                    formData.onlyFavourites = True;
                  } else {
                    formData.onlyFavourites = None();
                  }
                });
                onChanged();
              },
            ),
          SimpleFieldEntry<String>(
            label: localizations.cloudoffers_filter_sourceContactPhoneLabel,
            getValue: () => formData.sourceContactPhone.vn ?? "",
            setValue: (String? v) {
              setState(() {
                formData.sourceContactPhone = (v != null && v != "") ? Some(v) : None();
              });
              onChanged();
            },
            minLength: 6,
            onlyDigits: true,
          ),
          SelectFieldEntry<CloudOffersListDateIntervalCode>(
              label: localizations.cloudoffers_filter_publicationDateLabel,
              getValue: () => formData.createdAtIntervalCode.vn,
              setValue: (CloudOffersListDateIntervalCode? code) {
                setState(() {
                  if (code == null)
                    formData.createdAtIntervalCode = None();
                  else
                    formData.createdAtIntervalCode = Some(code);
                });
                onChanged();
              },
              options: [
                SelectOption(
                    label: localizations.cloudoffers_filter_publicationDate_manually,
                    value: CloudOffersListDateIntervalCode.between_two_dates),
                SelectOption(
                    label: localizations.cloudoffers_filter_publicationDate_last48h,
                    value: CloudOffersListDateIntervalCode.last_48h),
                SelectOption(
                    label: localizations.cloudoffers_filter_publicationDate_thisWeek,
                    value: CloudOffersListDateIntervalCode.this_week),
                SelectOption(
                    label: localizations.cloudoffers_filter_publicationDate_lastWeek,
                    value: CloudOffersListDateIntervalCode.last_week),
                SelectOption(
                    label: localizations.cloudoffers_filter_publicationDate_thisMonth,
                    value: CloudOffersListDateIntervalCode.this_month),
                SelectOption(
                    label: localizations.cloudoffers_filter_publicationDate_lastMonth,
                    value: CloudOffersListDateIntervalCode.last_month),
              ],
              hintText: localizations.cloudoffers_filter_publicationDate_hint),
          if (formData.createdAtIntervalCode.vn == CloudOffersListDateIntervalCode.between_two_dates)
            SimpleFieldEntry<DateTime>(
              label: localizations.cloudoffers_filter_publicationDate_from,
              getValue: () => formData.createdAtMin.vn,
              setValue: (DateTime? v) {
                setState(() {
                  formData.createdAtMin = v == null ? None() : Some(v);
                });
                onChanged();
              },
              max: formData.createdAtMax.vn,
              isDateOnly: true,
            ),
          if (formData.createdAtIntervalCode.vn == CloudOffersListDateIntervalCode.between_two_dates)
            SimpleFieldEntry<DateTime>(
              label: localizations.cloudoffers_filter_publicationDate_to,
              getValue: () => formData.createdAtMax.vn,
              setValue: (DateTime? v) {
                setState(() {
                  formData.createdAtMax = v == null ? None() : Some(v);
                });
                onChanged();
              },
              min: formData.createdAtMin.vn,
              isDateOnly: true,
            ),
        ],
      ),
    ];
  }
}
