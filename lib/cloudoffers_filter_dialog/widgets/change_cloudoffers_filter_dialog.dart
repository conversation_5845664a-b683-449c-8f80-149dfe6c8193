import 'dart:math';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/cloudoffers_filter_dialog/cloudoffers_filter.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/common/helpers.dart';

Future<CloudoffersListFilter?> changeCloudoffersFilterDialog(
  context, {
  CloudoffersListFilter filter = const CloudoffersListFilter(),
}) {
  final width = MediaQuery.of(context).size.width;
  
  return showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    pageBuilder: (
      BuildContext context,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
      return ChangeNotifierProvider<_ChangeOffersFilterModel>(
        create: (_) => _ChangeOffersFilterModel(),
        child: Consumer<_ChangeOffersFilterModel>(builder: (context, filterModel, child) {
          final apploc = context.apploc;
          return AlertDialog(
            insetPadding: EdgeInsets.all(5.0),
            actionsAlignment: MainAxisAlignment.spaceEvenly,
            contentPadding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
            content: SingleChildScrollView(
              child: Container(
                width: min(492, width),
                // Ojo a esto: Fijamos una altgura en pixels en la que quepa todo
                // el forumario.
                // Si el valor es demasiado pequeño:
                //    - En web para escritorio no hay problema (internamente "crece" hasta poder mostrar todo el contenido).
                //    - En web para smartphone (ios/android web) el contenido "excesivo" no es alcanzable por scroll (Se puede "tirar" del sinlechildscrollview con el dedo y ver... pero al soltar se vuelve a ir fuera de la vista )
                height: 800,

                child: Padding(
                  padding: EdgeInsets.all(5.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: CloudoffersFilterForm(
                          formKey: _formKey,
                          actualFilter: filter,
                          onChanged: (changedFilter) {
                            filter = changedFilter;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              TextButton.icon(
                icon: Icon(Icons.check),
                label: Text(apploc.common_Accept),
                onPressed: () {
                  if (_formKey.currentState?.validate() ?? false) {
                    _formKey.currentState!.save();
                    Navigator.pop(context, filter);
                  }
                },
              ),
              TextButton(
                child: Text(apploc.common_Cancel),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          );
        }),
      );
    },
  );
}

class _ChangeOffersFilterModel extends ChangeNotifier {
  OffersListFilter filter;

  _ChangeOffersFilterModel({
    this.filter = const OffersListFilter(),
  }) : super();

  void changeFilter(OffersListFilter newFilter) {
    filter = newFilter;
    notifyListeners();
  }
}
