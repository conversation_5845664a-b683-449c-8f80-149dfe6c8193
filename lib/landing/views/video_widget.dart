import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class VideoWidget extends StatefulWidget {
  final String url;
  VideoWidget({required this.url}) : super() {
    print(this.url);
  }

  @override
  _VideoWidgetState createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  VideoPlayerController? _controller;
  ChewieController? _chewieController;
  bool inError = false;
  @override
  void initState() {
    super.initState();
    initializePlayer();
  }

  Future<void> initializePlayer() async {
    try {
      _controller = VideoPlayerController.network(widget.url);
      await Future.wait([_controller!.initialize()]);
      
      _chewieController = ChewieController(
        videoPlayerController: _controller!,
        autoPlay: false,
        autoInitialize: true,
        showControlsOnInitialize: false,
        showOptions: false,
        allowFullScreen: false,
       
        errorBuilder: ( (context, error){
          inError=true;
          print("Error:"); print(error);
          return Text("");
        }),
      );
      
      setState(() {});
     
    } catch (e) {
      print(e);
      inError = true;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: _chewieController?.videoPlayerController.value.isInitialized ?? false
          ? AspectRatio(
              aspectRatio: _controller!.value.aspectRatio,
              child: Chewie(
                controller: _chewieController!,
              ),
            )
          : inError
              ? Text("")
              : Padding(
                  padding: EdgeInsets.all(48),
                  child: Container(
                    child: CircularProgressIndicator(),
                  ),
                ),
    );
  }

  @override
  void dispose() {
    try {
      _chewieController?.dispose();
      _controller?.dispose();
    } finally {
      super.dispose();
    }
  }
}
