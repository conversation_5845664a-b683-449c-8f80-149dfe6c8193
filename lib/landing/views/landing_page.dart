import 'package:agentor_utils/agentor_utils.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/landing/views/buyer_content.dart';
import 'package:flutter/material.dart';
import 'package:topbrokers/landing/views/landing_content_template.dart';
import 'package:topbrokers/landing/views/seller_content.dart';

enum _ContentType { buyer, seller }

class LandingPage extends StatefulWidget {
  const LandingPage({Key? key}) : super(key: key ?? AgentorKeys.loginPage);

  @override
  State<StatefulWidget> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: Colors.white,
        body: BuyerContent(),
      );
}
