import 'dart:math';

import 'package:agentor_deps/agentor_deps.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/app_config.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/responsive.dart';
import 'package:topbrokers/landing/views/video_widget.dart';
import 'package:topbrokers/routes.dart';
import 'package:agentor_utils/agentor_utils.dart';
import 'package:url_launcher/link.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

final _textStyles_W = Map<String, TextStyle>()
  ..addEntries([
    MapEntry("h1", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 40, fontWeight: FontWeight.w700, height: 1.05))),
    MapEntry("h2", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 24, fontWeight: FontWeight.w700, height: 1.50))),
    MapEntry("h3", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, height: 1.50))),
  ]);
final _textStyles_S = Map<String, TextStyle>()
  ..addEntries([
    MapEntry("h1", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 28, fontWeight: FontWeight.w700, height: 1.05))),
    MapEntry("h2", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 18, fontWeight: FontWeight.w400, height: 1.50))),
    MapEntry("h3", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, height: 1.50))),
  ]);

final _tipFontStyles = Map<String, TextStyle>()
  ..addEntries([
    MapEntry("h3", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w700, height: 1.50))),
    MapEntry("p", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w300, height: 1.50))),
  ]);

class LandingTip {
  final String assetUrl;
  final String title;
  final String text;
  const LandingTip({required this.assetUrl, required this.title, required this.text});
}

class LandingAction {
  final String text;
  final Function() onTap;
  LandingAction({required this.text, required this.onTap});
}

class LandingContentTemplate extends StatelessWidget {
  final String title;
  final String videoUrl;
  final List<LandingTip> tips;
  final List<LandingAction> bottomLinks;

  LandingContentTemplate({required this.title, required this.videoUrl, this.tips = const [], this.bottomLinks = const []});

  @override
  Widget build(BuildContext context) {
    const double LEFT_RIGHT_PADDING = 12;
    const double COLUMN_Width = 320; //20;
    const double LEFT_IMAGE_Width = 0;
    const double SZ_W = LEFT_IMAGE_Width + LEFT_RIGHT_PADDING + COLUMN_Width + LEFT_RIGHT_PADDING;

    final sz = MediaQuery.of(context).size;

    Row buildEntryBloc(BuildContext context) => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              //constraints: BoxConstraints(maxWidth: COLUMN_Width), // 360),
              constraints: BoxConstraints(
                minWidth: sz.width - LEFT_RIGHT_PADDING * 2,
                maxWidth: sz.width - LEFT_RIGHT_PADDING * 2,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Column(
                    children: [
                      Container(
                        alignment: Alignment.topCenter,
                        child: Image(
                          image: AssetImage('images/topbrokers_io_black.png'),
                          //fit: BoxFit.contain,
                          alignment: FractionalOffset.topCenter,
                          height: 40,
                          width: COLUMN_Width * 0.75,
                          //width: CONTENT_Width, //double.infinity,
                          filterQuality: FilterQuality.high,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        this.title,
                        textAlign: TextAlign.center,
                        style: _textStyles_W["h2"]?.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(10),
                        constraints: BoxConstraints(
                          //minWidth: sz.width - LEFT_RIGHT_PADDING * 2,
                          maxWidth: min(520, sz.width - LEFT_RIGHT_PADDING * 2),
                        ),
                        child: Image(
                          image: AssetImage('images/landing_image.png'),
                          //fit: BoxFit.cover,
                          fit: BoxFit.fitHeight,
                          alignment: FractionalOffset.center,
                          width: min(COLUMN_Width, 320),
                          height: min(COLUMN_Width, 320),
                        ),

                        // https://flutter.github.io/assets-for-api-docs/assets/videos/bee.mp4'),
                      ),
                      const SizedBox(height: 32),
                      Flex(
                        direction: Axis.vertical,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _btnSignUp(context),
                          const SizedBox(height: 12, width: 12),
                          _btnSignIn(context),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );

    Row buildTopicsBloc(BuildContext context) => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              //color: Colors.orange,
              constraints: BoxConstraints(
                minWidth: min(sz.width - LEFT_RIGHT_PADDING * 2, COLUMN_Width),
                maxWidth: min(sz.width - LEFT_RIGHT_PADDING * 2, COLUMN_Width * 3),
              ),
              child: Column(
                children: [
                  //const SizedBox(height: 32),
                  Responsive(
                    nColumns: sz.width <= SZ_W * 1.5
                        ? 1
                        : sz.width <= SZ_W * 2
                            ? 2
                            : 3,
                    children: buildTipsWidgets(context),
                  ),
                  //const SizedBox(height: 32),
                ],
              ),
            ),
          ],
        );

    return Container(
      height: double.infinity,
      color: Color.fromRGBO(48, 193, 238, 255).withAlpha(255),
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          SingleChildScrollView(
            child: Container(
              alignment: Alignment.topCenter,
              //constraints: BoxConstraints(maxWidth: 512),
              padding: const EdgeInsets.fromLTRB(LEFT_RIGHT_PADDING, 12, LEFT_RIGHT_PADDING, 12),
              //color: Colors.green, //Colors.white.withOpacity(0),
              child: Column(
                children: [
                  buildEntryBloc(context),
                  const SizedBox(height: 32),
                  buildTopicsBloc(context),
                  const SizedBox(height: 32),
                  buildBottomBloc(context),
                  const SizedBox(height: 32),
                  buildBottomLinksBloc(context),
                  const SizedBox(height: 64),
                  Divider(height: 12),
                  HTML.toRichText(
                    context,
                    '@${DateTime.now().year} topbrokers.io <a href="${Deps.solve<AppConfig>().webserverUrl}aviso-legal">${context.apploc.landing_legalNotice}</a>',
                    linksCallback: (lnk) async {
                      if (await canLaunchUrlString(lnk)) {
                        await launchUrlString(lnk);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> buildTipsWidgets(BuildContext context) {
    Widget buildTipWidget({required String assetUrl, required String title, required String text}) {
      return Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          //crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image(
              image: AssetImage(assetUrl),
              width: 48,
            ),
            const SizedBox(height: 6),
            Text.rich(
              TextSpan(
                text: title,
                style: _tipFontStyles["h3"],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text.rich(
              TextSpan(
                text: text,
                style: _tipFontStyles["p"],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
          ],
        ),
      );
    }

    return this
        .tips
        .map((tip) => buildTipWidget(
              assetUrl: tip.assetUrl,
              title: tip.title,
              text: tip.text,
            ))
        .toList();
  }

  Widget buildBottomBloc(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
      ),
    );
  }

  Widget buildBottomLinksBloc(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: this
          .bottomLinks
          .map(
            (bottomLink) => ElevatedButton(
              onPressed: bottomLink.onTap,
              child: Text(bottomLink.text, style: TextStyle(fontSize: 18)),
              style: ElevatedButton.styleFrom(
                side: BorderSide(color: Colors.black, width: 1),
                elevation: 2,
                minimumSize: Size(350, 50),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _btnSignUp(BuildContext context) => ElevatedButton(
        onPressed: () {
          Navigator.pushNamed(context, AgentorRoutes.signUp);
        },
        child: Text(context.apploc.landing_signUp, style: TextStyle(fontSize: 18)),
        style: ElevatedButton.styleFrom(
          side: BorderSide(color: Colors.black, width: 1),
          elevation: 2,
          minimumSize: Size(350, 50),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        ),
      );
  Widget _btnSignIn(BuildContext context) => ElevatedButton(
        onPressed: () {
          Navigator.pushNamed(context, AgentorRoutes.signIn);
        },
        child: Text(context.apploc.landing_logIn, style: TextStyle(fontSize: 18)),
        style: ElevatedButton.styleFrom(
          onPrimary: Theme.of(context).primaryColorDark,
          primary: Colors.white,
          side: BorderSide(color: Colors.black, width: 1),
          elevation: 2,
          minimumSize: Size(350, 50),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        ),
      );
}
