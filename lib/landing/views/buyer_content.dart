import 'package:topbrokers/landing/views/landing_content_template.dart';
import 'package:agentor_deps/agentor_deps.dart';
import 'package:topbrokers/app_config.dart';
import 'package:agentor_utils/agentor_utils.dart';

class BuyerContent extends LandingContentTemplate {
  BuyerContent({List<LandingAction> actions = const []})
      : super(
          title: const {
            "default": "App",
            "es": "App",
          }.localized,
          videoUrl: "${Deps.solve<AppConfig>().webserverUrl}assets/images/landing_image.png",
          tips: [
            
          ],
          bottomLinks: actions,
        );
}
