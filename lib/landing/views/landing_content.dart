import 'dart:math';

import 'package:agentor_deps/agentor_deps.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/app_config.dart';

import 'package:topbrokers/common/widgets/responsive.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher_string.dart';

final _textStyles_W = Map<String, TextStyle>()
  ..addEntries([
    MapEntry("h1", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 40, fontWeight: FontWeight.w700, height: 1.05))),
    MapEntry("h2", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 22, fontWeight: FontWeight.w400, height: 1.50))),
    MapEntry("h3", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 18, fontWeight: FontWeight.w400, height: 1.50))),
  ]);
final _textStyles_S = Map<String, TextStyle>()
  ..addEntries([
    MapEntry("h1", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 28, fontWeight: FontWeight.w700, height: 1.05))),
    MapEntry("h2", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 18, fontWeight: FontWeight.w400, height: 1.50))),
    MapEntry("h3", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, height: 1.50))),
  ]);

class LandingAgentContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    const double W_Width = 480;
    const double LEFT_RIGHT_PADDING = 12;
    const double COLUMN_Width = 320; //20;
    const double LEFT_IMAGE_Width = 0;
    const double SZ_W = LEFT_IMAGE_Width + LEFT_RIGHT_PADDING + COLUMN_Width + LEFT_RIGHT_PADDING;

    final sz = MediaQuery.of(context).size;
    final isW = sz.width > W_Width;
    final styles = isW ? _textStyles_W : _textStyles_S;

    Row buildEntryBloc(BuildContext context) => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              constraints: BoxConstraints(maxWidth: COLUMN_Width), // 360),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Column(
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: Image(
                          image: AssetImage('images/topbrokers_io_black.png'),
                          //fit: BoxFit.contain,
                          alignment: FractionalOffset.centerLeft,
                          height: 40,
                          width: 320,
                          //width: CONTENT_Width, //double.infinity,
                          filterQuality: FilterQuality.high,
                        ),
                      ),
                      const SizedBox(height: 54),
                      Container(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          width: 320,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text.rich(
                                TextSpan(text: 'Personal', style: _textStyles_W["h1"]),
                                textAlign: TextAlign.left,
                              ),
                              Text.rich(
                                TextSpan(text: 'Real Estate', style: _textStyles_W["h1"]),
                                textAlign: TextAlign.left,
                              ),
                              Text.rich(
                                TextSpan(text: 'Platform', style: _textStyles_W["h1"]),
                                textAlign: TextAlign.left,
                              ),
                              Text.rich(
                                TextSpan(text: '', style: _textStyles_W["h2"]),
                                textAlign: TextAlign.left,
                              ),
                              Text.rich(
                                TextSpan(text: 'Únete hoy y sube tu negocio.', style: _textStyles_W["h2"]),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Flex(
                        direction: Axis.vertical,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _btnSignUp(context),
                          const SizedBox(height: 12, width: 12),
                          _btnSignIn(context),
                        ],
                      ),
                      const SizedBox(height: 32),
                      Image(
                        image: AssetImage('images/landing_image.png'),
                        //fit: BoxFit.cover,
                        fit: BoxFit.fitHeight,
                        alignment: FractionalOffset.center,
                        width: min(COLUMN_Width, 320),
                        height: min(COLUMN_Width, 320),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );

    Row buildTopicsBloc(BuildContext context) => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              //color: Colors.orange,
              constraints: BoxConstraints(
                minWidth: min(sz.width - LEFT_RIGHT_PADDING * 2, COLUMN_Width),
                maxWidth: min(sz.width - LEFT_RIGHT_PADDING * 2, COLUMN_Width * 3),
              ),
              child: Column(children: [
                const SizedBox(height: 32),
                Text.rich(
                  TextSpan(
                    text: 'Simplicidad y potencia',
                    style: styles["h1"],
                  ),
                  textAlign: TextAlign.center,
                ),
                Text.rich(
                  TextSpan(
                    text: 'Descubre todo lo que puedes hacer con Topbrokers, estos son nuestros favoritos.',
                    style: styles["h2"],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Responsive(
                  nColumns: sz.width <= SZ_W * 1.5
                      ? 1
                      : sz.width <= SZ_W * 2
                          ? 2
                          : 3,
                  children: listTips(context),
                ),
                const SizedBox(height: 32),
                Image(
                  image: AssetImage('images/landing/ellipsis-50.png'),
                ),
              ]),
            ),
          ],
        );

    return Container(
      height: double.infinity,
      //color: Colors.red,
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          SingleChildScrollView(
            child: Container(
              alignment: Alignment.topCenter,
              //constraints: BoxConstraints(maxWidth: 512),
              padding: const EdgeInsets.fromLTRB(LEFT_RIGHT_PADDING, 60, LEFT_RIGHT_PADDING, 32),
              //color: Colors.green, //Colors.white.withOpacity(0),
              child: Column(
                children: [
                  buildEntryBloc(context),
                  buildTopicsBloc(context),
                  Divider(
                    height: 12,
                  ),
                  HTML.toRichText(
                    context,
                    '@2021 topbrokers.io <a href="${Deps.solve<AppConfig>().webserverUrl}aviso-legal">Aviso legal</a>',
                    linksCallback: (lnk) async {
                      if (await canLaunchUrlString(lnk)) {
                        await launchUrlString(lnk);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> listTips(BuildContext context) {
    final styles = Map<String, TextStyle>()
      ..addEntries([
        MapEntry(
            "h3", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w700, height: 1.50))),
        MapEntry(
            "p", GoogleFonts.poppins(textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w300, height: 1.50))),
      ]);

    Widget tip({required String assetUrl, required String title, required String text}) {
      return Padding(
        padding: EdgeInsets.all(10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          //crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image(
              image: AssetImage(assetUrl),
            ),
            const SizedBox(height: 6),
            Text.rich(
              TextSpan(
                text: title,
                style: styles["h3"],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text.rich(
              TextSpan(
                text: text,
                style: styles["p"],
              ),
              textAlign: TextAlign.justify,
            ),
            const SizedBox(height: 12),
          ],
        ),
      );
    }

    return [
      tip(
        assetUrl: 'images/landing/01_private_space.webp',
        title: "Tu espacio privado",
        text:
            'Topbrokers te ofrece un espacio privado sólo para ti. No importa si desarrollas tu negocio de forma independiente o vinculado a una agencia, sólo tu tienes acceso a la información.',
      ),
      tip(
        assetUrl: 'images/landing/02_your_opportunities.webp',
        title: "Controla tus oportunidades",
        text:
            'Con Topbrokers tendrás una visión clara de tus oportunidades de negocio. Las Noticias, Ofertas y Demandas se muestran de una forma directa y sencilla.',
      ),
      tip(
        assetUrl: 'images/landing/03_bigdata.webp',
        title: "Bigdata",
        text:
            'Topbrokers incorpora miles de ofertas online de tu mercado. Tanto ofertas de agencias como de particulares. Realiza cruces con las demandas de tus clientes, seguimiento para realizar nuevas captaciones o haz comparativas de mercado de una forma ágil.',
      ),
      tip(
        assetUrl: 'images/landing/04_networking.webp',
        title: "Conéctate con otros profesionales",
        text:
            'Crea espacios de negocio compartido con otros profesionales y aumenta así tus oportunidades. Personaliza en cada espacio compartido el nivel de privacidad que necesites.',
      ),
      tip(
        assetUrl: 'images/landing/05_portals.webp',
        title: "Promociona tu cartera en portales",
        text:
            'Publica en segundos tu cartera en los principales portales inmobiliarios. Gestiona de forma directa los contactos que recibas.',
      ),
      tip(
        assetUrl: 'images/landing/06_matchings.webp',
        title: "Cruces entre oferta y demanda",
        text: 'Topbrokers te avisa de las nuevas oportunidades con tu cartera y con la oferta online.',
      ),
      tip(
        assetUrl: 'images/landing/07_schedule.webp',
        title: "Agenda",
        text: 'Planifica tu día a día con la agenda integrada. Ten un control de toda la actividad.',
      ),
      tip(
        assetUrl: 'images/landing/08_actions.webp',
        title: "Acciones Comerciales ",
        text:
            'Haz un seguimiento detallado de toda la actividad comercial relacionada con Ofertas y Demandas. Anota en segundos cualquier nueva interacción. Nada estará fuera de tu control.',
      ),
      tip(
        assetUrl: 'images/landing/09_mortage.webp',
        title: "Hipotecas 100%",
        text:
            'Consigue la mejor Hipoteca para tus clientes. No pierdas una venta por falta de financiación. Hasta el 100% de financiación con las mejores condiciones. Genera nuevos ingresos ofreciendo mejor servicio.',
      ),
      tip(
        assetUrl: 'images/landing/10_mobile.webp',
        title: "Experiencia mobile Top",
        text:
            'Topbrokers está pensado y diseñado para su uso en móvil. Úsalo sobre la marcha en cualquier lugar y situación.',
      ),
    ];
  }

  Widget _btnSignUp(BuildContext context) => ElevatedButton(
        onPressed: () {
          Navigator.pushNamed(context, AgentorRoutes.signUp);
        },
        child: Text("Regístrate", style: TextStyle(fontSize: 18)),
        style: ElevatedButton.styleFrom(
          side: BorderSide(color: Colors.black, width: 1),
          elevation: 2,
          minimumSize: Size(350, 50),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        ),
      );
  Widget _btnSignIn(BuildContext context) => ElevatedButton(
        onPressed: () {
          Navigator.pushNamed(context, AgentorRoutes.signIn);
        },
        child: Text("Iniciar sesión", style: TextStyle(fontSize: 18)),
        style: ElevatedButton.styleFrom(
          onPrimary: Theme.of(context).primaryColorDark,
          primary: Colors.white,
          side: BorderSide(color: Colors.black, width: 1),
          elevation: 2,
          minimumSize: Size(350, 50),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        ),
      );
}
