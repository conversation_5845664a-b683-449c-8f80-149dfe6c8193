import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/contact_long_link.dart';
import 'package:topbrokers/common/widgets/offer_long_link.dart';
import 'package:topbrokers/common/widgets/titled_card.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/serviceaction_sheet/models/serviceaction_sheet_page_params.dart';
import 'package:topbrokers/serviceaction_sheet/serviceaction_sheet.dart';

typedef OnSaveActionCallback = Function(ActionDto action);

const _c_lang = "es";
const _c_lineheight = 1.5;

class ServiceactionSheetPage extends StatefulWidget {
  //final OnSaveActionCallback onSave;
  final ServiceactionSheetPageParams params;
  //final ActionDto action;

  ServiceactionSheetPage({Key? key, required this.params}) : super(key: key ?? AgentorKeys.editActionPage);

  @override
  _ServiceactionSheetPageState createState() => _ServiceactionSheetPageState();
}

class _ServiceactionSheetPageState extends State<ServiceactionSheetPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        final params = this.widget.params;
        return ServiceactionSheetBloc.create(context)..add(ServiceactionSheetOnShowEvent(actionId: params.id));
      },
      child: BlocConsumer<ServiceactionSheetBloc, ServiceactionSheetState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is ServiceactionSheetLoaded) {
              return _buildPageScaffold(context, state);
            } else if (state is ServiceactionSheetLoadFailure) {
              return Center(child: Text(state.error));
            } else {
              return const Center(child: CircularProgressIndicator());
            }
          }),
    );
  }

  Scaffold _buildPageScaffold(BuildContext context, ServiceactionSheetLoaded state) {
    final localization = context.getAppLocalizationsOrThrow();
    return Scaffold(
      appBar: AppBar(
        title: Text(localization.serviceaction_title),
        centerTitle: true,
      ),
      body: WidthLimiter(
        child: BlocConsumer<ServiceactionSheetBloc, ServiceactionSheetState>(
          listener: (context, state) {},
          builder: (context, state) {
            // ActionformSaveFailure y ActionformValidationFailure
            // extienden ActionformLoaded

            return _buildSheet(
              context,
              (state as ServiceactionSheetLoaded).action,
              actiontypes: state.actiontypes,
            );
          },
        ),
      ),
    );
  }

  Widget _buildSheet(BuildContext context, ActionDto action, {required Iterable<ActiontypeDto> actiontypes}) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);
    final localizations = context.getAppLocalizationsOrThrow();
    return new Container(
      child: new SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            TitledCard(
              Text("Datos principales", style: textTheme.headline6),
              children: [
                RichText(
                  softWrap: true,
                  textAlign: TextAlign.left,
                  text: TextSpan(
                    text: "",
                    style: textStyle,
                    children: _addSeparation(
                      [
                        _buildCustomEntrySpan(
                          context: context,
                          labelText: localizations.serviceaction_typeLabel,
                          valueText: action.type.vn?.label.vn?.getByLang(_c_lang),
                        ),
                        if (action.when.vn != null)
                          _buildCustomEntrySpan(
                            context: context,
                            labelText: localizations.serviceaction_whenLabel,
                            valueText: action.when.vn?.toLocal().formatRelativeToNow() ?? "",
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            if (action.contact.vn != null)
              TitledCard(
                Text(localizations.serviceaction_contactLabel, style: textTheme.headline6),
                children: [
                  ContactLongLink(
                    contact: action.contact.v!,
                    textAlign: TextAlign.left,
                    isDense: false,
                  ),
                ],
              ),
            if (action.offer.vn != null)
              TitledCard(
                Text(localizations.serviceaction_offerLabel, style: textTheme.headline6),
                children: [
                  OfferLongLink(
                    offer: action.offer.v!,
                    textAlign: TextAlign.left,
                    isDense: false,
                  )
                ],
              ),
          ],
        ),
      ),
    );
  }

  List<InlineSpan> _addSeparation(List<Iterable<InlineSpan>?> texts) {
    return texts.fold(
      <InlineSpan>[],
      (allSpans, spans) {
        if (spans == null)
          return allSpans;
        else
          return allSpans
            ..addAll([
              if (allSpans.length != 0 && spans.length != 0) TextSpan(text: "\n"),
              if (spans.length != 0) TextSpan(text: "- "),
            ])
            ..addAll(spans);
      },
    );
  }

  Iterable<TextSpan> _buildCustomEntrySpan(
      {required BuildContext context, required String labelText, String? valueText}) {
    final textTheme = Theme.of(context).textTheme;
    final lbStyle = textTheme.bodyText2;
    final txtStyle = textTheme.bodyText1;
    final label = TextSpan(text: "$labelText: ".capitalize, style: lbStyle);
    if (valueText != null && valueText.length != 0) {
      return [label, TextSpan(text: "$valueText", style: txtStyle)];
    } else {
      return [];
    }
  }
}
