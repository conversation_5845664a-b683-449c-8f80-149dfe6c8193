part of 'serviceaction_sheet_bloc.dart';

class ServiceactionSheetState extends Equatable {
  @override
  List<Object> get props => [];
}

class ServiceactionSheetLoading extends ServiceactionSheetState {}

class ServiceactionSheetLoaded extends ServiceactionSheetState {
  final ActionDto action;
  final List<ActiontypeDto> actiontypes;

  ServiceactionSheetLoaded({required this.action, required this.actiontypes}) : super();

  @override
  List<Object> get props => [action, actiontypes.length];
}

class ServiceactionSheetLoadFailure extends ServiceactionSheetState {
  final String error;

  ServiceactionSheetLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

