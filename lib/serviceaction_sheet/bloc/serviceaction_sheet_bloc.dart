import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'serviceaction_sheet_event.dart';
part 'serviceaction_sheet_state.dart';

class ServiceactionSheetBloc extends Bloc<ServiceactionSheetEvent, ServiceactionSheetState> {
  final AppModelsNS appModels;

  ServiceactionSheetBloc({required this.appModels}) : super(ServiceactionSheetState());

  factory ServiceactionSheetBloc.create(BuildContext context) =>
      ServiceactionSheetBloc(appModels: Provider.of<AppModelsNS>(context, listen: false));

  @override
  Stream<Transition<ServiceactionSheetEvent, ServiceactionSheetState>> transformEvents(
    Stream<ServiceactionSheetEvent> events,
    TransitionFunction<ServiceactionSheetEvent, ServiceactionSheetState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ServiceactionSheetState> mapEventToState(ServiceactionSheetEvent event) async* {
    final state = this.state;
    if (event is ServiceactionSheetOnShowEvent) {
      yield* _mapOnEditEventToState(state, event);
    } 
  }

  Stream<ServiceactionSheetState> _mapOnEditEventToState(ServiceactionSheetState state, ServiceactionSheetOnShowEvent event) async* {
    try {
      final action = await _readAction(event.actionId);
      final actiontypes = await appModels.listActiontypes();
      if (action == null) {
        yield ServiceactionSheetLoadFailure(error: "La acción no existe");
      } else if(action.type is None || action.type.v.service is None) {
        yield ServiceactionSheetLoadFailure(error: "Falta información asociada al tipo de la acción");
      } else {
        yield ServiceactionSheetLoaded(action: action, actiontypes: actiontypes);
      }
    } on Exception {
      yield ServiceactionSheetLoadFailure(error: "Problemas obteniendo datos");
    }
  }


  Future<ActionDto?> _readAction(String id) async {
    return appModels.readAction(id);
  }
}
