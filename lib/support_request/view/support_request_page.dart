import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/app_config.dart';
import 'package:topbrokers/common/scafold_helper.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/support_request/bloc/support_request_bloc.dart';
import 'package:topbrokers/support_request/models/support_request_model.dart';
import 'package:url_launcher/url_launcher.dart';

typedef OnSaveContactCallback = Function(ContactDto contact);

class SupportRequestPage extends StatefulWidget {
  SupportRequestPage({Key? key}) : super(key: key ?? AgentorKeys.supportRequestPage);

  @override
  _SupportRequestPage createState() => _SupportRequestPage();
}

class _SupportRequestPage extends State<SupportRequestPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    //final localizations = context.getAppLocalizationsOrThrow();

    return BlocProvider(
      create: (context) => SupportRequestBloc.create(context)..add(SupportRequestOnLoad()),
      child: BlocConsumer<SupportRequestBloc, SupportRequestState>(
        listener: (context, state) {},
        builder: (context, state) {
          if (state is SupportRequestLoadedState) {
            return Scaffold(
              appBar: AppBar(
                title: Text("Soporte"),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: BlocConsumer<SupportRequestBloc, SupportRequestState>(
                  listener: (context, state) {
                    if (state is SupportRequestFailureState) {
                      context.showError(state.error);
                    }
                  },
                  builder: (context, state) {
                    final loadedState = state as SupportRequestLoadedState;
                    return _buildForm(context, loadedState.formData);
                  },
                ),
              ),
            );
          } else if (state is SupportRequestSuccessState) {
            final headline6 = Theme.of(context).textTheme.titleLarge;
            final bodyText2 = Theme.of(context).textTheme.bodyMedium;
            return Scaffold(
              appBar: AppBar(
                title: Text("Soporte"),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: Center(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      children: [
                        HTML.toRichText(
                          context,
                          '''<h1>¡Gracias por tu mensaje!</h1><br/><p>Te responderemos a tu mail lo antes posible.</p><p>Equipo Topbrokers.</p>''',
                          overrideStyle: Map<String, TextStyle>()
                            ..addEntries([
                              if (headline6 != null) MapEntry("h1", headline6),
                              if (bodyText2 != null) MapEntry("p", bodyText2),
                            ]),
                        ),
                        const SizedBox(height: 32),
                        ElevatedButton.icon(
                          icon: Icon(Icons.check_circle),
                          label: Text("cerrar"),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            elevation: 2,
                            minimumSize: Size(200, 50),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          } else if (state is SupportRequestFailureState) {
            return Center(
              child: Text(state.error),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, SupportRequestModel data) {
    final headline6 = Theme.of(context).textTheme.titleLarge;
    final bodyText2 = Theme.of(context).textTheme.bodyMedium;
    return Center(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(32),
        child: Column(children: [
          HTML.toRichText(
            context,
            '''<h1>¿En que podemos ayudarte?</h1>''',
            overrideStyle: Map<String, TextStyle>()
              ..addEntries([
                if (headline6 != null) MapEntry("h1", headline6),
                if (bodyText2 != null) MapEntry("p", bodyText2),
              ]),
          ),
          _buildEntries(context, data).buildForm(context: context, key: _formKey),
          SizedBox.fromSize(size: Size.fromHeight(32)),
          ElevatedButton.icon(
            icon: Icon(Icons.send),
            style: ElevatedButton.styleFrom(
              elevation: 2,
              minimumSize: Size(200, 50),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
            ),
            onPressed: () {
              final formState = _formKey.currentState;
              if (formState != null && formState.validate()) {
                BlocProvider.of<SupportRequestBloc>(context).add(SupportRequestOnSend(formData: data));
              }
            },
            label: Text("Enviar"),
          ),
          SizedBox.fromSize(size: Size.fromHeight(32)),
          HTML.toRichText(
            context,
            'Puedes descargar nuestra ayuda <a href="${Deps.solve<AppConfig>().webserverUrl}tb_help.pdf">aquí</a>.',
            linksCallback: (lnk) async {
              if (await canLaunch(lnk)) await launch(lnk);
            },
            defaultTextStyle: Theme.of(context).textTheme.bodySmall,
          )
        ]),
      ),
    );
  }

  List<FormEntry> _buildEntries(BuildContext context, SupportRequestModel data) {
    //final localizations = AppLocalizations.of(context);
    return <FormEntry>[
      SimpleFieldEntry<String>(
        isMultiline: true,
        getValue: () => data.answere,
        setValue: (String? v) {
          if (v != null) // En realidad, siempre !=null debido a que isRequired:true
            setState(() {
              data.answere = v;
            });
        },
        isRequired: true,
      ),
    ];
  }
}
