part of 'support_request_bloc.dart';

int _counter = 0;

class SupportRequestState extends Equatable {
  final _instanceId = ++_counter;

  @override
  List<Object> get props => [_instanceId];
}

/// En preparación (cargando)
class SupportRequestInitialState extends SupportRequestState {}

class SupportRequestFinalState extends SupportRequestState {}

/// Todo listo para trabajar en la vista
class SupportRequestLoadedState extends SupportRequestState {
  final SupportRequestModel formData;

  SupportRequestLoadedState({required this.formData});

  SupportRequestLoadedState copyWith({SupportRequestModelStep? step, SupportRequestModel? formData}) {
    return SupportRequestLoadedState(formData: formData ?? this.formData);
  }
}

/// Hay un error en el formulario actual
class SupportRequestFailureState extends SupportRequestLoadedState {
  final String error;

  SupportRequestFailureState({
    required this.error,
    required SupportRequestModel formData,
  }) : super(formData: formData);

  factory SupportRequestFailureState.fromLoadedState(SupportRequestLoadedState src, {required String error}) {
    return SupportRequestFailureState(error: error, formData: src.formData);
  }
  @override
  List<Object> get props => [_instanceId, error];
}

/// Todo ha ido OK
class SupportRequestSuccessState extends SupportRequestState {}
