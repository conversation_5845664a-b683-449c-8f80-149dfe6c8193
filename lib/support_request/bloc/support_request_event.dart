part of 'support_request_bloc.dart';

abstract class SupportRequestEvent extends Equatable {
  const SupportRequestEvent();

  @override
  List<Object> get props => [];
}

class SupportRequestOnLoad extends SupportRequestEvent {
  SupportRequestOnLoad() : super();
}

///
///  Avanzar al siguiente paso del "wizzard"
///
class SupportRequestOnSend extends SupportRequestEvent {
  final SupportRequestModel formData;
  SupportRequestOnSend({required this.formData}) : super();
}
