import 'package:agentor_deps/agentor_deps.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/support_request/models/support_request_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

part 'support_request_event.dart';
part 'support_request_state.dart';

///
/// Gestiona estados y eventos para el formulario de login [login_form]
/// Además de su trabajo interno con el formulario, notifica a [SessionBloc]
/// cuándo el usuario ha realizado un log-in para que la aplicación actúen en consecuencia
///

class SupportRequestBloc extends Bloc<SupportRequestEvent, SupportRequestState> {
  final api = Deps.solve<ApiServices>();

  SupportRequestBloc() : super(SupportRequestInitialState()) {
    this.add(SupportRequestOnLoad());
  }

  factory SupportRequestBloc.create(BuildContext context) {
    return SupportRequestBloc();
  }

  @override
  Stream<Transition<SupportRequestEvent, SupportRequestState>> transformEvents(
    Stream<SupportRequestEvent> events,
    TransitionFunction<SupportRequestEvent, SupportRequestState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<SupportRequestState> mapEventToState(SupportRequestEvent event) async* {
    final state = this.state;
    if (event is SupportRequestOnLoad) {
      yield* _mapOnLoad(state, event);
    } else if (event is SupportRequestOnSend && state is SupportRequestLoadedState) {
      yield* _mapOnSend(state, event);
    }
  }

  Stream<SupportRequestState> _mapOnLoad(SupportRequestState state, SupportRequestOnLoad event) async* {
    yield SupportRequestLoadedState(
      formData: SupportRequestModel(),
    );
  }

  Stream<SupportRequestState> _mapOnSend(SupportRequestLoadedState state, SupportRequestOnSend event) async* {
    try {
      await api.postSupportrequest(SupportrequestDto(question: Some(state.formData.answere)));
      yield SupportRequestSuccessState();
    } on ApiException catch (e) {
      yield SupportRequestFailureState.fromLoadedState(state, error: "Problema inesperado: ${e.message}.");
    } on Exception {
      yield SupportRequestFailureState.fromLoadedState(state, error: "Problema inesperado, Inténtelo más tarde.");
    }
  }
}
