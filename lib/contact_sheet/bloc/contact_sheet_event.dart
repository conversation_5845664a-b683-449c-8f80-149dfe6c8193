part of 'contact_sheet_bloc.dart';

abstract class ContactSheetEvent extends Equatable {
  const ContactSheetEvent();

  @override
  List<Object> get props => [];
}

class ContactSheetOnFetchEvent extends ContactSheetEvent {
  final String contactId;
  const ContactSheetOnFetchEvent({required this.contactId});
}

class ContactSheetOnFetchedEvent extends ContactSheetEvent {
  final ContactDto contact;
  const ContactSheetOnFetchedEvent({required this.contact});
}

class ContactSheetOnRefreshEvent extends ContactSheetEvent {}
