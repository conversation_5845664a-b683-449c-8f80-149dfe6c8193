part of 'contact_sheet_bloc.dart';

enum ContactSheetStatus { initial, success, failure }
enum ContactSheetFailureCause {
  notFound,
  other,
}

class ContactSheetState extends Equatable {
  const ContactSheetState({
    this.status = ContactSheetStatus.initial,
    this.contact,
    this.definibleFields,
    this.lastFailureCause,
  });

  // Actual status of the screen
  final ContactSheetStatus status;
  final ContactSheetFailureCause? lastFailureCause;
  // Contacto cargado actualmente
  final ContactDto? contact;

  final Map<String, FieldDefDto>? definibleFields;

  ContactSheetState copyWith({
    ContactSheetStatus? status,
    ContactDto? contact,
    Map<String, FieldDefDto>? definibleFields,
    ContactSheetFailureCause? lastFailureCause,
  }) {
    return ContactSheetState(
      status: status ?? this.status,
      contact: contact ?? this.contact,
      definibleFields: definibleFields ?? this.definibleFields,
      lastFailureCause: lastFailureCause ?? this.lastFailureCause,
    );
  }

  @override
  List<Object> get props => [status, contact ?? ""];
}
