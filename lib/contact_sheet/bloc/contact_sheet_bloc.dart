import 'dart:async';

import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';

import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'contact_sheet_event.dart';
part 'contact_sheet_state.dart';

class ContactSheetBloc extends Bloc<ContactSheetEvent, ContactSheetState> {
  final AppModelsNS appModels;

  late StreamSubscription<ModelsChannelState> _contactsChannelSubscription;

  ContactSheetBloc({
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
  }) : super(ContactSheetState()) {
    this._contactsChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (this.state.contact != null && state is ModelsChannelUpdatedState<ContactDto>) {
        if (state.entities.map((o) => o.id.vn).where((s) => s != null).contains(this.state.contact?.id.vn)) {
          this.add(ContactSheetOnFetchEvent(contactId: this.state.contact!.id.v));
        }
      }
    });
  }

  factory ContactSheetBloc.create(BuildContext context) {
    return ContactSheetBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _contactsChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<ContactSheetEvent, ContactSheetState>> transformEvents(
    Stream<ContactSheetEvent> events,
    TransitionFunction<ContactSheetEvent, ContactSheetState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ContactSheetState> mapEventToState(ContactSheetEvent event) async* {
    if (event is ContactSheetOnFetchEvent) {
      yield* _mapOnFetchToState(state, event);
    }
  }

  Stream<ContactSheetState> _mapOnFetchToState(ContactSheetState state, ContactSheetOnFetchEvent event) async* {
    try {
      final contact = await appModels.readContact(event.contactId);
      if (contact != null) {
        final definibleFields = await _readDefinibleFields();
        yield state.copyWith(
          status: ContactSheetStatus.success,
          contact: contact,
          definibleFields: definibleFields,
        );
      } else {
        yield state.copyWith(status: ContactSheetStatus.failure, lastFailureCause: ContactSheetFailureCause.notFound);
      }
    } on Exception {
      yield state.copyWith(status: ContactSheetStatus.failure, lastFailureCause: ContactSheetFailureCause.notFound);
    }
  }

  Future<Map<String, FieldDefDto>> _readDefinibleFields() async {
    final result = await appModels.listContactFielddefs();
    return Map.fromEntries(result.map((fieldDef) => MapEntry(fieldDef.code, fieldDef)));
  }
}
