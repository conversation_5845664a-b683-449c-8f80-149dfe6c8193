import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:agentor_repositoryns/exception_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/action_edit/models/action_edit_page_params.dart';
import 'package:topbrokers/actionslist/views/embedded_actionslist.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/titled_card.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contact_sheet/bloc/contact_sheet_bloc.dart';
import 'package:topbrokers/demand_edit/models/demand_edit_page_params.dart';
import 'package:topbrokers/demands_list/views/views.dart';
import 'package:topbrokers/offer_edit/models/offer_edit_page_params.dart';
import 'package:topbrokers/offers_list/views/views.dart';
import 'package:topbrokers/routes.dart';
import 'package:topbrokers/global/session.dart';
import 'package:url_launcher/url_launcher_string.dart';

const _c_lineheight = 1.5;

class ContactSheetPage extends StatefulWidget {
  final String contactId;

  ContactSheetPage({Key? key, required this.contactId}) : super(key: key ?? AgentorKeys.showContactPage);

  @override
  _ContactSheetPageState createState() => _ContactSheetPageState();
}

class _ContactSheetPageState extends State<ContactSheetPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.contact_title),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: localizations.common_edit,
            onPressed: () {
              Navigator.pushNamed(context, AgentorRoutes.editContact, arguments: widget.contactId);
            },
          ),
        ],
      ),
      body: WidthLimiter(
        child: BlocProvider(
          create: (BuildContext context) =>
              ContactSheetBloc.create(context)..add(ContactSheetOnFetchEvent(contactId: this.widget.contactId)),
          child: _buildBody(context),
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return BlocConsumer<ContactSheetBloc, ContactSheetState>(
      listener: (context, state) {},
      buildWhen: (previous, actual) => true,
      builder: (context, state) {
        switch (state.status) {
          case ContactSheetStatus.failure:
            if (state.lastFailureCause == ContactSheetFailureCause.notFound)
              return Center(child: Text("El contacto no existe o no tienes permiso para consultarlo"));
            else
              return Center(child: Text(localizations.contact_errLoading));
          case ContactSheetStatus.success:
            if (state.contact == null)
              return Center(
                child: Text(localizations.contact_errDoesnExist),
              );
            else {
              final contact = state.contact as ContactDto;

              final definibleFields = state.definibleFields ?? doThrow<Map<String, FieldDefDto>>("Unexpected Null");
              return RefreshIndicator(
                onRefresh: () async {
                  context.bloc<ContactSheetBloc>().add(ContactSheetOnFetchEvent(contactId: widget.contactId));
                },
                child: Container(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(5, 5, 5, 5),
                    child: _buildCards(context, contact, definibleFields),
                  ),
                ),
              );
            }

          default:
            return const Center(
              child: CircularProgressIndicator(),
            );
        }
      },
    );
  }

  Widget _buildCards(
    BuildContext context,
    ContactDto contact,
    Map<String, FieldDefDto> fieldsDefinition,
  ) {
    final localizations = context.getAppLocalizationsOrThrow();
    final textTheme = Theme.of(context).textTheme;
    final imIndividual = context.imIndividual();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitledCard(
          Text(localizations.contact_title, style: textTheme.headline6),
          children: _buildHeadingCard(context, contact, fieldsDefinition),
        ),
        if (!imIndividual)
          TitledCard(
            Text(localizations.contact_offersLabel, style: textTheme.headline6),
            barActions: [
              TextButton.icon(
                icon: Icon(Icons.add),
                label: Text(localizations.contact_addOfferBtn),
                //padding: EdgeInsets.zero,
                onPressed: () {
                  assert(contact.id.vn != null);
                  Navigator.pushNamed(
                    context,
                    AgentorRoutes.addOffer,
                    arguments: NewOfferEditParams(customerId: contact.id.v),
                  );
                },
              )
            ],
            children: [
              EmbeddedOffersList(
                customerId: contact.id,
                inlcudeHistoric: False,
              ),
            ],
          ),
        if (!imIndividual)
          TitledCard(
            Text(localizations.contact_demandsLabel, style: textTheme.headline6),
            children: [
              EmbeddedDemandsList(
                customerId: contact.id,
                inlcudeHistoric: False,
              ),
            ],
            barActions: [
              TextButton.icon(
                icon: Icon(Icons.add),
                label: Text(localizations.contact_addDemandBtn),
                //padding: EdgeInsets.zero,
                onPressed: () {
                  assert(contact.id.vn != null);
                  Navigator.pushNamed(
                    context,
                    AgentorRoutes.addDemand,
                    arguments: NewDemandEditParams(customerId: contact.id.v),
                  );
                },
              )
            ],
          ),
        TitledCard(
          Text(localizations.contact_actionsLabel, style: textTheme.headline6),
          barActions: [
            TextButton.icon(
              icon: Icon(Icons.add),
              label: Text(localizations.contact_addActionBtn),
              //padding: EdgeInsets.zero,
              onPressed: () {
                assert(contact.id.vn != null);
                Navigator.pushNamed(
                  context,
                  AgentorRoutes.addAction,
                  arguments: NewActionEditParams(contactId: contact.id.v),
                );
              },
            )
          ],
          children: [
            EmbeddedActionsList(
              contactId: contact.id.v,
            ),
          ],
        )
      ],
    );
  }

  List<Widget> _buildHeadingCard(BuildContext context, ContactDto contact, Map<String, FieldDefDto> fieldsDefinition) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);
    final iconSize = (Theme.of(context).iconTheme.size ?? 24.0) * 0.8;
    final csiteBaseUrl = "https://tinder.topbrokers.io";
    final localization = context.getAppLocalizationsOrThrow();
    return <Widget>[
      Flexible(
        child: RichText(
          softWrap: true,
          text: TextSpan(
            text: "",
            style: textStyle,
            children: addSeparation([
              if (fieldsDefinition.containsKey(FielddefsSrv.c_contact_firstName))
                fieldsDefinition[FielddefsSrv.c_contact_firstName]!
                    .buildEntrySpans(context, getValue: () => contact.firstName.vn),
              if (fieldsDefinition.containsKey(FielddefsSrv.c_contact_lastName))
                fieldsDefinition[FielddefsSrv.c_contact_lastName]!
                    .buildEntrySpans(context, getValue: () => contact.lastName.vn),
              if (fieldsDefinition.containsKey(FielddefsSrv.c_contact_mobile))
                fieldsDefinition[FielddefsSrv.c_contact_mobile]!
                    .buildEntrySpans(context, getValue: () => contact.mobile.vn),
              if (fieldsDefinition.containsKey(FielddefsSrv.c_contact_email))
                fieldsDefinition[FielddefsSrv.c_contact_email]!
                    .buildEntrySpans(context, getValue: () => contact.email.vn),
              if (fieldsDefinition.containsKey(FielddefsSrv.c_contact_notes))
                fieldsDefinition[FielddefsSrv.c_contact_notes]!
                    .buildEntrySpans(context, getValue: () => contact.notes.vn),
            ]),
          ),
        ),
      ),
      if (contact.siteSlug.vn != null)
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton(
              child: Row(
                children: [Icon(Icons.open_in_browser), Text(localization.contact_siteLabel.capitalize)],
              ),
              onPressed: () async {
                final url = "$csiteBaseUrl/${contact.siteSlug.v}";
                if (await canLaunchUrlString(url)) {
                  await launchUrlString(url);
                }
              },
            ),
            TextButton.icon(
              onPressed: () async {
                final url = "$csiteBaseUrl/${contact.siteSlug.v}";
                ;
                await Clipboard.setData(ClipboardData(text: url));
                context.showNotification(localization.common_msg_urlCopiedToClipboard.capitalize);
              },
              label: Text("clipboard"),
              icon: Icon(Icons.copy, size: iconSize),
            ),
          ],
        ),
    ];
  }

  List<InlineSpan> addSeparation(List<Iterable<InlineSpan>> texts) {
    return texts.fold(
      <InlineSpan>[],
      (allSpans, spans) => allSpans
        ..addAll([
          if (allSpans.length != 0 && spans.length != 0) TextSpan(text: "\n"),
          if (spans.length != 0) TextSpan(text: "- "),
        ])
        ..addAll(spans),
    );
  }
}
