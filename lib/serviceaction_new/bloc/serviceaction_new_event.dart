part of 'serviceaction_new_bloc.dart';

abstract class ServiceactionNewEvent extends Equatable {
  const ServiceactionNewEvent();

  @override
  List<Object> get props => [];
}


class NewServiceactionOnNewEvent extends ServiceactionNewEvent {
  final String? actiontypeId;
  final String? contactId;
  final String? offerId;

  NewServiceactionOnNewEvent({this.contactId, this.offerId, this.actiontypeId}) : super();
  
  @override
  List<Object> get props => [contactId??"", offerId??"", actiontypeId??""];
}

///
/// El usuario quiere guardar los cambios
///
class NewServiceactionOnSaveEvent extends ServiceactionNewEvent {
  final ActionDto action;
  NewServiceactionOnSaveEvent({required this.action}) : super();

  @override
  List<Object> get props => [action];
}

class NewServiceactionOnValidationErrorEvent extends ServiceactionNewEvent {
  final String error;
  NewServiceactionOnValidationErrorEvent({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

///
/// Los cambios se han guardado corréctamente
///
class NewServiceactionOnSavedEvent extends ServiceactionNewEvent {}
