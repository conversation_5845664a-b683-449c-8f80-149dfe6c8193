import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/common/app_models_ns.dart';
part 'serviceaction_new_event.dart';
part 'serviceaction_new_state.dart';

class NewServiceactionBloc extends Bloc<ServiceactionNewEvent, NewServiceactionState> {
  final AppModelsNS appModels;

  NewServiceactionBloc({required this.appModels}) : super(NewServiceactionState());

  factory NewServiceactionBloc.create(BuildContext context) =>
      NewServiceactionBloc(appModels: Provider.of<AppModelsNS>(context, listen: false));

  @override
  Stream<Transition<ServiceactionNewEvent, NewServiceactionState>> transformEvents(
    Stream<ServiceactionNewEvent> events,
    TransitionFunction<ServiceactionNewEvent, NewServiceactionState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<NewServiceactionState> mapEventToState(ServiceactionNewEvent event) async* {
    final state = this.state;
    if (event is NewServiceactionOnNewEvent) {
      yield* _mapOnNewEventToState(state, event);
    } else if (event is NewServiceactionOnSaveEvent && state is NewServiceactionLoaded) {
      yield* _mapOnSaveEventToState(state, event);
    } else if (event is NewServiceactionOnValidationErrorEvent && state is NewServiceactionLoaded) {
      yield* _mapOnValidationErrorEventToState(state, event);
    }
  }

  Stream<NewServiceactionState> _mapOnNewEventToState(
      NewServiceactionState state, NewServiceactionOnNewEvent event) async* {
    try {
      final action = _emptyAction();
      final contactId = event.contactId;
      final actiontypeId = event.actiontypeId;

      if (contactId != null) {
        final contact = await appModels.readContact(contactId);
        action.contact = Some(contact);
      }
      final offerId = event.offerId;
      if (offerId != null) {
        action.offer = Some(await appModels.readOffer(offerId));
      }
      final actiontypes = await _listActiontypes(actiontypeId: actiontypeId);
      yield NewServiceactionLoaded(action: action, actiontypes: actiontypes);
    } on Exception {
      yield NewServiceactionLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<NewServiceactionState> _mapOnSaveEventToState(
    NewServiceactionLoaded state,
    NewServiceactionOnSaveEvent event,
  ) async* {
    try {
      yield NewServiceactionSaving.fromPrevious(state);
      final created = await _createAction(event.action);
      yield NewServiceactionSaved.fromPrevious(state, action: created);
      // Notificamos a quien le interese que la oferta ha cambiado.

    } on Exception {
      final msg = event.action.id.vn == null ? "Problemas guardando la nueva acción" : "Problemas guardando cambios";
      yield ServiceactionNewSaveFailure.fromPrevious(state, error: msg);
    }
  }

  Stream<NewServiceactionState> _mapOnValidationErrorEventToState(
      NewServiceactionLoaded state, NewServiceactionOnValidationErrorEvent event) async* {
    yield ServiceactionNewValidationFailure.fromPrevious(state, error: event.error);
  }

  Future<ActionDto> _createAction(ActionDto action) async {
    assert(action.id.vn == null);
    return appModels.createAction(action);
  }

  ActionDto _emptyAction() {
    return ActionDto.fromJson({"when": DateTime.now(), "done": false});
  }

  Future<List<ActiontypeDto>> _listActiontypes({String? actiontypeId}) {
    if (actiontypeId != null) {
      return appModels.listActiontypes(
        filter: ActiontypesListFilter(id: Some(actiontypeId)),
      ); //filter: ActiontypesListFilter(withService: False));
    } else {
      return appModels.listActiontypes(filter: ActiontypesListFilter(withService: True));
    }
  }
}
