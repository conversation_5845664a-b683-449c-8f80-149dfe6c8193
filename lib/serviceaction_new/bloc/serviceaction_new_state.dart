part of 'serviceaction_new_bloc.dart';

class NewServiceactionState extends Equatable {
  @override
  List<Object> get props => [];
}

class NewServiceactionLoading extends NewServiceactionState {}

class NewServiceactionLoaded extends NewServiceactionState {
  final ActionDto action;
  final List<ActiontypeDto> actiontypes;

  NewServiceactionLoaded({required this.action, required this.actiontypes}) : super();

  @override
  List<Object> get props => [action, actiontypes.length];
}

class NewServiceactionLoadFailure extends NewServiceactionState {
  final String error;

  NewServiceactionLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

class NewServiceactionSaved extends NewServiceactionLoaded {
  NewServiceactionSaved({required ActionDto action, required List<ActiontypeDto> actiontypes})
      : super(action: action, actiontypes: actiontypes);
  factory NewServiceactionSaved.fromPrevious(NewServiceactionLoaded status, {required ActionDto action}) {
    return NewServiceactionSaved(action: action, actiontypes: status.actiontypes);
  }
}

class NewServiceactionSaving extends NewServiceactionLoaded {
  NewServiceactionSaving({required ActionDto action, required List<ActiontypeDto> actiontypes})
      : super(action: action, actiontypes: actiontypes);
  factory NewServiceactionSaving.fromPrevious(NewServiceactionLoaded status) {
    return NewServiceactionSaving(action: status.action, actiontypes: status.actiontypes);
  }
}

class ServiceactionNewSaveFailure extends NewServiceactionLoaded {
  final lastError = new DateTime.now();
  final String error;
  ServiceactionNewSaveFailure({@required action, @required actiontypes, required this.error})
      : super(action: action, actiontypes: actiontypes);

  factory ServiceactionNewSaveFailure.fromPrevious(NewServiceactionLoaded status, {required String error}) {
    return ServiceactionNewSaveFailure(action: status.action, actiontypes: status.actiontypes, error: error);
  }
  @override
  List<Object> get props => [lastError, action, error];
}

class ServiceactionNewValidationFailure extends NewServiceactionLoaded {
  final lastError = new DateTime.now();
  final String error;
  ServiceactionNewValidationFailure({required action, required actiontypes, required this.error})
      : super(action: action, actiontypes: actiontypes);

  factory ServiceactionNewValidationFailure.fromPrevious(NewServiceactionLoaded status, {required String error}) {
    return ServiceactionNewValidationFailure(action: status.action, actiontypes: status.actiontypes, error: error);
  }

  @override
  List<Object> get props => [lastError, action, error];
}
