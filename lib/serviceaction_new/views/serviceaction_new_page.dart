import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/offers_list/widgets/offer_list_item.dart';
import 'package:topbrokers/serviceaction_new/models/serviceaction_new_page_params.dart';
import 'package:topbrokers/serviceaction_new/serviceaction_new.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:topbrokers/global/session.dart';

typedef OnSaveActionCallback = Function(ActionDto action);

const _c_lang = "es";

class NewServiceactionPage extends StatefulWidget {
  //final OnSaveActionCallback onSave;
  final isNew = true;
  final NewServiceactionParams params;
  //final ActionDto action;

  NewServiceactionPage({Key? key, required this.params}) : super(key: key ?? AgentorKeys.editActionPage);

  @override
  _NewServiceactionPageState createState() => _NewServiceactionPageState();
}

class _NewServiceactionPageState extends State<NewServiceactionPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        final params = this.widget.params;
        return NewServiceactionBloc.create(context)
          ..add(NewServiceactionOnNewEvent(
            contactId: params.contactId,
            offerId: params.offerId,
            actiontypeId: params.actiontypeId,
          ));
      },
      child: BlocConsumer<NewServiceactionBloc, NewServiceactionState>(listener: (context, state) {
        if (state is NewServiceactionSaved) {
          Navigator.pop(context);
        }
      }, builder: (context, state) {
        if (state is NewServiceactionLoaded) {
          return _buildPageScaffold(context: context, state: state);
        } else if (state is NewServiceactionLoadFailure) {
          return _buildScaffold(
            context: context,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(state.error),
                  Container(height: 12),
                  ElevatedButton(
                    child: Text("Reintentar"),
                    onPressed: () {
                      final params = this.widget.params;
                      final bloc = context.bloc<NewServiceactionBloc>();
                      if (bloc != null)
                        bloc.add(
                          NewServiceactionOnNewEvent(
                            contactId: params.contactId,
                            offerId: params.offerId,
                            actiontypeId: params.actiontypeId,
                          ),
                        );
                    },
                  ),
                ],
              ),
            ),
          );
        } else {
          return _buildScaffold(context: context, child: const Center(child: CircularProgressIndicator()));
        }
      }),
    );
  }

  Scaffold _buildScaffold({required BuildContext context, required Widget child, Widget? floatingActionButton}) {
    final localization = context.getAppLocalizationsOrThrow();
    return Scaffold(
      appBar: AppBar(
        title: Text(localization.serviceaction_titleNew),
        centerTitle: true,
      ),
      body: WidthLimiter(
        child: child,
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  Scaffold _buildPageScaffold({required BuildContext context, required NewServiceactionLoaded state}) {
    final localization = context.getAppLocalizationsOrThrow();
    return _buildScaffold(
      context: context,
      child: BlocConsumer<NewServiceactionBloc, NewServiceactionState>(
        listener: (context, state) {
          if (state is ServiceactionNewSaveFailure) {
            context.showError(state.error);
          } else if (state is ServiceactionNewValidationFailure) {
            context.showError(state.error);
          }
        },
        builder: (context, state) {
          if (state is NewServiceactionSaving) {
            return Stack(
              children: [
                _buildForm(
                  context,
                  state.action,
                  actiontypes: state.actiontypes,
                ),
                Container(
                  color: Colors.white.withAlpha(80),
                  //child: Text("Flutter"),
                  constraints: BoxConstraints.expand(),
                ),
                Center(child: CircularProgressIndicator()),
              ],
            );
          } else {
            return _buildForm(
              context,
              (state as NewServiceactionLoaded).action,
              actiontypes: state.actiontypes,
            );
          }
        },
      ),
      floatingActionButton: state.action.type is None || state is NewServiceactionSaving
          ? null
          : FloatingActionButton(
              key: AgentorKeys.saveAction,
              tooltip: localization.common_create,
              child: Icon(Icons.send, semanticLabel: "Aceptar"),
              onPressed: () async {
                if (_formKey.currentState?.validate() ?? false) {
                  _formKey.currentState?.save();
                  BlocProvider.of<NewServiceactionBloc>(context).add(NewServiceactionOnSaveEvent(action: state.action));
                } else {
                  BlocProvider.of<NewServiceactionBloc>(context)
                      .add(NewServiceactionOnValidationErrorEvent(error: localization.common_formWithErrors));
                }
              },
            ),
    );
  }

  Widget _buildForm(BuildContext context, ActionDto action, {required Iterable<ActiontypeDto> actiontypes}) {
    return new Container(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: (action.type is None)
            ? Center(
                child: Column(
                  children: [
                    SizedBox.fromSize(size: Size.fromHeight(12)),
                    for (final actiontype in actiontypes)
                      Card(
                        child: ListTile(
                          dense: false,
                          title: Text(
                            actiontype.label.vn?.getByLang(_c_lang) ?? "",
                          ),
                          onTap: () {
                            setState(() {
                              action.type = Some(actiontype);
                            });
                          },
                        ),
                      ),
                  ],
                ),
              )
            : Column(
                children: [
                  _actionFormEntries(context, action, actiontypes: actiontypes)
                      .buildForm(key: _formKey, context: context),
                  SizedBox.fromSize(size: Size.fromHeight(64))
                ],
              ),
      ),
    );
  }

  List<FormEntry> _actionFormEntries(BuildContext context, ActionDto action,
      {required Iterable<ActiontypeDto> actiontypes}) {
    final localizations = context.getAppLocalizationsOrThrow();
    final service = action.type.vn?.service.vn;
    // Si el tipo de acción es un servicio y la acción no es nueva, estamos en modo solo lectura
    final disclaimer = service?.disclaimer.vn;

    return <FormEntry>[
      GroupEntry(label: action.type.vn?.label.vn?.getByLang(_c_lang) ?? "", children: [
        //TextEntry(label: action.type.vn?.label.vn?.getByLang(_c_lang) ?? ""),
        /*SelectFieldEntry<String>(
          label: localizations.serviceaction_typeLabel,
          getValue: () => action.type.vn?.id.vn,
          setValue: (selectedId) {
            setState(() {
              final selectedType = actiontypes.firstWhere((atype) => atype.id.v == selectedId, orElse: null);
              action.type = Some(selectedType);
            });
          },
          isRequired: true,
          options: actiontypes.map((atype) {
            return SelectOption<String>(
              value: atype.id.v,
              label: atype.label.vn?.getByLang(_c_lang) ?? "",
              sublabel: atype.service.vn?.label.vn?.getByLang(_c_lang),
            );
          }).toList(),
        ),*/
        if (disclaimer != null)
          TextEntry(
            label: disclaimer.title.vn?.getByLang(_c_lang) ?? "",
            sufix: TextButton.icon(
              onPressed: () async {
                final title = disclaimer.title.vn?.getByLang(_c_lang) ?? "";
                final body = disclaimer.detailHtml.vn?.getByLang(_c_lang) ?? "";
                await _showDialog(context, title, body);
              },
              icon: Icon(Icons.open_in_new),
              label: Text("detalles"),
            ),
          ),
        if (!context.imIndividual())
          SearchFieldEntry<ContactDto>(
            label: localizations.serviceaction_contactLabel,
            getValue: () => action.contact.vn,
            setValue: (contact) {
              setState(() {
                action.contact = Some(contact);
              });
            },
            onSearch: (String search) => api.listContacts(filter: ContactsListFilter(search: Some(search))),
            itemBuilder: (context, contact, {bool isSelected = false}) => ContactListItem(contact: contact),
            valueToString: (contact) => contact != null ? contact.name.vn ?? "" : localizations.common_selectAContact,
            isRequired: action.type.vn?.contactIsRequired.vn ?? false,
          ),
        if (!context.imIndividual())
          SearchFieldEntry<OfferDto>(
            label: localizations.serviceaction_offerLabel,
            getValue: () => action.offer.vn,
            setValue: (OfferDto? value) => setState(() => action.offer = Some(value)),
            onSearch: (String search) => api.listOffers(filter: OffersListFilter(search: search)),
            itemBuilder: (context, offer, {bool isSelected = false}) => OfferListItem(offer: offer),
            valueToString: (offer) => offer != null ? offer.name.vn ?? "" : localizations.common_selectAnOffer,
            isRequired: action.type.vn?.offerIsRequired.vn ?? false,
          ),
        if (!context.imIndividual())
          SimpleFieldEntry<String>(
            label: localizations.serviceaction_notesLabel,
            getValue: () => action.description.vn,
            setValue: (newValue) {
              setState(() {
                action.description = Some(newValue);
              });
            },
            isRequired: false,
            isMultiline: true,
          )
      ])
    ];
  }

  Future<void> _showDialog(BuildContext context, String title, String bodyHtml) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          //title: Text(title),
          content: HTML.toRichText(context, bodyHtml.replaceAll('<li>', " - ").replaceAll('</li>', "<br />"),
              linksCallback: (link) async {
            if (await canLaunch(link)) {
              await launch(link);
            }
          }),
          buttonPadding: EdgeInsets.fromLTRB(20, 0, 20, 0),
          actions: <Widget>[
            ElevatedButton(
              child: Text("cerrar"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
