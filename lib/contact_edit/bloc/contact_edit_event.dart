part of 'contact_edit_bloc.dart';

abstract class ContactEditEvent extends Equatable {
  const ContactEditEvent();

  @override
  List<Object> get props => [];
}

class ContactEditOnEditEvent extends ContactEditEvent {
  final String contactId;
  ContactEditOnEditEvent({required this.contactId});
  @override
  List<Object> get props => [contactId];
}

class ContactEditOnNewEvent extends ContactEditEvent {}


///
/// El usuario quiere guardar los cambios
///
class ContactEditOnSaveEvent extends ContactEditEvent {
  final ContactDto contact;
  ContactEditOnSaveEvent({required this.contact}) : super();

  @override
  List<Object> get props => [contact];
}

class ContactEditOnValidationErrorEvent extends ContactEditEvent {
  final String error;
  ContactEditOnValidationErrorEvent({required this.error}) : super();

  @override
  List<Object> get props => [error];
}



///
/// Los cambios se han guardado corréctamente
///
class ContactEditOnSavedEvent extends ContactEditEvent {
}
