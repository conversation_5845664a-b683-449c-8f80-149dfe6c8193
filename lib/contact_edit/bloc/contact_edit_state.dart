part of 'contact_edit_bloc.dart';

class ContactEditState extends Equatable {
  @override
  List<Object> get props => [];
}

class ContactEditLoading extends ContactEditState {}

class ContactEditLoaded extends ContactEditState {
  final ContactDto contact;
  ContactEditLoaded({required this.contact}) : super();

  @override
  List<Object> get props => [contact];
}

class ContactEditLoadFailure extends ContactEditState {
  final String error;

  ContactEditLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

class ContactEditSaved extends ContactEditState {
  final ContactDto contact;

  ContactEditSaved({required this.contact}) : super();

  @override
  List<Object> get props => [contact];
}

class ContactEditSaveFailure extends ContactEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  ContactEditSaveFailure({required contact, required this.error}) : super(contact: contact);

  @override
  List<Object> get props => [lastError, contact, error];
}

class ContactEditValidationFailure extends ContactEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  ContactEditValidationFailure({
    required offer,
    required this.error,
  }) : super(contact: offer);

  @override
  List<Object> get props => [lastError, contact, error];
}
