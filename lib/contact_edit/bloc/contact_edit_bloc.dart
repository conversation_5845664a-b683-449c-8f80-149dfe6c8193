
import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/api_exception.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/contacts_list/contacts_utils.dart';

part 'contact_edit_event.dart';
part 'contact_edit_state.dart';

class ContactEditBloc extends Bloc<ContactEditEvent, ContactEditState> {
  final AppModelsNS appModels;

  ContactEditBloc({required this.appModels}) : super(ContactEditState());

  factory ContactEditBloc.create(BuildContext context) {
    return ContactEditBloc(appModels: Provider.of<AppModelsNS>(context, listen: false));
  }

  @override
  Stream<Transition<ContactEditEvent, ContactEditState>> transformEvents(
    Stream<ContactEditEvent> events,
    TransitionFunction<ContactEditEvent, ContactEditState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ContactEditState> mapEventToState(ContactEditEvent event) async* {
    final state = this.state;
    if (event is ContactEditOnEditEvent) {
      yield* _mapOnEditEventToState(state, event);
    } else if (event is ContactEditOnNewEvent) {
      yield* _mapOnNewEventToState(state, event);
    } else if (event is ContactEditOnSaveEvent && state is ContactEditLoaded) {
      yield* _mapOnSaveEventToState(state, event);
    } else if (event is ContactEditOnValidationErrorEvent && state is ContactEditLoaded) {
      yield* _mapOnValidationErrorEventToState(state, event);
    }
  }

  Stream<ContactEditState> _mapOnEditEventToState(ContactEditState state, ContactEditOnEditEvent event) async* {
    try {
      final contact = await _readContact(event.contactId);
      if (contact == null)
        throw new Exception("Contacto no existe");
      else
        yield ContactEditLoaded(contact: contact);
    } on Exception {
      yield ContactEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<ContactEditState> _mapOnNewEventToState(ContactEditState state, ContactEditOnNewEvent event) async* {
    try {
      final contact = ContactUtils.emptyContact();
      yield ContactEditLoaded(contact: contact);
    } on Exception {
      yield ContactEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<ContactEditState> _mapOnSaveEventToState(ContactEditLoaded state, ContactEditOnSaveEvent event) async* {
    try {
      if (event.contact.id.vn == null) {
        final created = await _createContact(event.contact);
        yield ContactEditSaved(contact: created);
        // Notificamos a quien le interese que la oferta ha cambiado.
      } else {
        final updated = await _updateContact(event.contact);
        yield ContactEditSaved(contact: updated);
        // Notificamos a quien le interese que la oferta ha cambiado.
      }
    } on ApiEmailAlredyExistException {
      yield ContactEditSaveFailure(contact: state.contact, error: "El email indicado ya existe");
    } on ApiMobileAlredyExistException {
      yield ContactEditSaveFailure(contact: state.contact, error: "El móvil indicado ya existe");
    } on Exception {
      final msg = event.contact.id.vn == null ? "Problemas creando el contacto" : "Problemas guardando cambios";
      yield ContactEditSaveFailure(contact: state.contact, error: msg);
    }
  }

  Stream<ContactEditState> _mapOnValidationErrorEventToState(
    ContactEditLoaded state,
    ContactEditOnValidationErrorEvent event,
  ) async* {
    yield ContactEditValidationFailure(offer: state.contact, error: event.error);
  }

  Future<ContactDto?> _readContact(String id) async {
    return appModels.readContact(id);
  }

  Future<ContactDto> _updateContact(ContactDto contact) async {
    assert(contact.id.vn != null);
    return appModels.updateContact(contact);
  }

  Future<ContactDto> _createContact(ContactDto contact) async {
    return appModels.createContact(contact);
  }
}
