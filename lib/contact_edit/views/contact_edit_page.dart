import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contact_edit/bloc/contact_edit_bloc.dart';
import 'package:topbrokers/contact_edit/models/contact_edit_page_result.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:form_builder/form_builder.dart';

typedef OnSaveContactCallback = Function(ContactDto contact);

class ContactEditPage extends StatefulWidget {
  //final OnSaveContactCallback onSave;
  final String? contactId;
  //final ContactDto contact;

  ContactEditPage({Key? key, this.contactId}) : super(key: key ?? AgentorKeys.editContactPage);

  @override
  _ContactEditPageState createState() => _ContactEditPageState();
}

class _ContactEditPageState extends State<ContactEditPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return BlocProvider(
      create: (context) => ContactEditBloc.create(context)
        ..add(this.widget.contactId != null
            ? ContactEditOnEditEvent(contactId: this.widget.contactId as String)
            : ContactEditOnNewEvent()),
      child: BlocConsumer<ContactEditBloc, ContactEditState>(
        listener: (context, state) {
          if (state is ContactEditSaved) {
            Navigator.pop(context, ContactEditPageResult(id: state.contact.id.vn, saved: true));
          }
        },
        builder: (context, state) {
          if (state is ContactEditLoaded) {
            return Scaffold(
              appBar: AppBar(
                title: Text(widget.contactId != null ? localizations.contact_title : localizations.contact_titleNew),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: BlocConsumer<ContactEditBloc, ContactEditState>(
                  listener: (context, state) {
                    if (state is ContactEditSaveFailure) {
                      context.showError(state.error);
                    } else if (state is ContactEditValidationFailure) {
                      context.showError(state.error);
                    }
                  },
                  builder: (context, state) {
                    // ContactformSaveFailure y ContactformValidationFailure
                    // extienden ContactformLoaded
                    return _buildForm(context, (state as ContactEditLoaded).contact);
                  },
                ),
              ),
              floatingActionButton: FloatingActionButton(
                key: AgentorKeys.saveNewContact,
                tooltip: widget.contactId != null ? localizations.contact_saveBtn : localizations.contact_createBtn,
                child: Icon(Icons.save_outlined, semanticLabel: "Aceptar"),
                onPressed: () async {
                  if (_formKey.currentState?.validate() ?? false) {
                    _formKey.currentState?.save();
                    BlocProvider.of<ContactEditBloc>(context).add(ContactEditOnSaveEvent(contact: state.contact));
                  } else {
                    BlocProvider.of<ContactEditBloc>(context)
                        .add(ContactEditOnValidationErrorEvent(error: localizations.common_formWithErrors));
                  }
                },
              ),
            );
          } else if (state is ContactEditLoadFailure) {
            return Center(
              child: Text(state.error),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, ContactDto contact) {
    return Container(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            _contactEntries(context, contact).buildForm(key: _formKey, context: context),
            SizedBox.fromSize(size: Size.fromHeight(64)),
          ],
        ),
      ),
    );
  }

  List<FormEntry> _contactEntries(BuildContext context, ContactDto contact) {
    final localizations = context.getAppLocalizationsOrThrow();
    return <FormEntry>[
      GroupEntry(children: [
        SimpleFieldEntry<String>(
          label: localizations.contact_nameLabel,
          getValue: () => contact.firstName.vn,
          setValue: (String? v) {
            setState(() {
              contact.firstName = v==null ? None() : Some(v);
            });
          },
          isRequired: true,
        ),
        SimpleFieldEntry<String>(
          label: localizations.contact_lastnameLabel,
          getValue: () => contact.lastName.vn,
          setValue: (String? v) {
            setState(() {
              contact.lastName = Some(v);
            });
          },
          isRequired: false,
        ),
        SimpleFieldEntry<String>(
          label: localizations.contact_emailLabel,
          getValue: () => contact.email.vn,
          setValue: (String? v) {
            setState(() {
              contact.email = v=="" ? Some(null) : Some(v);
            });
          },
          isRequired: false,
          isEmail: true,
        ),
        SimpleFieldEntry<String>(
          label: localizations.contact_mobileLabel,
          getValue: () => contact.mobile.vn,
          setValue: (String? v) {
            setState(() {
              contact.mobile = v=="" ? Some(null) : Some(v);
            });
          },
          isPhoneNumber: true,
        ),
        SimpleFieldEntry<String>(
          label: localizations.contact_notesLabel,
          getValue: () => contact.notes.vn,
          setValue: (String? v) {
            setState(() {
              contact.notes = Some(v);
            });
          },
          isMultiline: true,
        ),
      ])
    ];
  }
}
