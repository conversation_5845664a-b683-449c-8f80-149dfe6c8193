import 'package:flutter/widgets.dart';

class AgentorKeys {
  // Landing page
  static const landingPage = Key('__landingPage__');

  static const supportRequestPage = Key('__supportRequestPage__');
  // Login page
  static const loginPage = Key('_loginPage__');
  // Sign up page
  static const signupPage = Key('_signupPage__');
  // Home page
  static const homePage = Key('__homePage__');
  static final homeTabsCloud = const Key('__homeTabsCloud__');
  static final homeTabsOpportunities = const Key('__homeTabsOpportunities__');
  static final homeTabsContacts = const Key('_homeTabsContacts__');
  static final homeTabsActions = const Key('__homeTabsActions__');
  
  // Offers page
  static const offersList = const Key('__offersList__');
  static const offersFilterButton = const Key("__offersFilterButton__");
  static const offersFilterPage = const Key("__offersFilterPage__");
  static const offersExportPage = const Key("__offersExportPage__");

  static const cloudoffersFilterButton = const Key("__cloudoffersFilterButton__");
  // New Offer Page
  static final newOfferPage = const Key('__newOfferPage__');
  static final saveNewOffer = const Key('__saveNewOffer__');
  // Offer import page
  static final offerImportPage = const Key('__offerImportPage__');

  // Edit Offer Page
  static final editOfferPage = const Key('__editOfferPage__');

  // Show Offer page
  static final showOfferPage = const Key('__showOfferPage__');
  
  // Demands page
  static final demandsList = const Key('__demandsListPage__');
  static final demandsFilterButton = const Key('__demandsFilterButton__');
  // New Demand Page
  static final newDemandPage = const Key('__newDemandPage__');
  static final saveNewDemand = const Key('__saveNewDemand__');

  // Edit Demand Page
  static final editDemandPage = const Key('__editDemandPage__');

  // Show Demand page
  static final showDemandPage = const Key('__showDemandPage__');

  // Contacts
  static const contactsPage = Key('__contactsPage__');
  static const contactsList = const Key('__contactsList__');
  static const contactsFilterButton = const Key('__contactsFilterButton__');
  static const contactsFilterPage = const Key('__contactsFilterPage__');
  static final newContactPage = const Key('__newContactPage__');
  static final saveNewContact = const Key('__saveNewContact__');
  static final editContactPage = const Key("__editContactPage__");
  static final showContactPage = const Key("__showContactPage__");

  static final createDepositPage = const Key('_createDepositPage_');
  static final purchaseslist = const Key('__purchasesList__');
  static final purchasesPage = const Key('__purchasesPage__');
  
  static final editAgentPage = const Key("__editAgentPage__");
  static final saveAgent = const Key('__saveAgent__');

  static final actionsList = const Key('__actionsList__');
  static const actionsFilterButton = const Key("__actionsFilterButton__");
  // Edit/New Action Page
  static final editActionPage = const Key('__editActionPage__');
  static final saveAction = const Key('__saveNewAction__');

  // TABS

  static final extraActionsPopupMenuButton = const Key('__extraActionsPopupMenuButton__');
  static final extraActionsEmptyContainer = const Key('__extraActionsEmptyContainer__');
  static final filteredTodosEmptyContainer = const Key('__filteredTodosEmptyContainer__');
  static final statsLoadInProgressIndicator = const Key('__statsLoadInProgressIndicator__');
  static final emptyStatsContainer = const Key('__emptyStatsContainer__');
  static final emptyDetailsContainer = const Key('__emptyDetailsContainer__');
  static final detailsScreenCheckBox = const Key('__detailsScreenCheckBox__');
}
