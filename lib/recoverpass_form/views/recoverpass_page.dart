import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/recoverpass_form/bloc/recoverpass_form_bloc.dart';
import 'package:topbrokers/recoverpass_form/views/recoverpass_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart'; // Add this line

class RecoverpassPage extends StatelessWidget {
  const RecoverpassPage({Key? key}) : super(key: key ?? AgentorKeys.loginPage);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RecoverpassFormBloc>(
      create: (context) => RecoverpassFormBloc(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(AppLocalizations.of(context)?.recoverpass_title ?? ""),
          centerTitle: true,
          actions: [],
        ),
        body: WidthLimiter(child: RecoverpassForm()),
      ),
    );
  }
}
