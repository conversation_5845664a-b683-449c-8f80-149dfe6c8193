import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/recoverpass_form/bloc/recoverpass_form_bloc.dart';
import 'package:topbrokers/recoverpass_form/models/recoverpass_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';


class RecoverpassForm extends StatefulWidget {
  @override
  _RecoverpassFormState createState() => _RecoverpassFormState();
}

class _RecoverpassFormState extends State<RecoverpassForm> {
  final GlobalKey<FormState> _key = GlobalKey<FormState>();

  final _email = TextEditingController();
  final _validationcode = TextEditingController();
  final _password = TextEditingController();
  final _password2 = TextEditingController();

  bool _autoValidate = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<RecoverpassFormBloc, RecoverpassFormState>(
      listener: (context, state) {
        if (state is RecoverpassFormFailureState) {
          context.showError(state.error);
        }
      },
      child: <PERSON><PERSON><PERSON>er<RecoverpassFormBloc, RecoverpassFormState>(
        builder: (context, state) {
          if (state is RecoverpassFormInitialState)
            return Center(
              child: CircularProgressIndicator(),
            );
          else if (state is RecoverpassFormLoadedState && state.step == RecoverpassFormModelStep.step1_inputEmail)
            return _buildStep1(context, state);
          else if (state is RecoverpassFormLoadedState && state.step == RecoverpassFormModelStep.step2_fillData)
            return _buildStep2(context, state);
          else if (state is RecoverpassFormFinalState)
            return _buildFinalStep(context, state);
          else
            return Center(
              child: CircularProgressIndicator(),
            );
        },
      ),
    );
  }

  Widget _buildStep1(BuildContext context, RecoverpassFormLoadedState state) {
    final localization = context.getAppLocalizationsOrThrow();

    _email.text = state.formData.email ?? "";
    return Form(
      key: _key,
      autovalidateMode: _autoValidate ? AutovalidateMode.onUserInteraction : AutovalidateMode.disabled,
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(42),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              TextFormField(
                decoration: InputDecoration(
                  labelText: localization.agent_emailLabel,
                ),
                controller: _email,
                validator: MyTextInputValidators.isEmail(allowEmpty: false),
                keyboardType: TextInputType.emailAddress,
                autocorrect: false,
              ),
              const SizedBox(
                height: 42,
              ),
              ElevatedButton(
                child: Text(localization.common_continue),
                onPressed: () {
                  final formState = _key.currentState;
                  if (formState != null) {
                    if (formState.validate())
                      BlocProvider.of<RecoverpassFormBloc>(context).add(
                        RecoverpassFormOnNext(
                          formData: state.formData.copyWith(
                            email: _email.text,
                          ),
                        ),
                      );
                    else
                      setState(() {
                        _autoValidate = true;
                      });
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStep2(BuildContext context, RecoverpassFormLoadedState state) {
    final localization = context.getAppLocalizationsOrThrow();

    _email.text = state.formData.email ?? "";

    _validationcode.text = state.formData.validationCode ?? "";

    _password.text = state.formData.password ?? "";
    _password2.text = state.formData.password2 ?? "";
    return Form(
      key: _key,
      autovalidateMode: _autoValidate ? AutovalidateMode.onUserInteraction : AutovalidateMode.disabled,
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(42),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.signup_validationcodeLabel, // "Código de validación",
                    helperText: localization.recoverpass_validationcodeHelper,
                    helperMaxLines: 2,
                  ),
                  controller: _validationcode,
                  validator: MyTextInputValidators.notEmpty(),
                  onChanged: (value) {
                    state.formData.validationCode = value;
                  }),
              const SizedBox(
                height: 42,
              ),
              Text(localization.signup_fieldsBlocTitle, style: Theme.of(context).textTheme.headline6),
              TextFormField(
                decoration: InputDecoration(
                  labelText: localization.agent_emailLabel,
                ),
                controller: _email,
                readOnly: true,
                enabled: false,
              ),
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.signup_passwordLabel,
                  ),
                  obscureText: true,
                  controller: _password,
                  validator: MyTextInputValidators.isPassword(),
                  onChanged: (value) {
                    state.formData.password = value;
                  }),
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.signup_password2Label,
                  ),
                  obscureText: true,
                  controller: _password2,
                  validator: (value) {
                    if (value != _password.text) {
                      return localization.signup_password2Error;
                    } else {
                      return null;
                    }
                  },
                  onChanged: (value) {
                    state.formData.password2 = value;
                  }),
              const SizedBox(
                height: 42,
              ),
              ElevatedButton(
                child: Text(localization.common_continue),
                onPressed: () {
                  final formState = _key.currentState;
                  if (formState != null) {
                    if (formState.validate())
                      BlocProvider.of<RecoverpassFormBloc>(context).add(
                        RecoverpassFormOnNext(
                          formData: state.formData.copyWith(email: _email.text, validationCode: _validationcode.text),
                        ),
                      );
                    else
                      setState(() {
                        _autoValidate = true;
                      });
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFinalStep(BuildContext context, RecoverpassFormFinalState state) {
    final localization = context.getAppLocalizationsOrThrow();
    final textStyle = Theme.of(context).textTheme.headline6;
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(42),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              localization.recoverpass_finalMessageText,
              style: textStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 42,
            ),
            ElevatedButton(
              child: Text(localization.signup_loginBtn),
              onPressed: () {
                Navigator.pop(context);
              },
            )
          ],
        ),
      ),
    );
  }
}
