part of 'recoverpass_form_bloc.dart';

int _counter = 0;

class RecoverpassFormState extends Equatable {
  final _instanceId = ++_counter;

  @override
  List<Object> get props => [_instanceId];
}

/// En preparación (cargando)
class RecoverpassFormInitialState extends RecoverpassFormState {}

class RecoverpassFormFinalState extends RecoverpassFormState {
  RecoverpassFormFinalState() : super();
}

/// Todo listo para trabajar en la vista
class RecoverpassFormLoadedState extends RecoverpassFormState {
  final RecoverpassFormModelStep step;
  final RecoverpassFormModel formData;

  RecoverpassFormLoadedState({
    required this.step,
    required this.formData,
  });

  RecoverpassFormLoadedState copyWith({
    RecoverpassFormModelStep? step,
    RecoverpassFormModel? formData,
  }) {
    return RecoverpassFormLoadedState(step: step ?? this.step, formData: formData ?? this.formData);
  }
}

/// Hay un error en el formulario actual
class RecoverpassFormFailureState extends RecoverpassFormLoadedState {
  final String error;

  RecoverpassFormFailureState({
    required this.error,
    required RecoverpassFormModelStep step,
    required RecoverpassFormModel formData,
  }) : super(step: step, formData: formData);

  factory RecoverpassFormFailureState.fromLoadedState(RecoverpassFormLoadedState src, {required String error}) {
    return RecoverpassFormFailureState(error: error, step: src.step, formData: src.formData);
  }
  @override
  List<Object> get props => [_instanceId, error];
}

/// Todo ha ido OK
class RecoverpassFormSuccessState extends RecoverpassFormState {}
