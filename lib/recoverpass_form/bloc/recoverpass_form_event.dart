part of 'recoverpass_form_bloc.dart';

abstract class RecoverpassFormEvent extends Equatable {
  const RecoverpassFormEvent();

  @override
  List<Object> get props => [];
}

class RecoverpassFormOnLoad extends RecoverpassFormEvent {
  RecoverpassFormOnLoad() : super();
}

///
///  Avanzar al siguiente paso del "wizzard"
///
class RecoverpassFormOnNext extends RecoverpassFormEvent {
  final RecoverpassFormModel formData;
  RecoverpassFormOnNext({required this.formData}) : super();
}
