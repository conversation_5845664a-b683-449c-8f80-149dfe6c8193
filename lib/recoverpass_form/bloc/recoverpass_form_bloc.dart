import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:agentor_repositoryns/exception_utils.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/recoverpass_form/models/recoverpass_model.dart';

part 'recoverpass_form_event.dart';
part 'recoverpass_form_state.dart';

///
/// Gestiona estados y eventos para el formulario de login [login_form]
/// Además de su trabajo interno con el formulario, notifica a [SessionBloc]
/// cuándo el usuario ha realizado un log-in para que la aplicación actúen en consecuencia
///

class RecoverpassFormBloc extends Bloc<RecoverpassFormEvent, RecoverpassFormState> {
  final api = Deps.solve<ApiServices>();

  RecoverpassFormBloc() : super(RecoverpassFormInitialState()) {
    this.add(RecoverpassFormOnLoad());
  }

  @override
  Stream<Transition<RecoverpassFormEvent, RecoverpassFormState>> transformEvents(
    Stream<RecoverpassFormEvent> events,
    TransitionFunction<RecoverpassFormEvent, RecoverpassFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<RecoverpassFormState> mapEventToState(RecoverpassFormEvent event) async* {
    final state = this.state;
    if (event is RecoverpassFormOnLoad) {
      yield* _mapOnLoad(state, event);
    } else if (state is RecoverpassFormLoadedState && event is RecoverpassFormOnNext) {
      yield* _mapOnNext(state, event);
    }
  }

  Stream<RecoverpassFormState> _mapOnLoad(RecoverpassFormState state, RecoverpassFormOnLoad event) async* {
    yield RecoverpassFormLoadedState(
      step: RecoverpassFormModelStep.step1_inputEmail,
      formData: RecoverpassFormModel(),
    );
  }

  Stream<RecoverpassFormState> _mapOnNext(RecoverpassFormLoadedState state, RecoverpassFormOnNext event) async* {
    try {
      switch (state.step) {
        case RecoverpassFormModelStep.step1_inputEmail:
          assert(event.formData.email != null);

          // Aquí toca generar una clave
          await api.postPrePassword(event.formData.email ?? "");
          // Siguiente paso
          yield RecoverpassFormLoadedState(
            step: RecoverpassFormModelStep.step2_fillData,
            formData: RecoverpassFormModel(email: event.formData.email),
          );
          break;
        case RecoverpassFormModelStep.step2_fillData:
          // Crear cuenta
          assert(event.formData.email != null);
          assert(event.formData.password != null);
          assert(event.formData.validationCode != null);

          await api.postPassword(
            email: event.formData.email ?? doThrow<String>("Unexpected: email can't be null"),
            password: event.formData.password ?? doThrow<String>("Unexpected: password can't be null"),
            validationCode: event.formData.validationCode ?? doThrow<String>("Unexpected: validationCode can't be null"),
          );

          // Siguiente paso
          yield RecoverpassFormFinalState();

          break;
        default:
          yield state;
          break;
      }

      //_globalBloc.add(GlobalOnLoggedIn(token: token));
    } on ApiUnknownValidationCodeException {
      yield RecoverpassFormFailureState.fromLoadedState(
        state,
        error: "El código de validación no existe o ha expirado",
      );
    } on ApiException catch (e) {
      yield RecoverpassFormFailureState.fromLoadedState(state, error: "Problema inesperado: ${e.message}.");
    } on Exception {
      yield RecoverpassFormFailureState.fromLoadedState(state, error: "Problema inesperado, Inténtelo más tarde.");
    }
  }
}
