enum RecoverpassFormModelStep {
  ///
  /// Solicitar eMail.
  /// Al apreta<PERSON> "continuar" se genera un código de validación (que se envía por correo electrónico) y se navega al paso siguiente
  /// Tanto si existe como si no el email, se ofrecerá "continuar" (la no existencia del email no se informa al usuario).
  step1_inputEmail,

  ///
  /// Se solicita el código de validación y la contraseña
  ///
  step2_fillData,

  ///
  /// La contraseña se ha cambiado OK
  ///
  step3_allDone,
}

class RecoverpassFormModel {
  String? email;
  String? validationCode;
  String? password;
  String? password2;

  RecoverpassFormModel({
    this.email,
    this.validationCode,
    this.password,
    this.password2,
  });

  RecoverpassFormModel copyWith({
    String? email,
    String? validationCode,
    String? password,
    String? password2,
  }) {
    return RecoverpassFormModel(
      email: email ?? this.email,
      validationCode: validationCode ?? this.validationCode,
      password: password ?? this.password,
      password2: password2 ?? this.password2,
    );
  }
}
