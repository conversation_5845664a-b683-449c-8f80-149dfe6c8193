import 'package:agentor_deps/agentor_deps.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/app_config.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/signup_form/bloc/signup_form_bloc.dart';
import 'package:topbrokers/signup_form/models/singup_model.dart';
import 'package:url_launcher/url_launcher.dart';

class SignupForm extends StatefulWidget {
  @override
  _SignupFormState createState() => _SignupFormState();
}

class _SignupFormState extends State<SignupForm> {
  final GlobalKey<FormState> _key = GlobalKey<FormState>();

  final _email = TextEditingController();
  final _mobile = TextEditingController();
  final _onetimecode = TextEditingController();
  final _validationcode = TextEditingController();
  final _password = TextEditingController();
  final _firstName = TextEditingController();
  final _lastName = TextEditingController();
  final _password2 = TextEditingController();

  bool _autoValidate = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignupFormBloc, SignupFormState>(
      listener: (context, state) {
        if (state is SignupFormFailureState) {
          context.showError(state.error);
        }
      },
      child: BlocBuilder<SignupFormBloc, SignupFormState>(
        builder: (context, state) {
          if (state is SignupFormInitialState)
            return Center(
              child: CircularProgressIndicator(),
            );
          else if (state is SignupFormLoadedState && state.step == SignupFormModelStep.step1_inputEmail)
            return _buildStep1(context, state);
          else if (state is SignupFormLoadedState && state.step == SignupFormModelStep.step2_fillData)
            return _buildStep2(context, state);
          else if (state is SignupFormFinalState)
            return _buildFinalStep(context, state);
          else
            return Center(
              child: CircularProgressIndicator(),
            );
        },
      ),
    );
  }

  Widget _buildStep1(BuildContext context, SignupFormLoadedState state) {
  final localization = context.getAppLocalizationsOrThrow();

  _email.text = state.formData.email;
  _onetimecode.text = state.formData.onetimeCode;
  
  return Form(
    key: _key,
    autovalidateMode: _autoValidate ? AutovalidateMode.onUserInteraction : AutovalidateMode.disabled,
    child: SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(42),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            TextFormField(
              decoration: InputDecoration(
                labelText: localization.agent_emailLabel,
              ),
              controller: _email,
              validator: (value) {
                //if (!(MyTextInputValidators.isEmail(allowEmpty: false)(value) ?? false)) {
                //  return localization.invalid_email;
                //}
                if (!value!.endsWith("@agenteunico.net") && !value.endsWith("@percentservicios.com")) {
                  return localization.signup_invalid_domain;
                }
                return null;
              },
              keyboardType: TextInputType.emailAddress,
              autocorrect: false,
            ),
            const SizedBox(
              height: 42,
            ),
            TextFormField(
              decoration: InputDecoration(
                labelText: localization.signup_onetimecodeLabel, // "Código de validación onetime, dado por nosotros",
                helperText: localization.signup_onetimecodeHelper,
                helperMaxLines: 2,
              ),
              controller: _onetimecode,
              validator: MyTextInputValidators.notEmpty(),
              onChanged: (value) {
                state.formData.onetimeCode = value;
              },
            ),
            const SizedBox(
              height: 24,
            ),
            ElevatedButton(
              child: Text(localization.common_continue),
              onPressed: () {
                final formState = _key.currentState;
                if (formState != null && formState.validate()) {
                  BlocProvider.of<SignupFormBloc>(context).add(
                    SignupFormOnNext(
                      formData: state.formData.copy()
                        ..email = _email.text
                        ..onetimeCode = _onetimecode.text,
                    ),
                  );
                } else {
                  setState(() {
                    _autoValidate = true;
                  });
                }
              },
            )
          ],
        ),
      ),
    ),
  );
}


  Widget _buildStep2(BuildContext context, SignupFormLoadedState state) {
    final localization = context.getAppLocalizationsOrThrow();

    _email.text = state.formData.email;
    _mobile.text = state.formData.mobile;
    _validationcode.text = state.formData.validationCode;
    _firstName.text = state.formData.firstName;
    _lastName.text = state.formData.lastName;
    _password.text = state.formData.password;
    _password2.text = state.formData.password2;

    return Form(
      key: _key,
      autovalidateMode: _autoValidate ? AutovalidateMode.onUserInteraction : AutovalidateMode.disabled,
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(42),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.signup_validationcodeLabel, // "Código de validación",
                    helperText: localization.signup_validationcodeHelper,
                    helperMaxLines: 2,
                  ),
                  controller: _validationcode,
                  validator: MyTextInputValidators.notEmpty(),
                  onChanged: (value) {
                    state.formData.validationCode = value;
                  }),
              if (state.showResendCodeButton)
                const SizedBox(
                  height: 12,
                ),
              if (state.showResendCodeButton)
                ElevatedButton(
                  child: Text(localization.signup_resendValidationCodeBtn),
                  onPressed: () {
                    final formState = _key.currentState;
                    if (formState != null) {
                      if (formState.validate() && state.formData.eulaAccepted)
                        BlocProvider.of<SignupFormBloc>(context).add(
                          SignupFormOnPrevious(
                            formData: state.formData.copy()..validationCode = "",
                          ),
                        );
                      else
                        setState(() {
                          _autoValidate = true;
                        });
                    }
                  },
                ),
              const SizedBox(
                height: 12,
              ),
              Text(localization.signup_fieldsBlocTitle, style: Theme.of(context).textTheme.titleLarge),
              TextFormField(
                decoration: InputDecoration(
                  labelText: localization.agent_emailLabel,
                ),
                controller: _email,
                readOnly: true,
                enabled: false,
              ),
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.agent_mobileLabel,
                  ),
                  controller: _mobile,
                  readOnly: false,
                  enabled: true,
                  validator: MyTextInputValidators.notEmpty(),
                  onChanged: (value) {
                    state.formData.mobile = value;
                  }),
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.agent_nameLabel,
                  ),
                  controller: _firstName,
                  validator: MyTextInputValidators.notEmpty(),
                  onChanged: (value) {
                    state.formData.firstName = value;
                  }),
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.agent_lastnameLabel,
                  ),
                  controller: _lastName,
                  validator: MyTextInputValidators.notEmpty(),
                  onChanged: (value) {
                    state.formData.lastName = value;
                  }),
              TextFormField(
                  decoration: InputDecoration(
                    labelText: localization.signup_passwordLabel,
                  ),
                  obscureText: true,
                  controller: _password,
                  validator: MyTextInputValidators.isPassword(),
                  onChanged: (value) {
                    state.formData.password = value;
                  }),
              TextFormField(
                decoration: InputDecoration(
                  labelText: localization.signup_password2Label,
                ),
                obscureText: true,
                controller: _password2,
                validator: (value) {
                  if (value != _password.text) {
                    return localization.signup_password2Error;
                  } else {
                    return null;
                  }
                },
                onChanged: (value) {
                  state.formData.password2 = value;
                },
              ),
              const SizedBox(
                height: 12,
              ),
              CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.all(0),
                title: HTML.toRichText(
                  context,
                  localization.signup_acceptEulaTitle(
                    "${Deps.solve<AppConfig>().webserverUrl}aviso-legal",
                    '${Deps.solve<AppConfig>().webserverUrl}aviso-legal',
                  ),
                  linksCallback: (lnk) async {
                    if (await canLaunch(lnk)) {
                      await launch(lnk);
                    }
                  },
                ),
                value: state.formData.eulaAccepted,
                onChanged: (bool? value) {
                  setState(() {
                    state.formData.eulaAccepted = value ?? false;
                  });
                },
              ),
              const SizedBox(
                height: 12,
              ),
              if (state.formData.eulaAccepted)
                ElevatedButton(
                  child: Text(localization.common_continue),
                  onPressed: () {
                    final formState = _key.currentState;
                    if (formState != null) {
                      if (formState.validate() && state.formData.eulaAccepted)
                        BlocProvider.of<SignupFormBloc>(context).add(
                          SignupFormOnNext(
                            formData: state.formData.copy()
                              ..email = _email.text
                              ..validationCode = _validationcode.text,
                          ),
                        );
                      else
                        setState(() {
                          _autoValidate = true;
                        });
                    }
                  },
                )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFinalStep(BuildContext context, SignupFormFinalState state) {
    final localization = context.getAppLocalizationsOrThrow();
    final textStyle = Theme.of(context).textTheme.titleLarge;
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(42),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              //localization.signup_welcomeTitle.replaceFirst('%s', state.createdAgent.firstName.vn ?? ""),
              localization.signup_welcomeTitle(state.createdAgent.firstName.vn ?? ""),
              style: textStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 24,
            ),
            Text(
              localization.signup_welcomeText,
              style: textStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 42,
            ),
            ElevatedButton(
              child: Text(localization.signup_loginBtn),
              onPressed: () {
                Navigator.pop(context);
              },
            )
          ],
        ),
      ),
    );
  }
}
