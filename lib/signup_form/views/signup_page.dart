import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/signup_form/bloc/signup_form_bloc.dart';
import 'package:topbrokers/signup_form/views/signup_form.dart';
//import 'package:flutter_gen/gen_l10n/app_localizations.dart'; // Add this line

class SignupPage extends StatelessWidget {
  const SignupPage({Key? key}) : super(key: key ?? AgentorKeys.loginPage);

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();

    return BlocProvider<SignupFormBloc>(
      create: (context) => SignupFormBloc(localizations: localizations),
      child: Scaffold(
        appBar: AppBar(
          title: Text(localizations.signup_title),
          centerTitle: true,
          actions: [],
        ),
        body: WidthLimiter(
          child: SignupForm(),
        ),
      ),
    );
  }
}
