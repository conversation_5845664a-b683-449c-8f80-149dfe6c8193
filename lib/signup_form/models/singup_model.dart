enum SignupFormModelStep {
  ///
  /// Solicitar eMail.
  /// Al apretar "continuar" se genera un código de validación (que se envía por correo electrónico) y se navega al paso siguiente
  /// * Si eMail ya existe, "continuar" fallará con el mensaje de error
  ///   "la dirección de email ya está registrada en topbrokers"
  step1_inputEmail,

  ///
  /// Se solicitan los datos de la nueva cuenta y el código de validación que se habrá enviado
  /// por correo electrónico
  ///
  step2_fillData,

  ///
  /// La cuenta se ha creado OK
  ///
  step3_allDone,
}

class SignupFormModel {
  SignupFormModelStep actualStep;

  String email;
  String mobile;
  String onetimeCode;
  String validationCode;
  String firstName;
  String lastName;
  String password;
  String password2;
  bool eulaAccepted;

  SignupFormModel({
    this.email = "",
    this.mobile = "",
    this.onetimeCode = "",
    this.validationCode = "",
    this.firstName = "",
    this.lastName = "",
    this.password = "",
    this.password2 = "",
    this.eulaAccepted = false,
    this.actualStep = SignupFormModelStep.step1_inputEmail,
  });

  SignupFormModel copy() => SignupFormModel(
        mobile: mobile,
        email: email,
        onetimeCode: onetimeCode,
        validationCode: validationCode,
        firstName: firstName,
        lastName: lastName,
        password: password,
        password2: password2,
        eulaAccepted: eulaAccepted,
        actualStep: actualStep,
      );
}
