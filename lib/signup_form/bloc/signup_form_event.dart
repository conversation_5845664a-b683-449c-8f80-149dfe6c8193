part of 'signup_form_bloc.dart';

abstract class SignupFormEvent extends Equatable {
  const SignupFormEvent();

  @override
  List<Object> get props => [];
}

class SignupFormOnLoad extends SignupFormEvent {
  SignupFormOnLoad() : super();
}

///
///  Avanzar al siguiente paso del "wizzard"
///
class SignupFormOnNext extends SignupFormEvent {
  final SignupFormModel formData;
  SignupFormOnNext({required this.formData}) : super();
}

class SignupFormOnPrevious extends SignupFormEvent {
  final SignupFormModel formData;
  SignupFormOnPrevious({required this.formData}) : super();
}