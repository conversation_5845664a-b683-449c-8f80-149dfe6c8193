import 'package:agentor_deps/agentor_deps.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/signup_form/models/singup_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/generated/app_localizations.dart';
part 'signup_form_event.dart';
part 'signup_form_state.dart';

///
/// Gestiona estados y eventos para el formulario de login [login_form]
/// Además de su trabajo interno con el formulario, notifica a [SessionBloc]
/// cuándo el usuario ha realizado un log-in para que la aplicación actúen en consecuencia
///

class SignupFormBloc extends Bloc<SignupFormEvent, SignupFormState> {
  final api = Deps.solve<ApiServices>();
  final AppLocalizations localizations;

  SignupFormBloc({required this.localizations}) : super(SignupFormInitialState()) {
    this.add(SignupFormOnLoad());
  }

  @override
  Stream<Transition<SignupFormEvent, SignupFormState>> transformEvents(
    Stream<SignupFormEvent> events,
    TransitionFunction<SignupFormEvent, SignupFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<SignupFormState> mapEventToState(SignupFormEvent event) async* {
    final state = this.state;
    if (event is SignupFormOnLoad) {
      yield* _mapOnLoad(state, event);
    } else if (event is SignupFormOnNext && state is SignupFormLoadedState) {
      yield* _mapOnNext(state, event);
    } else if (event is SignupFormOnPrevious && state is SignupFormLoadedState) {
      yield* _mapOnPrevious(state, event);
    }
  }

  Stream<SignupFormState> _mapOnLoad(SignupFormState state, SignupFormOnLoad event) async* {
    yield SignupFormLoadedState(
      step: SignupFormModelStep.step1_inputEmail,
      formData: SignupFormModel(),
    );
  }

  Stream<SignupFormState> _mapOnPrevious(SignupFormLoadedState state, SignupFormOnPrevious event) async* {
    switch (state.step) {
      case SignupFormModelStep.step2_fillData:
        // Volver al paso 1
        yield SignupFormLoadedState(
          step: SignupFormModelStep.step1_inputEmail,
          formData: state.formData
            ..validationCode = ""
            ..eulaAccepted = false,
        );
        break;
      default:
        yield state;
        break;
    }
  }

  Stream<SignupFormState> _mapOnNext(SignupFormLoadedState state, SignupFormOnNext event) async* {
    try {
      switch (state.step) {
        case SignupFormModelStep.step1_inputEmail:
          // comprobar código de validación onetime
          print("----------------------------------");
          print("onetimeCode: ${event.formData.onetimeCode}");
          bool onetimeSuccess = await api.getOnetime(event.formData.onetimeCode);
          
          print("onetimeSuccess: $onetimeSuccess");
          print("----------------------------------");
          if (onetimeSuccess) {
            // Aquí toca generar una clave
            await api.postPresignup(event.formData.email);
            // Siguiente paso
            yield SignupFormLoadedState(
              step: SignupFormModelStep.step2_fillData,
              formData: event.formData, //SignupFormModel(email: event.formData.email),
              showResendCodeButton: false,
            );
          } else {
            yield SignupFormFailureState.fromLoadedState(state, error: localizations.signup_err_onetimecodeinvalid);
          }
          break;
        case SignupFormModelStep.step2_fillData:

          // Crear cuenta
          final newAgent = await api.postAgent(
              AgentDto(
                email: Some(event.formData.email),
                mobile: Some(event.formData.mobile),
                firstName: Some(event.formData.firstName),
                lastName: Some(event.formData.lastName),
              ),
              event.formData.validationCode,
              ScrtyCredentialsDto(password: Some(event.formData.password)));
          // Update onetime
          final onetimeupdate = await api.postOnetime(event.formData.onetimeCode, event.formData.email);
          print("onetimeupdate: $onetimeupdate");
          // Siguiente paso
          yield SignupFormFinalState(createdAgent: newAgent);
          break;
        default:
          yield state;
          break;
      }

      //_globalBloc.add(GlobalOnLoggedIn(token: token));
    } on ApiEmailAlredyExistException {
      yield SignupFormFailureState.fromLoadedState(state, error: localizations.signup_err_emailalredyexists);
    } on ApiUnknownValidationCodeException {
      yield SignupFormFailureState.fromLoadedState(
        state.copyWith(showResendButton: true),
        error: localizations.signup_err_validationcodeinvalid,
      );
    } on ApiException catch (e) {
      yield SignupFormFailureState.fromLoadedState(state,
          error: localizations.common_err_unexpected_Msg(e.message ?? ""));
    } on Exception {
      yield SignupFormFailureState.fromLoadedState(state, error: localizations.common_err_unexpected_Tryagain);
    }
  }
}
