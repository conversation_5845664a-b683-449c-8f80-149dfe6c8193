part of 'signup_form_bloc.dart';

int _counter = 0;

class SignupFormState extends Equatable {
  final _instanceId = ++_counter;

  @override
  List<Object> get props => [_instanceId];
}

/// En preparación (cargando)
class SignupFormInitialState extends SignupFormState {}

class SignupFormFinalState extends SignupFormState {
  final AgentDto createdAgent;
  SignupFormFinalState({required this.createdAgent}) : super();
}

/// Todo listo para trabajar en la vista
class SignupFormLoadedState extends SignupFormState {
  final SignupFormModelStep step;
  final SignupFormModel formData;
  final bool showResendCodeButton;

  SignupFormLoadedState({
    required this.step,
    required this.formData,
    this.showResendCodeButton = false,
  });

  SignupFormLoadedState copyWith({
    SignupFormModelStep? step,
    SignupFormModel? formData,
    bool? showResendButton,
  }) {
    return SignupFormLoadedState(
      step: step ?? this.step,
      formData: formData ?? this.formData,
      showResendCodeButton: showResendButton ?? this.showResendCodeButton,
    );
  }
}

/// Hay un error en el formulario actual
class SignupFormFailureState extends SignupFormLoadedState {
  final String error;

  SignupFormFailureState({
    required this.error,
    required SignupFormModelStep step,
    required SignupFormModel formData,
    required bool showResendButton,
  }) : super(step: step, formData: formData, showResendCodeButton: showResendButton);

  factory SignupFormFailureState.fromLoadedState(SignupFormLoadedState src, {required String error}) {
    return SignupFormFailureState(
        error: error, step: src.step, formData: src.formData, showResendButton: src.showResendCodeButton);
  }
  @override
  List<Object> get props => [_instanceId, error];
}

/// Todo ha ido OK
class SignupFormSuccessState extends SignupFormState {}
