import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/propertymedia/bloc/propertymedia_uploader_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

class PropertymediaUploader extends StatefulWidget {
  // Picked file to be uploaded
  final PickedFile pickedImage;
  final String offerId;
  final void Function(String error) onError;
  final void Function(PropertymediaDto propertymedia) onUploaded;
  PropertymediaUploader(
      {Key? key, required this.offerId, required this.pickedImage, required this.onError, required this.onUploaded})
      : super(key: key);

  @override
  _PropertymediaUploaderState createState() => _PropertymediaUploaderState();
}

class _PropertymediaUploaderState extends State<PropertymediaUploader> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => PropertymediaUploaderBloc.fromContext(context)
        ..add(
          PropertymediauploaderOnUpload(
            offerId: widget.offerId,
            pickedImage: widget.pickedImage,
            onUploaded: widget.onUploaded,
            onError: widget.onError,
          ),
        ),
      child: BlocConsumer<PropertymediaUploaderBloc, PropertymediauploaderState>(
        listener: (context, state) {},
        builder: (context, state) {
          if (state is PropertymediauploaderInitialState) {
            return const Center(child: CircularProgressIndicator(backgroundColor: Colors.grey));
          } else if (state is PropertymediauploaderUploadingState) {
            return const Center(child: CircularProgressIndicator(backgroundColor: Colors.teal));
          } else if (state is PropertymediauploaderUploadedState) {
            return const Center(child: Icon(Icons.check, color: Colors.teal));
          } else if (state is PropertymediauploaderFailureState) {
            return const Center(child: Icon(Icons.error, color: Colors.red));
          } else {
            return const Center(child: Text('???'));
          }
        },
      ),
    );
  }
}
