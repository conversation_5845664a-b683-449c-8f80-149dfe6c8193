import 'dart:typed_data';

import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:image/image.dart' as imagePackage;
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
part 'propertymedia_uploader_event.dart';
part 'propertymedia_uploader_state.dart';

///
/// Gestiona estados y eventos widget de subida de fichero
///

const _c_msgerror_processingfile = "Problemas procesando el fichero";
const _c_msgerror_badextension = "Solo admitimos imagenes jpeg y png";
const _c_msgerror_uploading = "Problemas al intentar subir la imagen";

class PropertymediaUploaderBloc extends Bloc<PropertymediauploaderEvent, PropertymediauploaderState> {
  final AppModelsNS appModels;

  PropertymediaUploaderBloc({required this.appModels}) : super(PropertymediauploaderInitialState());

  factory PropertymediaUploaderBloc.fromContext(BuildContext context) =>
      PropertymediaUploaderBloc(appModels: Provider.of<AppModelsNS>(context, listen: false));

  @override
  Stream<Transition<PropertymediauploaderEvent, PropertymediauploaderState>> transformEvents(
    Stream<PropertymediauploaderEvent> events,
    TransitionFunction<PropertymediauploaderEvent, PropertymediauploaderState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<PropertymediauploaderState> mapEventToState(PropertymediauploaderEvent event) async* {
    if (event is PropertymediauploaderOnUpload) {
      yield* _mapOnUpload(state, event);
    }
  }

  @override
  @mustCallSuper
  Future<void> close() async => super.close();

  Stream<PropertymediauploaderState> _mapOnUpload(
      PropertymediauploaderState state, PropertymediauploaderOnUpload event) async* {
    try {
      final pickedFileBinary = await event.pickedImage.readAsBytes();
      final decoder = imagePackage.findDecoderForData(pickedFileBinary);
      final mimetype = (decoder is imagePackage.JpegDecoder)
          ? "image/jpeg"
          : (decoder is imagePackage.PngDecoder)
              ? "image/png"
              : null;
      final filename = (decoder is imagePackage.JpegDecoder)
          ? "file.jpg"
          : (decoder is imagePackage.PngDecoder)
              ? "file.png"
              : null;
      if (mimetype == null) {
        yield PropertymediauploaderFailureState(error: _c_msgerror_badextension);
        noThrow(() => event.onError(_c_msgerror_badextension));
      } else {
        yield PropertymediauploaderUploadingState(pickedFileBinary: pickedFileBinary);
        try {
          final propertyMedia =
              await appModels.createOfferPropertyMedia(event.offerId, pickedFileBinary, filename, mimetype);
          yield PropertymediauploaderUploadedState();
          noThrow(() => event.onUploaded(propertyMedia));
        } catch (e) {
          yield PropertymediauploaderFailureState(error: _c_msgerror_uploading);
          noThrow(() => event.onError(_c_msgerror_uploading));
        }
      }
    } on Exception {
      yield PropertymediauploaderFailureState(error: _c_msgerror_processingfile);
      noThrow(() => event.onError(_c_msgerror_processingfile));
    }
  }

  void noThrow(Function() x) {
    try {
      x();
    } catch (e) {
      print("$e");
    }
  }
}
