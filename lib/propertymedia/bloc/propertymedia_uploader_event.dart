part of 'propertymedia_uploader_bloc.dart';

abstract class PropertymediauploaderEvent extends Equatable {
  const PropertymediauploaderEvent();

  @override
  List<Object> get props => [];
}

class PropertymediauploaderOnUpload extends PropertymediauploaderEvent {
  final String offerId;
  final PickedFile pickedImage;
  final void Function(PropertymediaDto) onUploaded;
  final void Function(String) onError;

  PropertymediauploaderOnUpload({
    required this.offerId,
    required this.pickedImage,
    required this.onUploaded,
    required this.onError,
  }) : super();

  @override
  List<Object> get props => [pickedImage];
}
