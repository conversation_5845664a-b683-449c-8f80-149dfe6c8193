part of 'propertymedia_uploader_bloc.dart';

class PropertymediauploaderState extends Equatable {
  @override
  List<Object> get props => [];
}

class PropertymediauploaderInitialState extends PropertymediauploaderState {}

class PropertymediauploaderUploadingState extends PropertymediauploaderState {
  final Uint8List pickedFileBinary;

  PropertymediauploaderUploadingState({required this.pickedFileBinary}) : super();
}

class PropertymediauploaderUploadedState extends PropertymediauploaderState {}

class PropertymediauploaderFailureState extends PropertymediauploaderState {
  final String error;

  PropertymediauploaderFailureState({required this.error});

  @override
  List<Object> get props => [error];
}
