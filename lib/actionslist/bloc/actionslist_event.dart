part of 'actionslist_bloc.dart';

int _sequence = 0;
abstract class ActionslistEvent extends Equatable {
  const ActionslistEvent();

  @override
  List<Object> get props => [];
}

class ActionslistOnRefresh extends ActionslistEvent {}

class ActionslistOnOffersUpdated extends ActionslistEvent {
  final List<OfferDto> offers;
  ActionslistOnOffersUpdated({required this.offers}):super();
}
class ActionslistOnContactsUpdated extends ActionslistEvent {
  final List<ContactDto> contacts;
  ActionslistOnContactsUpdated({required this.contacts}):super();
}
class ActionslistOnFetch extends ActionslistEvent {}

class ActionslistOnFilteroptionSelected extends ActionslistEvent {
  final _mySequence = ++_sequence;
  final ActionslistFilterOption filterOption;
  
  ActionslistOnFilteroptionSelected(this.filterOption);

  @override
  List<Object> get props => [_mySequence, filterOption];

  @override
  String toString() => "ActionslistOnFilteroptionSelected { selected: $filterOption }";
}
class ActionslistOnActionAdded extends ActionslistEvent {
  final ActionDto contact;

  const ActionslistOnActionAdded(this.contact);

  @override
  List<Object> get props => [contact];

  @override
  String toString() => "ActionAdded {  $contact }";
}

class ActionslistOnActionUpdated extends ActionslistEvent {
  final ActionDto action;

  const ActionslistOnActionUpdated(this.action);

  @override
  List<Object> get props => [action];

  @override
  String toString() => "ActionUpdated { $action  }";
}
