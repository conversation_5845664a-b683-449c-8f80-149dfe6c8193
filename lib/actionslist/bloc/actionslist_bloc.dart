import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/actionslist/actionslist.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/common/app_models_ns.dart';
part 'actionslist_event.dart';
part 'actionslist_state.dart';

const defaultFilterOptions = <ActionslistFilterOption>[ActionslistFilterOption.bydate_past, ActionslistFilterOption.done_and_undone];

class ActionslistBloc extends Bloc<ActionslistEvent, ActionslistState> {
  static const _PAGE_SIZE = 20;
  final AppModelsNS appModels;

  /// Identificador opcional de la oferta a la que pertenecen las acciones manejadas por el bloque.
  final String? offerId;

  /// Identificador opcional del contacto al que pertenecen las acciones manejadas por el bloque.
  final String? contactId;

  StreamSubscription<ModelsChannelState>? _modelsChannelSubscription;

  ActionslistBloc({
    required ModelsChannelBloc modelsChannel,
    required this.appModels,
    this.offerId,
    this.contactId,
    Set<ActionslistFilterOption>? filter,
  }) : super(ActionslistState(filter: filter ?? Set.from(defaultFilterOptions))) {
    _modelsChannelSubscription = modelsChannel.stream.listen((state) {
      // Esto causará que revisemos si alguna acción de la lista usa esta oferta
      if (state is ModelsChannelUpdatedState<ContactDto>) {
        this.add(ActionslistOnContactsUpdated(contacts: state.entities));
      } else if (state is ModelsChannelUpdatedState<OfferDto>) {
        this.add(ActionslistOnOffersUpdated(offers: state.entities));
      } else if (state is ModelsChannelUpdatedState<ActionDto> || state is ModelsChannelCreatedState<ActionDto> || state is ModelsChannelDeletedState<ActionDto>) {
        this.add(ActionslistOnRefresh());
      }
    });
  }

  factory ActionslistBloc.create(
    BuildContext context, {
    String? offerId,
    String? contactId,
    Set<ActionslistFilterOption>? filter,
  }) {
    return ActionslistBloc(
        modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
        appModels: Provider.of<AppModelsNS>(context, listen: false),
        offerId: offerId,
        contactId: contactId,
        filter: filter);
  }
  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _modelsChannelSubscription?.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<ActionslistEvent, ActionslistState>> transformEvents(
    Stream<ActionslistEvent> events,
    TransitionFunction<ActionslistEvent, ActionslistState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ActionslistState> mapEventToState(ActionslistEvent event) async* {
    if (event is ActionslistOnRefresh) {
      yield* _mapOnRefreshToState(state, event);
    } else if (event is ActionslistOnFilteroptionSelected) {
      yield* _mapOnFilterOptionSelectedToState(state, event);
    } else if (event is ActionslistOnFetch) {
      yield* _mapOnFetchToState(state, event);
    } else if (event is ActionslistOnActionAdded) {
      yield* _mapOnActionAddedToState(state, event);
    } else if (event is ActionslistOnActionUpdated) {
      yield* _mapOnActionUpdatedToState(state, event);
    } else if (event is ActionslistOnOffersUpdated) {
      yield* _mapOnOfferUpdatedToState(state, event);
    } else if (event is ActionslistOnContactsUpdated) {
      yield* _mapOnContactUpdatedToState(state, event);
    }
  }

  Stream<ActionslistState> _mapOnActionAddedToState(ActionslistState state, ActionslistOnActionAdded event) async* {
    try {
      // No tenemos en cuenta los datos de la nueva oferta:  recargamos la lista
      final actions = await _listActions(offset: 0);
      yield state.copyWith(status: ActionslistStatus.success, actions: actions, hasReachedMax: actions.length != _PAGE_SIZE);
    } on Exception {
      yield state.copyWith(status: ActionslistStatus.failure);
    }
  }

  Stream<ActionslistState> _mapOnActionUpdatedToState(ActionslistState state, ActionslistOnActionUpdated event) async* {
    try {
      final updated = event.action;

      yield state.copyWith(
        status: ActionslistStatus.success,
        actions: state.actions.map((a) => a.id.vn == updated.id.vn ? updated : a).toList(),
      );
    } on Exception {
      yield state.copyWith(status: ActionslistStatus.failure);
    }
  }

  Stream<ActionslistState> _mapOnOfferUpdatedToState(ActionslistState state, ActionslistOnOffersUpdated event) async* {
    try {
      final offers = Map.fromIterable(event.offers, key: (c) => c.id.v, value: (c) => c);
      final patchedActions = state.actions.map((action) {
        final offerId = action.offer.vn?.id.vn;
        if (offerId == null || !offers.containsKey(offerId))
          return action;
        else
          return action.copyWith(offer: Some(offers[offerId]));
      }).toList();
      yield state.copyWith(actions: patchedActions);
    } on Exception {
      // NADA
    }
  }

  Stream<ActionslistState> _mapOnContactUpdatedToState(
    ActionslistState state,
    ActionslistOnContactsUpdated event,
  ) async* {
    try {
      // No tenemos en cuenta los datos de la nueva oferta:  recargamos la lista
      final contacts = Map.fromIterable(event.contacts, key: (c) => c.id.v, value: (c) => c);
      final patchedActions = state.actions.map((action) {
        final contactId = action.contact.vn?.id.vn;
        if (contactId == null || !contacts.containsKey(contactId))
          return action;
        else
          return action.copyWith(contact: Some(contacts[contactId]));
      }).toList();
      yield state.copyWith(actions: patchedActions);
    } on Exception {
      // NADA
    }
  }

  Stream<ActionslistState> _mapOnRefreshToState(ActionslistState state, ActionslistOnRefresh event) async* {
    try {
      final actions = await _listActions(filter: state.filter, offset: 0);
      yield state.copyWith(
        status: ActionslistStatus.success,
        actions: actions,
        hasReachedMax: actions.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: ActionslistStatus.failure);
    }
  }

  Stream<ActionslistState> _mapOnFilterOptionSelectedToState(ActionslistState state, ActionslistOnFilteroptionSelected event) async* {
    try {
      final newFilter = Set<ActionslistFilterOption>.from([
        if (event.filterOption == ActionslistFilterOption.bydate_today ||
            event.filterOption == ActionslistFilterOption.bydate_past ||
            event.filterOption == ActionslistFilterOption.bydate_future)
          event.filterOption
        else if (state.filter.contains(ActionslistFilterOption.bydate_today))
          ActionslistFilterOption.bydate_today
        else if (state.filter.contains(ActionslistFilterOption.bydate_past))
          ActionslistFilterOption.bydate_past
        else if (state.filter.contains(ActionslistFilterOption.bydate_future))
          ActionslistFilterOption.bydate_future,
        if (event.filterOption == ActionslistFilterOption.done ||
            event.filterOption == ActionslistFilterOption.undone ||
            event.filterOption == ActionslistFilterOption.done_and_undone)
          event.filterOption
        else if (state.filter.contains(ActionslistFilterOption.done))
          ActionslistFilterOption.done
        else if (state.filter.contains(ActionslistFilterOption.undone))
          ActionslistFilterOption.undone
        else if (state.filter.contains(ActionslistFilterOption.done_and_undone))
          ActionslistFilterOption.done_and_undone,
      ]);

      final actions = await _listActions(filter: newFilter, offset: 0);
      yield state.copyWith(
        status: ActionslistStatus.success,
        filter: newFilter,
        actions: actions,
        hasReachedMax: actions.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: ActionslistStatus.failure);
    }
  }

  Stream<ActionslistState> _mapOnFetchToState(ActionslistState state, ActionslistOnFetch event) async* {
    if (state.hasReachedMax) yield state;
    try {
      if (state.status == ActionslistStatus.initial) {
        final actions = await _listActions(filter: state.filter, offset: 0);
        yield state.copyWith(
          status: ActionslistStatus.success,
          actions: actions,
          hasReachedMax: actions.length != _PAGE_SIZE,
        );
      } else {
        // Este punto sirve tanto en estado success como failure si se reintentase
        final actions = await _listActions(filter: state.filter, offset: state.actions.length);
        yield actions.isEmpty
            ? state.copyWith(hasReachedMax: true)
            : state.copyWith(
                status: ActionslistStatus.success,
                actions: List.of(state.actions)..addAll(actions),
                hasReachedMax: false,
              );
      }
    } on Exception catch (e) {
      print("*+*+* Exception _mapOnFetchToState: $e");
      yield state.copyWith(status: ActionslistStatus.failure);
    }
  }

  Future<List<ActionDto>> _listActions({Set<ActionslistFilterOption>? filter, int offset = 0}) async {
    try {
      final nnFilter = filter ?? Set();
      Optional<bool> doneFilter = nnFilter.contains(ActionslistFilterOption.done)
          ? Some(true)
          : nnFilter.contains(ActionslistFilterOption.undone)
              ? Some(false)
              : None();
      Optional<DateTime> minWhen =
          nnFilter.contains(ActionslistFilterOption.bydate_today) || nnFilter.contains(ActionslistFilterOption.bydate_future) ? Some(startOfToday()) : None();
      Optional<DateTime> maxWhen = nnFilter.contains(ActionslistFilterOption.bydate_past)
          ? Some(endOfToday())
          : nnFilter.contains(ActionslistFilterOption.bydate_today)
              ? Some(endOfToday())
              : None();

      /// Las acciones "pasadas" se ordenan de actuales a antiguas.  Las acciones "futuras" se ordenan de actuales a futuras.
      Optional<AscOrDesc> orderbyWhen =
          (nnFilter.contains(ActionslistFilterOption.bydate_today) || nnFilter.contains(ActionslistFilterOption.bydate_future) || nnFilter.contains(ActionslistFilterOption.undone))
              ? Some(AscOrDesc.desc)
              : Some(AscOrDesc.asc);

      ActionsListFilter srvFilter = ActionsListFilter(
        done: doneFilter,
        minWhen: minWhen,
        maxWhen: maxWhen,
        orderbyWhen: orderbyWhen,
        offerId: offerId == null ? None() : Some(offerId),
        contactId: contactId == null ? None() : Some<String>(contactId as String),
        includeOfferDetails: True,
      );
      return await appModels.listActions(filter: srvFilter, offset: offset, limit: _PAGE_SIZE);
    } catch (e) {
      print("*+*+* Error _listActions: $e");
      throw e;
    }
  }

  static DateTime endOfToday() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 23, 59, 59, 999, 999);
  }

  static DateTime startOfToday() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }
}
