part of 'actionslist_bloc.dart';

enum ActionslistStatus { initial, success, failure }

class ActionslistState extends Equatable {
  ActionslistState({
    this.status = ActionslistStatus.initial,
    this.actions = const <ActionDto>[],
    this.hasReachedMax = false,
    required this.filter,
  });

  // Actual status of the screen
  final ActionslistStatus status;
  // The list of actual fetched actions
  final List<ActionDto> actions;
  // There is no more actions to be fetched
  final bool hasReachedMax;
  //
  final Set<ActionslistFilterOption> filter;

  ActionslistState copyWith({
    ActionslistStatus? status,
    List<ActionDto>? actions,
    bool? hasReachedMax,
    Set<ActionslistFilterOption>? filter,
  }) {
    return ActionslistState(
      status: status ?? this.status,
      actions: actions ?? this.actions,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      filter: filter ?? this.filter,
    );
  }

  factory ActionslistState.create({
    ActionslistStatus status = ActionslistStatus.initial,
    List<ActionDto> actions = const <ActionDto>[],
    bool hasReachedMax = false,
    required Set<ActionslistFilterOption> filter,
  }) {
    return ActionslistState(
      status: status,
      actions: actions,
      hasReachedMax: hasReachedMax,
      filter: filter,
    );
  }

  @override
  List<Object> get props => [status, hasReachedMax, actions, filter];
}

class ActionslistLoadFailure extends ActionslistState {
  ActionslistLoadFailure({
    required filter,
  }) : super(filter: filter);
}
