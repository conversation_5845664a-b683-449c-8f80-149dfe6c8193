import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:topbrokers/common/datetime_helper.dart';
import 'package:topbrokers/common/widgets/contact_long_link.dart';
import 'package:topbrokers/common/widgets/gravatar.dart';
import 'package:topbrokers/common/widgets/offer_long_link.dart';
import 'package:topbrokers/custom_app_icons.dart';

class ActionslistItem extends StatelessWidget {
  const ActionslistItem({
    Key? key,
    required this.action,
    this.isSelected = false,
    this.showOffer = true,
    required this.onTap,
  }) : super(key: key);

  final ActionDto action;
  final bool showOffer;
  final bool isSelected;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    final isDense = true;
    final theme = Theme.of(context);
    final iconSize = (theme.iconTheme.size ?? 40) * (isDense ? 0.8 : 1);
    final isDone = action.done.vn ?? false;
    final isService = action.type.vn?.service.vn != null;
    final offer = action.offer.vn;
    final contact = action.contact.vn;
    return Card(
      child: ListTile(
        leading: Gravatar(
          size: iconSize,
          child: Icon(isService ? Icons.auto_fix_high : CustomAppIcons.action, size: iconSize),
          led: isDone ? Icons.check_circle_rounded : null,
          ledSize: iconSize * 0.4,
          ledColor: Colors.green,
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                action.type.vn?.label.vn?.localized ?? "",
                style: isDone
                    ? theme.textTheme.bodyMedium?.copyWith(
                        decoration: TextDecoration.lineThrough,
                        decorationColor: Colors.red,
                        decorationThickness: 2,
                      )
                    : theme.textTheme.bodyLarge,
              ),
            ),
            Expanded(
              child: Text(
                action.when.vn?.formatRelativeToNow() ?? "",
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
        isThreeLine: true,
        subtitle: Column(children: [
          if (action.description.vn != null)
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                action.description.vn ?? "",
                textAlign: TextAlign.left,
              ),
            ),
          if (contact?.id.vn != null)
            ContactLongLink(
              contact: contact!,
              textAlign: TextAlign.left,
            ),
          if (showOffer && offer?.id.vn != null)
            OfferLongLink(
              offer: offer!,
              textAlign: TextAlign.left,
            ),
        ]),
        //subtitle: action?.contact?.vn != null ? ContactListItem(contact: action.contact.v) : Text(""),
        visualDensity: VisualDensity.compact,
        dense: isDense,
        selected: isSelected,
        onTap: onTap,
      ),
    );
  }
}
