import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/actionslist/actionslist.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/common/helpers.dart';
class ActionsFilterButton extends StatelessWidget {
  final bool visible;

  ActionsFilterButton({
    required this.visible,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActionslistBloc, ActionslistState>(builder: (context, state) {
      final button = _Button(
        onSelected: (ActionslistFilterOption filterOption) {
          BlocProvider.of<ActionslistBloc>(context).add(ActionslistOnFilteroptionSelected(filterOption));
        },
        activeOptions: state.filter,
      );
      return AnimatedOpacity(
        opacity: visible ? 1.0 : 0.0,
        duration: Duration(milliseconds: 150),
        child: visible ? button : IgnorePointer(child: button),
      );
    });
  }
}

class _<PERSON><PERSON> extends StatelessWidget {
  const _Button({
    Key? key,
    required this.onSelected,
    required this.activeOptions,
  }) : super(key: key);

  final PopupMenuItemSelected<ActionslistFilterOption> onSelected;
  final Set<ActionslistFilterOption> activeOptions;

  @override
  Widget build(BuildContext context) {
    final apploc = context.apploc;
    return PopupMenuButton<ActionslistFilterOption>(
      key: AgentorKeys.actionsFilterButton,
      icon: Icon(Icons.filter_list),
      tooltip: apploc.actions_filterTooltip,
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<ActionslistFilterOption>(
          //key: AgentorKeys.allFilter,
          value: ActionslistFilterOption.bydate_today,
          child: _buildMenuItemWidget(
            apploc.actions_filter_todayLabel,
            activeOptions.contains(ActionslistFilterOption.bydate_today),
          ),
        ),
        PopupMenuItem<ActionslistFilterOption>(
          //key: AgentorKeys.allFilter,
          value: ActionslistFilterOption.bydate_future,
          child: _buildMenuItemWidget(
            apploc.actions_filter_fromTodayLabel,
            activeOptions.contains(ActionslistFilterOption.bydate_future),
          ),
        ),
        PopupMenuItem<ActionslistFilterOption>(
          //key: AgentorKeys.allFilter,
          value: ActionslistFilterOption.bydate_past,
          child: _buildMenuItemWidget(
            apploc.actions_filter_untilTodayLabel,
            activeOptions.contains(ActionslistFilterOption.bydate_past),
          ),
        ),
        PopupMenuDivider(),
        PopupMenuItem<ActionslistFilterOption>(
          value: ActionslistFilterOption.done,
          child: _buildMenuItemWidget(
            apploc.actions_filter_doneLabel,
            activeOptions.contains(ActionslistFilterOption.done),
          ),
        ),
        PopupMenuItem<ActionslistFilterOption>(
          value: ActionslistFilterOption.undone,
          child: _buildMenuItemWidget(
            apploc.actions_filter_undoneLabel,
            activeOptions.contains(ActionslistFilterOption.undone),
          ),
        ),
        PopupMenuItem<ActionslistFilterOption>(
          value: ActionslistFilterOption.done_and_undone,
          child: _buildMenuItemWidget(
            apploc.actions_filter_doneAndUndoneLabel,
            activeOptions.contains(ActionslistFilterOption.done_and_undone),
          ),
        )
      ],
    );
  }

  static Widget _buildMenuItemWidget(String text, bool active) {
    return Row(
      children: [
        if (active)
          Icon(Icons.check_box_outlined, color: Colors.grey)
        else
          Icon(Icons.check_box_outline_blank, color: Colors.grey),
        Text(text)
      ],
    );
  }
}
