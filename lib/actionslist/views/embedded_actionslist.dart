import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/action_edit/models/action_edit_page_params.dart';
import 'package:topbrokers/actionslist/bloc/actionslist_bloc.dart';
import 'package:topbrokers/actionslist/model/actionslist_filteroption.dart';
import 'package:topbrokers/actionslist/widgets/widgets.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/routes.dart';
import 'package:topbrokers/serviceaction_sheet/models/serviceaction_sheet_page_params.dart';

class EmbeddedActionsList extends StatefulWidget {
  final String? offerId;
  final String? contactId;

  EmbeddedActionsList({
    this.offerId,
    this.contactId,
  }) : super();

  @override
  _EmbeddedActionsListState createState() => _EmbeddedActionsListState();
}

class _EmbeddedActionsListState extends State<EmbeddedActionsList> {
  final _scrollController = ScrollController();
  late ActionslistBloc _actionsBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActionslistBloc>(
      create: (context) {
        _actionsBloc = ActionslistBloc.create(
          context,
          offerId: widget.offerId,
          contactId: widget.contactId,
          filter: Set.from([ActionslistFilterOption.done_and_undone, ActionslistFilterOption.bydate_all]),
        )..add(ActionslistOnFetch());
        return _actionsBloc;
      },
      child: BlocConsumer<ActionslistBloc, ActionslistState>(
        listener: (context, state) {},
        builder: (context, state) {
          final apploc = context.apploc;
          if (state.status == ActionslistStatus.failure)
            return ConstrainedBox(
              constraints: BoxConstraints(maxHeight: 320, minHeight: 20),
              child: Center(
                child: Text(apploc.actions_errLoading),
              ),
            );
          else if (state.status == ActionslistStatus.success && state.actions.isEmpty)
            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    apploc.actions_noActions,
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            );
          else if (state.status == ActionslistStatus.success)
            return SizedBox(
              height: 320,
              child: RefreshIndicator(
                onRefresh: () async {
                  _actionsBloc.add(ActionslistOnRefresh());
                },
                child: ListView.builder(
                  //key: AgentorKeys.actionsList,
                  physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                  itemBuilder: (BuildContext context, int index) {
                    if (index >= state.actions.length)
                      return BottomLoader();
                    else
                      return ActionslistItem(
                        action: state.actions[index],
                        showOffer: widget.offerId == null,
                        onTap: () {
                          final actionId = state.actions[index].id.v;
                          final isService = state.actions[index].type.vn?.service.vn != null;
                          if (isService) {
                            Navigator.pushNamed(
                              context,
                              AgentorRoutes.showServiceaction,
                              arguments: ServiceactionSheetPageParams(id: actionId),
                            );
                          } else {
                            Navigator.pushNamed(
                              context,
                              AgentorRoutes.editAction,
                              arguments: ExistingActionEditParams(id: actionId),
                            );
                          }
                        },
                      );
                  },
                  itemCount: state.hasReachedMax ? state.actions.length : state.actions.length + 1,
                  controller: _scrollController,
                ),
              ),
            );
          else
            return const Center(
              child: CircularProgressIndicator(),
            );
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // assert(_actionsBloc != null, "unitialized _actionsBlock when managing _onScrooo");
    if (_isBottom) {
      _actionsBloc.add(ActionslistOnFetch());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) {
      return false;
    } else {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.offset;
      return currentScroll >= (maxScroll * 0.9);
    }
  }
}
