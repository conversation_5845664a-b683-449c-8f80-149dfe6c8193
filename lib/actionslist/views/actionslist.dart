import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/action_edit/models/action_edit_page_params.dart';
import 'package:topbrokers/actionslist/bloc/actionslist_bloc.dart';
import 'package:topbrokers/actionslist/widgets/widgets.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/routes.dart';
import 'package:topbrokers/serviceaction_sheet/models/serviceaction_sheet_page_params.dart';

class ActionsList extends StatefulWidget {
  @override
  _ActionsListState createState() => _ActionsListState();
}

class _ActionsListState extends State<ActionsList> {
  final _scrollController = ScrollController();
  late ActionslistBloc? _actionsBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _actionsBloc = context.bloc<ActionslistBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();

    return BlocConsumer<ActionslistBloc, ActionslistState>(
      listener: (context, state) {
        if (!state.hasReachedMax && _isBottom) {
          _actionsBloc?.add(ActionslistOnFetch());
        }
      },
      builder: (context, state) {
        switch (state.status) {
          case ActionslistStatus.failure:
            return Center(child: Text(localizations.actions_errLoading));
          case ActionslistStatus.success:
            if (state.actions.isEmpty)
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      localizations.actions_noActionsYet,
                      style: TextStyle(color: Colors.grey),
                    ),
                    Divider(height: 42),
                    ElevatedButton.icon(
                      icon: Icon(
                        CustomAppIcons.action,
                        size: 16,
                      ),
                      label: Text(localizations.actions_addFirstAction),
                      onPressed: () {
                        Navigator.pushNamed(context, AgentorRoutes.addAction);
                      },
                    )
                  ],
                ),
              );
            else
              return RefreshIndicator(
                onRefresh: () async => _actionsBloc?.add(ActionslistOnRefresh()),
                child: ListView.builder(
                  key: AgentorKeys.actionsList,
                  physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                  itemBuilder: (BuildContext context, int index) {
                    if (index >= state.actions.length)
                      return BottomLoader();
                    else
                      return ActionslistItem(
                        action: state.actions[index],
                        onTap: () {
                          final actionId = state.actions[index].id.v;
                          final isService = state.actions[index].type.vn?.service.vn != null;
                          if (isService)
                            Navigator.pushNamed(
                              context,
                              AgentorRoutes.showServiceaction,
                              arguments: ServiceactionSheetPageParams(id: actionId),
                            );
                          else
                            Navigator.pushNamed(
                              context,
                              AgentorRoutes.editAction,
                              arguments: ExistingActionEditParams(id: actionId),
                            );
                        },
                      );
                  },
                  itemCount: state.hasReachedMax ? state.actions.length : state.actions.length + 1,
                  controller: _scrollController,
                ),
              );
          default:
            return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _actionsBloc?.add(ActionslistOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
