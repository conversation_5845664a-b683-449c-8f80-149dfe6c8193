import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class OffersFilterFormModel {
  Optional<bool> sourceIndividual;
  Optional<String> sourceContactPhone;
  Optional<bool> onlyFavourites;
  Optional<CloudOffersListDateIntervalCode> createdAtIntervalCode;
  Optional<DateTime> createdAtMin;
  Optional<DateTime> createdAtMax;
  Optional<String> propertytypeCode;
  Optional<double> propertyM2Min;
  Optional<double> propertyM2Max;
  Optional<bool> saleAllowed;
  Optional<double> saleAmountMin;
  Optional<double> saleAmountMax;
  Optional<bool> rentAllowed;
  Optional<double> rentAmountMin;
  Optional<double> rentAmountMax;
  Optional<PropertyzoneDto> zone;
  Optional<CityDto> city;
  OffersFilterFormModel({
    this.sourceIndividual = const None(),
    this.sourceContactPhone = const None(),
    this.onlyFavourites = const None(),
    this.propertytypeCode = const None(),
    this.propertyM2Min = const None(),
    this.propertyM2Max = const None(),
    this.createdAtIntervalCode = const None(),
    this.createdAtMin = const None(),
    this.createdAtMax = const None(),
    this.rentAllowed = const None(),
    this.rentAmountMin = const None(),
    this.rentAmountMax = const None(),
    this.saleAllowed = const None(),
    this.saleAmountMin = const None(),
    this.saleAmountMax = const None(),
    this.zone = const None(),
    this.city = const None(),
  });
}
