import 'dart:convert';
import 'dart:html';

import 'package:agentor_repositoryns/exception_utils.dart';
import 'package:agentor_repositoryns/models/models.dart';
import 'package:excel/excel.dart';

enum ReportFormatCode { csv, xlms }

class ReportFormat {
  final ReportFormatCode code;
  final MultilingualStrDto name;
  const ReportFormat({required this.code, required this.name});
}

final ReportFormats = <ReportFormat>[
  ReportFormat(code: ReportFormatCode.csv, name: MultilingualStrDto.fromJson({"default": "Csv"})),
  ReportFormat(code: ReportFormatCode.xlms, name: MultilingualStrDto.fromJson({"default": "Excel"})),
];

enum CloudoffersReportCode {
  phonenumbers,
  basicinfo,
}

abstract class CloudOfferReport {
  int get rowsCount;
  static MultilingualStrDto get name => doThrow("Must be implemented");
  static CloudoffersReportCode get code => doThrow("Must be implemented");

  void addOffers(List<OfferDto> offers);

  void save(ReportFormatCode formatCode) {
    switch (formatCode) {
      case ReportFormatCode.csv:
        _saveCsv();
        break;
      case ReportFormatCode.xlms:
        _saveExcel();
        break;
      default:
        break;
    }
  }

  void _saveCsv();
  void _saveExcel();

  static String _toCsvStringValue(String? value) => (value == null) ? "" : '"${value.replaceAll('"', '""')}"';
  static String _toCsvBoolValue(bool? value) => '${value ?? ""}';
  static String _toCsvNumValue(num? value) => '${value ?? ""}';

  static void _saveToExcel(Excel excel, fileName) {
    excel.save(fileName: fileName);
  }

  static void _saveToCsv(String content, String fileName) {
    AnchorElement()
      ..href = '${Uri.dataFromString(content, mimeType: 'text/csv', encoding: utf8)}'
      ..download = fileName
      ..style.display = 'none'
      ..click();
  }
}

class CloudOffersPhonesReport extends CloudOfferReport {
  List<String> _columns = ["PHONENUMBER", "NAME"];
  List<_CloudOffersPhonesReportRow> allRows = [];
  int get rowsCount => allRows.length;
  static MultilingualStrDto get name => MultilingualStrDto.fromJson({"default": "Phone numbers", "es": "Teléfonos"});
  static CloudoffersReportCode get code => CloudoffersReportCode.phonenumbers;

  void addOffers(List<OfferDto> offers) {
    allRows.addAll(offers.map((o) => new _CloudOffersPhonesReportRow.fromOffer(o)));
  }

  void _saveCsv() {
    List<String> csv = [
      _columns.join(", "),
    ]..addAll(
        allRows.where((row) => row.phone != null).map(
              (row) => [
                CloudOfferReport._toCsvStringValue(row.phone),
                CloudOfferReport._toCsvStringValue(row.name),
              ].join(", "),
            ),
      );
    CloudOfferReport._saveToCsv(
      csv.join("\n"),
      "phonesreport_${DateTime.now().toIso8601String()}.csv",
    );
  }

  void _saveExcel() {
    var excel = Excel.createExcel();
    Sheet sheetObject = excel[excel.getDefaultSheet() ?? "Main"];
    sheetObject.appendRow(_columns);
    allRows.forEach((row) {
      sheetObject.appendRow([row.phone, row.name]);
    });
    CloudOfferReport._saveToExcel(
      excel,
      "phonesreport_${DateTime.now().toIso8601String()}.xlsx",
    );
  }
}

class _CloudOffersPhonesReportRow {
  String? phone;
  String? name;

  _CloudOffersPhonesReportRow.fromOffer(OfferDto offer) {
    String? phone = _normalizePhone(offer.source.v.contact.v?.phone.vn);
    String cityName = offer.property.vn?.address.vn?.city.vn?.label.vn?.localized ?? "";
    String zoneName = offer.property.vn?.zone.vn?.name.vn ?? "";

    this.phone = phone;
    this.name = '${offer.id.vn ?? ''}_${cityName}_$zoneName';
  }

  static String? _normalizePhone(String? phone) {
    if (phone == null)
      return null;
    else {
      phone = phone.replaceAll(RegExp(r'\s*'), '');
      return phone.startsWith("+")
          ? phone.substring(1)
          // Muy peligroso suponer que es un teléfono español cuando no se ha indicado "+"
          : _normalizePrefix(phone, "34");
    }
  }

  static String _normalizePrefix(String phone, String prefix) => phone.startsWith(prefix) ? phone : "$prefix$phone";
}

class CloudOffersBasicReport extends CloudOfferReport {
  final _columns = <String>[
    "REF",
    "SOURCE_PHONE_NUMBER",
    "SOURCE_PAGE_URL",
    "ADDR_PROVINCE_NAME",
    "ADDR_CITY_NAME",
    "ZONE_NAME",
    "SALE_ALLOWED",
    "SALE_AMOUNT",
    "RENT_ALLOWED",
    "RENT_AMOUNT",
    "M2",
    "BEDROOMS_COUNT"
  ];
  var _allRows = <_CloudOffersBasicReportRow>[];
  int get rowsCount => _allRows.length;
  static MultilingualStrDto get name =>
      MultilingualStrDto.fromJson({"default": "Basic information", "es": "Información básica"});
  static CloudoffersReportCode get code => CloudoffersReportCode.basicinfo;

  void addOffers(List<OfferDto> offers) {
    _allRows.addAll(offers.map((o) => new _CloudOffersBasicReportRow.fromOffer(o)));
  }

  void _saveExcel() {
    var excel = Excel.createExcel();
    Sheet sheetObject = excel[excel.getDefaultSheet() ?? "Main"];
    sheetObject.appendRow(_columns);
    _allRows.forEach((row) {
      sheetObject.appendRow([
        row.ref,
        row.phone,
        row.url,
        row.provinceName,
        row.cityName,
        row.zoneName,
        row.saleAllowed ? 1 : 0,
        row.saleAmount,
        row.rentAllowed ? 1 : 0,
        row.rentAmount,
        row.m2,
        row.bedrooms,
      ]);
    });
    CloudOfferReport._saveToExcel(
      excel,
      "basicreport_${DateTime.now().toIso8601String()}.xlsx",
    );
  }

  void _saveCsv() {
    final heading = _columns.join(", ");
    final csv = [heading]..addAll(
        _allRows.where((row) => row.phone != null).map(
              (row) => [
                CloudOfferReport._toCsvStringValue(row.ref),
                CloudOfferReport._toCsvStringValue(row.phone),
                CloudOfferReport._toCsvStringValue(row.url),
                CloudOfferReport._toCsvStringValue(row.provinceName),
                CloudOfferReport._toCsvStringValue(row.cityName),
                CloudOfferReport._toCsvStringValue(row.zoneName),
                CloudOfferReport._toCsvBoolValue(row.saleAllowed),
                CloudOfferReport._toCsvNumValue(row.saleAmount),
                CloudOfferReport._toCsvBoolValue(row.rentAllowed),
                CloudOfferReport._toCsvNumValue(row.rentAmount),
                CloudOfferReport._toCsvNumValue(row.m2),
                CloudOfferReport._toCsvNumValue(row.bedrooms),
              ].join(", "),
            ),
      );
    CloudOfferReport._saveToCsv(
      csv.join("\n"),
      "basicreport_${DateTime.now().toIso8601String()}.csv",
    );
  }
}

class _CloudOffersBasicReportRow {
  String? ref;
  String? phone;
  String? name;
  String? url;
  String? provinceName;
  String? cityName;
  String? zoneName;
  bool saleAllowed = false;
  double? saleAmount;
  bool rentAllowed = false;
  double? rentAmount;
  double? m2;
  int? bedrooms;

  _CloudOffersBasicReportRow.fromOffer(OfferDto offer) {
    String? phone = offer.source.v.contact.v?.phone.vn;
    String? cityName = offer.property.vn?.address.vn?.city.vn?.label.vn?.localized;
    String? zoneName = offer.property.vn?.zone.vn?.name.vn;
    double? salePrice = offer.sale.vn?.amount.vn ?? 0;

    this.ref = "${offer.id.vn ?? ""}";
    this.phone = phone;
    this.name = '${offer.id.vn ?? ''}_${zoneName ?? ""}_${cityName ?? ""}';
    this.url = offer.source.vn?.pageUrl.vn;
    this.provinceName =
        offer.property.vn?.address.vn?.city.vn?.province.vn?.label.vn?.localized ?? "";
    this.cityName = cityName;
    this.zoneName = zoneName;
    this.saleAllowed = offer.sale.vn?.allowed.vn ?? false;
    this.saleAmount = salePrice;
    this.rentAllowed = offer.rent.vn?.allowed.vn ?? false;
    this.rentAmount = rentAmount;
    this.m2 = offer.property.vn?.attributes.vn?.totalSurfaceM2.vn;
    this.bedrooms = offer.property.vn?.attributes.vn?.totalBedroomsCount.vn;
  }
}
