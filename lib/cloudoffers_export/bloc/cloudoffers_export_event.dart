part of 'cloudoffers_export_bloc.dart';

int _id = 0;

abstract class CloudoffersExportEvent extends Equatable {
  const CloudoffersExportEvent();

  @override
  List<Object> get props => [];
}

///
/// Evento inicial
///
class CloudoffersExportOnLoadEvent extends CloudoffersExportEvent {
  final _myid = ++_id;

  final CloudoffersListFilter filter;
  final CloudoffersListOrderBy orderBy;

  CloudoffersExportOnLoadEvent({
    required this.filter,
    required this.orderBy,
  }) : super();

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}

///
/// Usuario indica que quiere empezar a procesar
///
class CloudoffersExportOnStartEvent extends CloudoffersExportEvent {
  final CloudoffersReportCode reportCode;
  final ReportFormatCode reportFormatCode;
  final int reportMaxRows;
  CloudoffersExportOnStartEvent({
    required this.reportCode,
    required this.reportFormatCode,
    required this.reportMaxRows,
  }) : super();
}

///
/// Se ha producido un error procesando otro evento
///
class CloudoffersExportOnFailureEvent extends CloudoffersExportEvent {
  final String message;
  CloudoffersExportOnFailureEvent({required this.message}) : super();
}
