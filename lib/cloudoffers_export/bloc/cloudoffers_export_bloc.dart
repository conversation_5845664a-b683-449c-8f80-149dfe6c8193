import 'dart:async';
import 'dart:math';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/cloudoffers_export/reports/cloudoffers_export_reports.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'cloudoffers_export_event.dart';
part 'cloudoffers_export_state.dart';

class CloudoffersExportBloc extends Bloc<CloudoffersExportEvent, CloudoffersExportState> {
  final AppModelsNS appModels;

  CloudoffersExportBloc({required this.appModels}) : super(CloudoffersExportInitState());

  factory CloudoffersExportBloc.create(BuildContext context) {
    return CloudoffersExportBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
    );
  }

  @override
  Stream<Transition<CloudoffersExportEvent, CloudoffersExportState>> transformEvents(
    Stream<CloudoffersExportEvent> events,
    TransitionFunction<CloudoffersExportEvent, CloudoffersExportState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<CloudoffersExportState> mapEventToState(CloudoffersExportEvent event) async* {
    if (event is CloudoffersExportOnLoadEvent) {
      yield* _mapOnLoadd(state, event);
    } else if (state is CloudoffersExportStepsState && event is CloudoffersExportOnStartEvent) {
      yield* _mapOnStart(state as CloudoffersExportStepsState, event);
    }
  }

  Stream<CloudoffersExportState> _mapOnLoadd(CloudoffersExportState state, CloudoffersExportOnLoadEvent event) async* {
    yield CloudoffersExportStepsState(
      filter: event.filter,
      orderBy: event.orderBy,
      step: CloudoffersExportStep.step01WaitingConfirmation,
      loadedRowsCount: 0,
    );
  }

  Stream<CloudoffersExportState> _mapOnStart(
    CloudoffersExportStepsState state,
    CloudoffersExportOnStartEvent event,
  ) async* {
    try {
      // Empezamos a obtener datos
      final waitingState = state.copyWith(step: CloudoffersExportStep.step02RetrievingData);
      yield waitingState;

      final report =
          event.reportCode == CloudoffersReportCode.basicinfo ? CloudOffersBasicReport() : CloudOffersPhonesReport();

      await for (var offersBlock in _listAllOffersInBlocks(state.filter, state.orderBy, maxRows: event.reportMaxRows)) {
        report.addOffers(offersBlock);
        yield waitingState.copyWith(loadedRowsCount: report.rowsCount);
      }

      // Empezamos a generar el fichero
      final generatingFileState = waitingState.copyWith(
        step: CloudoffersExportStep.step03GeneratingFile,
        loadedRowsCount: report.rowsCount,
      );
      yield generatingFileState;

      // Damos tiempo a que el diálogo pueda refrescar el nuevo estado, ya que el proceso de generar XML/CSV (siguiente paso) es bloqueante
      await Future.delayed(Duration(seconds: 1),(){});
      
       
      final savingFileState = generatingFileState.copyWith(step: CloudoffersExportStep.step04SavingFile);
      yield savingFileState;

      report.save(event.reportFormatCode);

      yield savingFileState.copyWith(step: CloudoffersExportStep.step05Done);
    } on Exception {
      yield CloudoffersExportFailureState.fromStepsState(stepsState: state, error: "Problemas generando exportación");
    }
  }

  Stream<List<OfferDto>> _listAllOffersInBlocks(
    CloudoffersListFilter filter,
    CloudoffersListOrderBy orderBy, {
    int blockSize = 250,
    int maxRows = 5000,
  }) async* {
    var offset = 0;
    var limit = min(blockSize, maxRows - offset);

    while (limit > 0) {
      var offers = await appModels.listCloudOffers(
        filter: filter,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
      if (offers.length != 0) yield offers;
      if (offers.length < blockSize) {
        break;
      } else {
        offset += offers.length;
        limit = min(blockSize, maxRows - offset);
      }
    }
  }
}
