part of 'cloudoffers_export_bloc.dart';

enum CloudoffersExportStep {
  step01WaitingConfirmation,
  step02RetrievingData,
  step03GeneratingFile,
  step04SavingFile,
  step05Done,
}


abstract class CloudoffersExportState extends Equatable {
  final CloudoffersListFilter filter;
  final CloudoffersListOrderBy orderBy;
  final CloudoffersReportCode reportCode;

  CloudoffersExportState(
      {this.filter = const CloudoffersListFilter(),
      this.orderBy = CloudoffersListOrderBy.created_at_desc,
      this.reportCode = CloudoffersReportCode.basicinfo})
      : super();
  @override
  List<Object> get props => [];
}

class CloudoffersExportInitState extends CloudoffersExportState {}

class CloudoffersExportStepsState extends CloudoffersExportState {
  static int _id = 0;

  final CloudoffersExportStep step;
  // All offers must be loaded before file generation (02, retrieving Data)
  final int loadedRowsCount;

  final _myid = ++_id;
  @override
  List<Object> get props => [_myid];

  CloudoffersExportStepsState({
    required CloudoffersListFilter filter,
    required CloudoffersListOrderBy orderBy,
    required this.step,
    this.loadedRowsCount = 0,
  }) : super(filter: filter, orderBy: orderBy);

  /// Clonar objeto por completo
  factory CloudoffersExportStepsState.copyFrom({
    required CloudoffersExportStepsState stepsState,
  }) =>
      CloudoffersExportStepsState(
        filter: stepsState.filter,
        orderBy: stepsState.orderBy,
        step: stepsState.step,
        loadedRowsCount: stepsState.loadedRowsCount,
      );

  /// Copiar a un nuevo objeto cambiando algunos valores
  CloudoffersExportStepsState copyWith({
    CloudoffersListFilter? filter,
    CloudoffersListOrderBy? orderBy,
    CloudoffersExportStep? step,
    int? loadedRowsCount,
  }) =>
      CloudoffersExportStepsState(
        filter: filter ?? this.filter,
        orderBy: orderBy ?? this.orderBy,
        step: step ?? this.step,
        loadedRowsCount: loadedRowsCount ?? this.loadedRowsCount,
      );
}

class CloudoffersExportFailureState extends CloudoffersExportStepsState {
  final String error;
  CloudoffersExportFailureState({
    required this.error,
    required CloudoffersExportStep step,
    required CloudoffersReportCode reportCode,
    required CloudoffersListFilter filter,
    required CloudoffersListOrderBy orderBy,
    required loadedRowsCount,
  }) : super(step: step, filter: filter, orderBy: orderBy, loadedRowsCount: loadedRowsCount);

  factory CloudoffersExportFailureState.fromStepsState({
    required CloudoffersExportStepsState stepsState,
    required String error,
  }) {
    return CloudoffersExportFailureState(
      error: error,
      filter: stepsState.filter,
      orderBy: stepsState.orderBy,
      reportCode: stepsState.reportCode,
      step: stepsState.step,
      loadedRowsCount: stepsState.loadedRowsCount,
    );
  }

  @override
  List<Object> get props => [error, step, filter];
}
