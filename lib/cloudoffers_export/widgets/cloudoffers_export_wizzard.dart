import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:form_builder/form_builder.dart';
import 'package:intl/intl.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/cloudoffers_export/offers_filter.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/cloudoffers_export/reports/cloudoffers_export_reports.dart';
import 'package:topbrokers/global/session_bloc.dart';

class CloudoffersExportWizzard extends StatefulWidget {
  final CloudoffersListFilter actualFilter;
  final CloudoffersListOrderBy actualOrderBy;

  final void Function() onEnded;

  CloudoffersExportWizzard({
    Key? key,
    required this.actualFilter,
    required this.actualOrderBy,
    required this.onEnded,
  }) : super(key: key ?? AgentorKeys.offersExportPage);

  @override
  _CloudoffersExportWizzardState createState() => _CloudoffersExportWizzardState();
}

class _CloudoffersExportWizzardState extends State<CloudoffersExportWizzard> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  // Form data
  CloudoffersReportCode reportCode = CloudoffersReportCode.basicinfo;
  ReportFormatCode reportFormatCode = ReportFormatCode.csv;
  //
  _CloudoffersExportWizzardState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));
    late CloudoffersExportBloc _formBloc;
    final textTheme = Theme.of(context).textTheme;
    return BlocProvider<CloudoffersExportBloc>(
      create: (context) {
        _formBloc = CloudoffersExportBloc.create(context)
          ..add(
            CloudoffersExportOnLoadEvent(
              filter: widget.actualFilter,
              orderBy: widget.actualOrderBy,
            ),
          );
        return _formBloc;
      },
      child: BlocConsumer<CloudoffersExportBloc, CloudoffersExportState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          if (state is CloudoffersExportFailureState) {
            return _unexpectedError(context, state, _formBloc);
          } else if (state is CloudoffersExportStepsState) {
            switch (state.step) {
              case CloudoffersExportStep.step01WaitingConfirmation:
                return _Step01Form(
                  onCancel: () => widget.onEnded(),
                  onContinue: (({required reportCode, required reportFormatCode, required reportMaxRows}) {
                    _formBloc.add(
                      CloudoffersExportOnStartEvent(
                        reportCode: reportCode,
                        reportFormatCode: reportFormatCode,
                        reportMaxRows: reportMaxRows,
                      ),
                    );
                  }),
                );

              case CloudoffersExportStep.step02RetrievingData:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(localizations.cloudoffers_export_retrievingRows, style: textTheme.headline5),
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                      Text(localizations.cloudoffers_export_retrievedCount(state.loadedRowsCount))
                    ],
                  ),
                );
              case CloudoffersExportStep.step03GeneratingFile:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                      Text(localizations.cloudoffers_export_generatingFile(state.loadedRowsCount)),
                    ],
                  ),
                );
              case CloudoffersExportStep.step05Done:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: Center(
                            child: Text(localizations.cloudoffers_export_done_titleLabel, style: textTheme.headline5)),
                      ),
                      Divider(height: 20.0, color: Colors.transparent),
                      Center(child: Text(localizations.cloudoffers_export_done_totalrowsLabel(state.loadedRowsCount))),
                      Divider(height: 40.0, color: Colors.transparent),
                      TextButton.icon(
                        icon: Icon(Icons.check),
                        label: Text(localizations.common_Close),
                        onPressed: () {
                          widget.onEnded();
                        },
                      ),
                    ],
                  ),
                );
              default:
                throw Exception("Unexpected wizzard state");
            }
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _unexpectedError(BuildContext context, CloudoffersExportFailureState state, CloudoffersExportBloc _formBloc) {
    final apploc = context.apploc;
    final textTheme = Theme.of(context).textTheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(apploc.common_err_unexpected, style: textTheme.headline5),
          Divider(height: 40.0, color: Colors.transparent),
          Padding(
            padding: EdgeInsets.all(5),
            child: ElevatedButton.icon(
              icon: Icon(Icons.check),
              label: Text(apploc.common_Close),
              onPressed: () {
                widget.onEnded();
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.all(5),
            child: ElevatedButton.icon(
              icon: Icon(Icons.refresh),
              label: Text(apploc.common_Retry),
              onPressed: () {
                _formBloc.add(CloudoffersExportOnLoadEvent(filter: state.filter, orderBy: state.orderBy));
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _Step01Form extends StatefulWidget {
  final void Function() onCancel;
  final void Function({
    required CloudoffersReportCode reportCode,
    required ReportFormatCode reportFormatCode,
    required int reportMaxRows,
  }) onContinue;

  const _Step01Form({Key? key, required this.onCancel, required this.onContinue}) : super(key: key);

  @override
  _Step01FormState createState() {
    return _Step01FormState();
  }
}

class _Step01FormState extends State<_Step01Form> {
  final _formKey = GlobalKey<FormState>();

  CloudoffersReportCode reportCode = CloudoffersReportCode.basicinfo;
  ReportFormatCode reportFormatCode = ReportFormatCode.xlms;
  int reportMaxRows = 500;

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    // Build a Form widget using the _formKey created above.
    return BlocConsumer<SessionBloc, SessionState>(
      listener: (context, state) {},
      builder: (context, globalState) {
        switch (globalState.status) {
          case SessionStatus.authentified:
            final isPremiumAgent = globalState.agent.vn?.isPremium.vn ?? false;
            return Form(
              key: _formKey,
              child: Column(
                children: [
                  GroupEntry(
                    //label: "Datos del anuncio",
                    isSubgroup: true,
                    children: [
                      SelectFieldEntry<CloudoffersReportCode>(
                        label: localization.cloudoffers_export_reportLabel,
                        getValue: () => this.reportCode,
                        setValue: (code) => setState(() {
                          this.reportCode = code == null ? CloudoffersReportCode.basicinfo : code;
                        }),
                        isRequired: true,
                        options: [
                          SelectOption(
                            label: CloudOffersBasicReport.name.localized,
                            value: CloudOffersBasicReport.code,
                          ),
                          SelectOption(
                            label: CloudOffersPhonesReport.name.localized,
                            value: CloudOffersPhonesReport.code,
                          ),
                        ],
                      ),
                      SelectFieldEntry<ReportFormatCode>(
                        label: localization.cloudoffers_export_formatLabel,
                        getValue: () => this.reportFormatCode,
                        setValue: (code) => setState(() {
                          this.reportFormatCode = (code == null) ? ReportFormatCode.xlms : code;
                        }),
                        isRequired: true,
                        options: ReportFormats.map(
                          (rf) => SelectOption(
                            label: rf.name.localized,
                            value: rf.code,
                          ),
                        ).toList(),
                      ),
                      SelectFieldEntry<int>(
                        label: localization.cloudoffers_export_maxrowsLabel,
                        getValue: () => this.reportMaxRows,
                        setValue: (code) {
                          setState(() {
                            this.reportMaxRows = code == null ? 500 : code;
                          });
                        },
                        isRequired: true,
                        options: (isPremiumAgent ? [100, 500, 1000, 2500, 5000, 10000] : [100, 500, 1000, 2500, 5000])
                            .map((n) => SelectOption(label: "$n", value: n))
                            .toList(),
                      ),
                    ],
                  ),
                  Divider(height: 40.0, color: Colors.transparent),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(0),
                        child: TextButton.icon(
                          icon: Icon(Icons.check),
                          label: Text(localization.cloudoffers_export_exportBtn),
                          onPressed: () {
                            widget.onContinue(
                              reportCode: this.reportCode,
                              reportFormatCode: this.reportFormatCode,
                              reportMaxRows: this.reportMaxRows,
                            );
                          },
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(0),
                        child: TextButton.icon(
                          icon: Icon(Icons.close),
                          label: Text(localization.common_Cancel),
                          onPressed: () {
                            widget.onCancel();
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          default:
            // No deberíaentrar nuca aquí
            return Icon(Icons.warning);
        }
      },
    );
  }
}
