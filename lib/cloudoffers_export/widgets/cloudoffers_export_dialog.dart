import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/cloudoffers_export/offers_filter.dart';
import 'package:topbrokers/cloudoffers_export/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

Future<void> exportCloudoffersDialog(
  context, {
  CloudoffersListFilter filter = const CloudoffersListFilter(),
  CloudoffersListOrderBy orderBy = CloudoffersListOrderBy.created_at_desc,
}) {
  return showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    pageBuilder: (
      BuildContext context,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      return ChangeNotifierProvider<_ChangeOffersFilterModel>(
        create: (_) => _ChangeOffersFilterModel(),
        child: Consumer<_ChangeOffersFilterModel>(builder: (context, filterModel, child) {
          return AlertDialog(
            insetPadding: EdgeInsets.all(5.0),
            actionsAlignment: MainAxisAlignment.spaceEvenly,
            contentPadding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 5),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                CloudoffersExportWizzard(
                  actualFilter: filter,
                  actualOrderBy: orderBy,
                  onEnded: () {
                    Navigator.pop(context, null);
                  },
                ),
              ],
            ),
            actions: [],
          );
        }),
      );
    },
  );
}

class _ChangeOffersFilterModel extends ChangeNotifier {
  late OffersListFilter filter;

  _ChangeOffersFilterModel() : super();

  void changeFilter(OffersListFilter newFilter) {
    filter = newFilter;
    notifyListeners();
  }
}
