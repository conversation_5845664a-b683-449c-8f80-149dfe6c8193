import 'package:agentor_repositoryns/services/services.dart';
import 'package:topbrokers/agentor_app.dart';
import 'package:agentor_deps/agentor_deps.dart';
import 'package:topbrokers/app_config.dart';
import 'package:topbrokers/app_constants.dart';
import 'simple_bloc_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

const C_DEVEL = false;
void main() async {
  Deps().register(AppConstants(isDevelopment: C_DEVEL, title: "percent", zonesEnabled: true));
  if (C_DEVEL) {
    Deps().register(ApiServices(baseUrl: 'http://localhost:8000/api/'));
    Deps().register(AppConfig(webserverUrl: "http://localhost:8000/"));
    Bloc.observer = SimpleBlocObserver();
  } else {
    Deps().register(ApiServices(baseUrl: '/api/'));
    Deps().register(AppConfig(webserverUrl: "https://www.topbrokers.io/"));
  }
  runApp(TopbrokersApp());
}
