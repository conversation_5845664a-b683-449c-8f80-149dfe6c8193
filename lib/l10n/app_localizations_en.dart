// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get app_title => 'Percent';

  @override
  String get app_err_loading => 'App can\'t be initialized';

  @override
  String get common_and => 'and';

  @override
  String get common_add => 'add';

  @override
  String get common_edit => 'edit';

  @override
  String get common_accept => 'accept';

  @override
  String get common_Accept => 'Aceptar';

  @override
  String get common_acceptChanges => 'accept changes';

  @override
  String get common_create => 'create';

  @override
  String get common_continue => 'continue';

  @override
  String get common_Continue => 'Continue';

  @override
  String get common_cancel => 'cancel';

  @override
  String get common_Cancel => 'Cancel';

  @override
  String get common_close => 'close';

  @override
  String get common_Close => 'Close';

  @override
  String get common_retry => 'try again';

  @override
  String get common_Retry => 'Try again';

  @override
  String get common_errLoading => 'Problems loading data';

  @override
  String get common_formWithErrors => 'The form contains errors. Check it out.';

  @override
  String get common_forSale => 'for sale';

  @override
  String get common_forRent => 'for rent';

  @override
  String get common_sale => 'sale';

  @override
  String get common_rent => 'rent';

  @override
  String get common_marketPrice => 'market price';

  @override
  String get common_try_again => 'Try again';

  @override
  String get common_see_more => 'See more';

  @override
  String get common_selectAContact => 'Select a contact';

  @override
  String get common_selectAnOffer => 'Select an offer';

  @override
  String get common_AnyLabel => 'Any';

  @override
  String get common_NotMatterLabel => 'Not matter';

  @override
  String get common_YesLabel => 'Yes';

  @override
  String get common_NotLabel => 'Not';

  @override
  String get common_HouseLabel => 'House';

  @override
  String get common_FlatLabel => 'Flat';

  @override
  String get common_SaleLabel => 'Sale';

  @override
  String get common_RentLabel => 'Rent';

  @override
  String get common_detailsLabel => 'details';

  @override
  String get common_msg_urlCopiedToClipboard => 'Url copied to clipboard';

  @override
  String get common_err_unexpected => 'Unexpected problem';

  @override
  String get common_err_unexpected_Tryagain =>
      'Unexpected problem. Try again later';

  @override
  String common_err_unexpected_Msg(String message) {
    return 'Unexpected problem: $message';
  }

  @override
  String get landing_logIn => 'Sign in';

  @override
  String get landing_signUp => 'Sign up';

  @override
  String get landing_legalNotice => 'Legal Notice';

  @override
  String get login_emailLabel => 'eMail';

  @override
  String get login_passwordLabel => 'Password';

  @override
  String get login_signinBtn => 'log in';

  @override
  String get login_forgotPasswordLabel => 'Forgot your password?';

  @override
  String get login_recoverPasswordBtn => 'Recover it!';

  @override
  String get login_noAccountLabel => 'Don\'t have a Topbrokers account?';

  @override
  String get invalid_email => 'Not a correct email';

  @override
  String get signup_invalid_domain => 'Only agenteunico.net emails allowed';

  @override
  String get login_signUpBtn => 'Sign up now!';

  @override
  String get signup_title => 'Sign up';

  @override
  String get signup_onetimecodeLabel => 'Enter the token to access registry';

  @override
  String get signup_onetimecodeHelper =>
      'The token given by your organisation.';

  @override
  String get signup_onetimecodeError => 'That is not the correct code';

  @override
  String get signup_validationcodeLabel =>
      'Enter the code we have sent by eMail';

  @override
  String get signup_validationcodeHelper =>
      'If you haven\'t received anything, please check your spam folder.';

  @override
  String get signup_resendValidationCodeBtn => 'Reenviar código de validación';

  @override
  String get signup_fieldsBlocTitle => 'Signup data';

  @override
  String get signup_passwordLabel => 'Password';

  @override
  String get signup_password2Label => 'Password (repeat)';

  @override
  String get signup_password2Error => 'Must match \'Password\'';

  @override
  String signup_acceptEulaTitle(String eulaUrl, String privacyPolicyUrl) {
    return 'I have read and accept the <a href=\'$eulaUrl\'>conditions of use</a> and the <a href=\'$privacyPolicyUrl\'>privacy policy</a>';
  }

  @override
  String signup_welcomeTitle(String name) {
    return 'Hi $name!';
  }

  @override
  String get signup_welcomeText => 'welcome to topbrokers.io';

  @override
  String get signup_loginBtn => 'log in';

  @override
  String get signup_err_emailalredyexists => 'The email entered already exists';

  @override
  String get signup_err_validationcodeinvalid =>
      'Validation code doens\'t exist or has expired';

  @override
  String get signup_err_onetimecodeinvalid =>
      'Onetime code doesn\'t exist or has expired';

  @override
  String get home_tabs_search => 'search';

  @override
  String get home_tabs_favorites => 'favorites';

  @override
  String get home_tabs_alerts => 'alerts';

  @override
  String get home_tabs_offers => 'offers';

  @override
  String get home_tabs_contacts => 'contacts';

  @override
  String get home_tabs_actions => 'actions';

  @override
  String get offer_errLoading => 'Problem loading data';

  @override
  String get offer_errDoesnExist => 'The offer doesn\'t exist';

  @override
  String get offer_favouriteLabel => 'Favorite';

  @override
  String get offer_srcadHeading => 'Original Ad';

  @override
  String get offer_srcad_updatedAtLabel => 'Last update';

  @override
  String get offer_srcad_advertiserLabel => 'Advertiser';

  @override
  String get offer_srcad_individualLabel => 'Individual';

  @override
  String get offer_srcad_nameLabel => 'Name';

  @override
  String get offer_srcad_webpageLabel => 'Web page';

  @override
  String get offer_srcad_referenceLabel => 'Ref.';

  @override
  String get offer_mainHeading => 'Main data';

  @override
  String get offer_agentHeading => 'Agent';

  @override
  String get offer_ownerHeading => 'Owner';

  @override
  String get offer_picturesHeading => 'Pictures';

  @override
  String get offer_addressHeading => 'Address';

  @override
  String get offer_featuresHeading => 'Features';

  @override
  String get offer_descriptionHeading => 'Description';

  @override
  String get offer_internalsHeading => 'Internals';

  @override
  String get offer_actionsHeading => 'Actions';

  @override
  String get offer_matchingsHeading => 'Matchings (alerts)';

  @override
  String get offer_publicationsHeading => 'Publish in portals';

  @override
  String get offer_cantPublishDisclaimer =>
      'The offer needs to be in <b>commercialization</b> state before you can publish it on a real estate portal.<br/>Please, edit the offer (pencil button) and change it\'\'s state before';

  @override
  String get offer_sharingsHeading => 'Share with';

  @override
  String get offer_createBtn => 'Create offer';

  @override
  String get offer_saveBtn => 'Save changes';

  @override
  String get offer_titleNew => 'New offer';

  @override
  String get offer_title => 'Offer';

  @override
  String get offer_stateDialog_title => 'Status change';

  @override
  String get offer_stateDialog_bodyHTML =>
      '<p>Only offers in commercialization can be published on real estate portals or shared in working groups. <br/> If you continue, the offer will be withdrawn from all portals and working groups in which it is published.</p><p>Do you want to continue?</p>';

  @override
  String get offer_stateDialog_yes => 'Yes continue';

  @override
  String get offer_stateDialog_not => 'No, cancel';

  @override
  String get offers_errLoading => 'Problems loading offers';

  @override
  String get offers_noOffers => 'No offers';

  @override
  String get offers_addFirstOffer => 'Add offer';

  @override
  String offers_item_surfaceM2(int total) {
    return '${total}m²';
  }

  @override
  String offers_item_bethroomsCount(int total) {
    return '$total bedrooms';
  }

  @override
  String get offers_item_exteriorTxt => 'exterior';

  @override
  String get offersfilter_errLoading => 'Problems loading data';

  @override
  String get offersfilter_sourceContactPhoneLabel => 'Contact phone';

  @override
  String get offersfilter_isVersionLabel => 'Versions?';

  @override
  String get offersfilter_isVersion_yes => 'Versions only';

  @override
  String get offersfilter_isVersion_not => 'Main offers only';

  @override
  String get offersfilter_customerTypeLabel => 'Owner type';

  @override
  String get offersfilter_customerLabel => 'Owner';

  @override
  String get offersfilter_zoneLabel => 'Zone';

  @override
  String get offersfilter_cityLabel => 'City';

  @override
  String get offersfilter_cityEmptyText => 'Select a city';

  @override
  String get offersfilter_typeLabel => 'Type';

  @override
  String get offersfilter_operationLabel => 'Operation';

  @override
  String get offersfilter_m2Label => 'Surface';

  @override
  String get offersfilter_minM2Label => 'Min surface';

  @override
  String get offersfilter_maxM2Label => 'Max surface';

  @override
  String get offersfilter_saleAmountLabel => 'Price';

  @override
  String get offersfilter_rentAmountLabel => 'Rent';

  @override
  String get offersfilter_saleMinAmountLabel => 'Min price';

  @override
  String get offersfilter_saleMaxAmountLabel => 'Max price';

  @override
  String get offersfilter_rentMinAmountLabel => 'Min rent';

  @override
  String get offersfilter_rentMaxAmountLabel => 'Max rent';

  @override
  String get offersfilter_showNotMine => 'Show other agents opportunities';

  @override
  String get cloudoffers_filter_favoritesOnlyLabel => 'Favorites only';

  @override
  String get cloudoffers_filter_includeIndividualLabel => 'Individuals';

  @override
  String get cloudoffers_filter_includeProfessionalLabel => 'Professionals';

  @override
  String get cloudoffers_filter_sourceContactPhoneLabel => 'Contact phone #';

  @override
  String get cloudoffers_filter_publicationDateLabel => 'Publication date';

  @override
  String get cloudoffers_filter_publicationDate_manually => 'Enter manually';

  @override
  String get cloudoffers_filter_publicationDate_last48h => 'Last 48h';

  @override
  String get cloudoffers_filter_publicationDate_thisWeek => 'This week';

  @override
  String get cloudoffers_filter_publicationDate_lastWeek => 'Last week';

  @override
  String get cloudoffers_filter_publicationDate_thisMonth => 'This month';

  @override
  String get cloudoffers_filter_publicationDate_lastMonth => 'Last month';

  @override
  String get cloudoffers_filter_publicationDate_hint => 'Any';

  @override
  String get cloudoffers_filter_publicationDate_from => 'From';

  @override
  String get cloudoffers_filter_publicationDate_to => 'To';

  @override
  String get cloudoffers_import_addNotExistsLabel => 'The ad no longer exists';

  @override
  String get cloudoffers_import_waitingForLastVersionLabel =>
      'Getting updated version of ad';

  @override
  String get cloudoffers_import_newPropertyOwnerLabel =>
      'Owner of the new property';

  @override
  String get cloudoffers_import_createOfferLabel => 'Create offer';

  @override
  String get cloudoffers_import_creatingOfferLabel => 'Creating offer';

  @override
  String get cloudoffers_import_doneLabel => 'Offer created successfully';

  @override
  String get cloudoffers_import_seeDetailsLabel => 'See details';

  @override
  String get cloudoffers_import_err_importing => 'Problems while import';

  @override
  String get cloudoffers_refresh_doneLabel => 'Ad refreshed successfully';

  @override
  String get cloudoffers_refresh_errLabel => 'Problems while refreshing';

  @override
  String get cloudoffers_export_reportLabel => 'What to export';

  @override
  String get cloudoffers_export_formatLabel => 'In which format';

  @override
  String get cloudoffers_export_maxrowsLabel => 'How many rows (maximum)';

  @override
  String get cloudoffers_export_exportBtn => 'Export';

  @override
  String get cloudoffers_export_retrievingRows => 'Retrieving rows';

  @override
  String cloudoffers_export_retrievedCount(int count) {
    return 'Total: $count';
  }

  @override
  String cloudoffers_export_generatingFile(int count) {
    return 'Generating file for $count rows';
  }

  @override
  String get cloudoffers_export_done_titleLabel => 'All done!!';

  @override
  String cloudoffers_export_done_totalrowsLabel(int count) {
    return 'Exported: $count rows';
  }

  @override
  String get contactsfilter_errLoading => 'Problems loading data';

  @override
  String get contactsfilter_searchLabel => 'Search text';

  @override
  String get contactsfilter_isOfferCustomerLabel => 'Is offertant?';

  @override
  String get contactsfilter_isBankServicerLabel => 'Is Bank Servicer?';

  @override
  String get contactsfilter_hasSiteLabel => 'Has site (tinder.topbroekes.io)?';

  @override
  String get contact_titleNew => 'New contact';

  @override
  String get contact_title => 'Contact';

  @override
  String get contact_errLoading => 'Problems loading contact information';

  @override
  String get contact_errDoesnExist => 'The contact doesn\'t exist';

  @override
  String get contact_createBtn => 'Create contact';

  @override
  String get contact_saveBtn => 'Save changes';

  @override
  String get contact_nameLabel => 'Name';

  @override
  String get contact_lastnameLabel => 'Surnames';

  @override
  String get contact_emailLabel => 'eMail';

  @override
  String get contact_mobileLabel => 'Phone number (mobile)';

  @override
  String get contact_siteLabel => 'Contact site (tinder.topbrokers.io)';

  @override
  String get contact_notesLabel => 'Notes';

  @override
  String get contact_offersLabel => 'Ofertas';

  @override
  String get contact_addOfferBtn => 'nueva';

  @override
  String get contact_demandsLabel => 'Alerts';

  @override
  String get contact_addDemandBtn => 'nueva';

  @override
  String get contact_actionsLabel => 'Acciones';

  @override
  String get contact_addActionBtn => 'nueva';

  @override
  String get agent_titleNew => 'New agent';

  @override
  String get agent_title => 'Agent';

  @override
  String get agent_errLoading => 'Problems loading agent information';

  @override
  String get agent_errDoesnExist => 'The agent doesn\'t exist';

  @override
  String get agent_saveBtn => 'Save changes';

  @override
  String get agent_nameLabel => 'Name';

  @override
  String get agent_lastnameLabel => 'Surnames';

  @override
  String get agent_emailLabel => 'eMail';

  @override
  String get agent_mobileLabel => 'Phone number (mobile)';

  @override
  String get recoverpass_title => 'Recover password';

  @override
  String get recoverpass_validationcodeHelper =>
      'Enter the code that we have sent you by eMail. If you have not received anything, it is possible that the indicated address is not associated with any user or, may be, is in your spam folder. .';

  @override
  String get recoverpass_finalMessageText => 'Password changed';

  @override
  String get demand_titleNew => 'New alert';

  @override
  String get demands_errLoading => 'Problems loading alerts';

  @override
  String get demands_noDemands => 'No alerts';

  @override
  String get demands_addFirstDemand => 'Create an alert';

  @override
  String get demands_problems => 'Problems getting alerts';

  @override
  String get demands_notFound => 'No alerts found';

  @override
  String get demands_register => 'Register an alert';

  @override
  String get opportunities_errLoading => 'Problems loading opportunities';

  @override
  String get opportunities_noOpportunities => 'No oportunities found';

  @override
  String get opportunities_noOffers => 'No offers';

  @override
  String get opportunities_noDemands => 'No alerts';

  @override
  String get opportunities_noOffersOrDemands => 'No offers or alerts';

  @override
  String get opportunities_addFirstOffer => 'Create an Offer';

  @override
  String get opportunities_addFirstDemand => 'Create an Alert';

  @override
  String get matchings_errLoading => 'Problems loading matchings from server';

  @override
  String get matchings_noMatchings => 'No matchings';

  @override
  String get action_titleNew => 'New action';

  @override
  String get action_title => 'Action';

  @override
  String get action_mainHeading => 'Main data';

  @override
  String get action_typeLabel => 'Type';

  @override
  String get action_whenLabel => 'When';

  @override
  String get action_doneLabel => 'Done';

  @override
  String get action_contactLabel => 'With whom';

  @override
  String get action_offerLabel => 'Offer';

  @override
  String get action_notesLabel => 'Notes';

  @override
  String get actions_errLoading => 'Problems loading actions from server';

  @override
  String get actions_noActions => 'No actions';

  @override
  String get actions_noActionsYet => 'No actions found';

  @override
  String get actions_addFirstAction => 'Register a new action';

  @override
  String get actions_filterTooltip => 'Filter';

  @override
  String get actions_filter_todayLabel => 'Today';

  @override
  String get actions_filter_fromTodayLabel => 'From today';

  @override
  String get actions_filter_untilTodayLabel => 'Until today';

  @override
  String get actions_filter_doneLabel => 'Done';

  @override
  String get actions_filter_undoneLabel => 'Undone';

  @override
  String get actions_filter_doneAndUndoneLabel => 'Done and undone';

  @override
  String get serviceaction_titleNew => 'Superpower';

  @override
  String get serviceaction_title => 'Superpower';

  @override
  String get serviceaction_typeLabel => 'Type';

  @override
  String get serviceaction_whenLabel => 'When';

  @override
  String get serviceaction_doneLabel => 'Done';

  @override
  String get serviceaction_contactLabel => 'Contact';

  @override
  String get serviceaction_offerLabel => 'Offer';

  @override
  String get serviceaction_notesLabel => 'Notes';

  @override
  String get offer_cloneDialog_title => 'Create an offer version';

  @override
  String offer_cloneDialog_message(int offer_id) {
    return 'Do yuou want to create a version of the offer $offer_id ?';
  }

  @override
  String get offer_cloneDialog_yes => 'Yes, continue';

  @override
  String get offer_cloneDialog_not => 'No, cancel';

  @override
  String get offer_cloneDialog_type_message => 'Select version type';

  @override
  String get offer_cloneDialog_menu_option => 'Create version';

  @override
  String get offer_versionHeading => 'Version data';

  @override
  String get common_versionOf => 'Source offer';
}
