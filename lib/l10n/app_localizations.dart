import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_es.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es')
  ];

  /// No description provided for @app_title.
  ///
  /// In en, this message translates to:
  /// **'Percent'**
  String get app_title;

  /// No description provided for @app_err_loading.
  ///
  /// In en, this message translates to:
  /// **'App can\'t be initialized'**
  String get app_err_loading;

  /// No description provided for @common_and.
  ///
  /// In en, this message translates to:
  /// **'and'**
  String get common_and;

  /// No description provided for @common_add.
  ///
  /// In en, this message translates to:
  /// **'add'**
  String get common_add;

  /// No description provided for @common_edit.
  ///
  /// In en, this message translates to:
  /// **'edit'**
  String get common_edit;

  /// No description provided for @common_accept.
  ///
  /// In en, this message translates to:
  /// **'accept'**
  String get common_accept;

  /// No description provided for @common_Accept.
  ///
  /// In en, this message translates to:
  /// **'Aceptar'**
  String get common_Accept;

  /// No description provided for @common_acceptChanges.
  ///
  /// In en, this message translates to:
  /// **'accept changes'**
  String get common_acceptChanges;

  /// No description provided for @common_create.
  ///
  /// In en, this message translates to:
  /// **'create'**
  String get common_create;

  /// No description provided for @common_continue.
  ///
  /// In en, this message translates to:
  /// **'continue'**
  String get common_continue;

  /// No description provided for @common_Continue.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get common_Continue;

  /// No description provided for @common_cancel.
  ///
  /// In en, this message translates to:
  /// **'cancel'**
  String get common_cancel;

  /// No description provided for @common_Cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get common_Cancel;

  /// No description provided for @common_close.
  ///
  /// In en, this message translates to:
  /// **'close'**
  String get common_close;

  /// No description provided for @common_Close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get common_Close;

  /// No description provided for @common_retry.
  ///
  /// In en, this message translates to:
  /// **'try again'**
  String get common_retry;

  /// No description provided for @common_Retry.
  ///
  /// In en, this message translates to:
  /// **'Try again'**
  String get common_Retry;

  /// No description provided for @common_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading data'**
  String get common_errLoading;

  /// No description provided for @common_formWithErrors.
  ///
  /// In en, this message translates to:
  /// **'The form contains errors. Check it out.'**
  String get common_formWithErrors;

  /// No description provided for @common_forSale.
  ///
  /// In en, this message translates to:
  /// **'for sale'**
  String get common_forSale;

  /// No description provided for @common_forRent.
  ///
  /// In en, this message translates to:
  /// **'for rent'**
  String get common_forRent;

  /// No description provided for @common_sale.
  ///
  /// In en, this message translates to:
  /// **'sale'**
  String get common_sale;

  /// No description provided for @common_rent.
  ///
  /// In en, this message translates to:
  /// **'rent'**
  String get common_rent;

  /// No description provided for @common_marketPrice.
  ///
  /// In en, this message translates to:
  /// **'market price'**
  String get common_marketPrice;

  /// No description provided for @common_try_again.
  ///
  /// In en, this message translates to:
  /// **'Try again'**
  String get common_try_again;

  /// No description provided for @common_see_more.
  ///
  /// In en, this message translates to:
  /// **'See more'**
  String get common_see_more;

  /// No description provided for @common_selectAContact.
  ///
  /// In en, this message translates to:
  /// **'Select a contact'**
  String get common_selectAContact;

  /// No description provided for @common_selectAnOffer.
  ///
  /// In en, this message translates to:
  /// **'Select an offer'**
  String get common_selectAnOffer;

  /// No description provided for @common_AnyLabel.
  ///
  /// In en, this message translates to:
  /// **'Any'**
  String get common_AnyLabel;

  /// No description provided for @common_NotMatterLabel.
  ///
  /// In en, this message translates to:
  /// **'Not matter'**
  String get common_NotMatterLabel;

  /// No description provided for @common_YesLabel.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get common_YesLabel;

  /// No description provided for @common_NotLabel.
  ///
  /// In en, this message translates to:
  /// **'Not'**
  String get common_NotLabel;

  /// No description provided for @common_HouseLabel.
  ///
  /// In en, this message translates to:
  /// **'House'**
  String get common_HouseLabel;

  /// No description provided for @common_FlatLabel.
  ///
  /// In en, this message translates to:
  /// **'Flat'**
  String get common_FlatLabel;

  /// No description provided for @common_SaleLabel.
  ///
  /// In en, this message translates to:
  /// **'Sale'**
  String get common_SaleLabel;

  /// No description provided for @common_RentLabel.
  ///
  /// In en, this message translates to:
  /// **'Rent'**
  String get common_RentLabel;

  /// No description provided for @common_detailsLabel.
  ///
  /// In en, this message translates to:
  /// **'details'**
  String get common_detailsLabel;

  /// No description provided for @common_msg_urlCopiedToClipboard.
  ///
  /// In en, this message translates to:
  /// **'Url copied to clipboard'**
  String get common_msg_urlCopiedToClipboard;

  /// No description provided for @common_err_unexpected.
  ///
  /// In en, this message translates to:
  /// **'Unexpected problem'**
  String get common_err_unexpected;

  /// No description provided for @common_err_unexpected_Tryagain.
  ///
  /// In en, this message translates to:
  /// **'Unexpected problem. Try again later'**
  String get common_err_unexpected_Tryagain;

  /// No description provided for @common_err_unexpected_Msg.
  ///
  /// In en, this message translates to:
  /// **'Unexpected problem: {message}'**
  String common_err_unexpected_Msg(String message);

  /// No description provided for @landing_logIn.
  ///
  /// In en, this message translates to:
  /// **'Sign in'**
  String get landing_logIn;

  /// No description provided for @landing_signUp.
  ///
  /// In en, this message translates to:
  /// **'Sign up'**
  String get landing_signUp;

  /// No description provided for @landing_legalNotice.
  ///
  /// In en, this message translates to:
  /// **'Legal Notice'**
  String get landing_legalNotice;

  /// No description provided for @login_emailLabel.
  ///
  /// In en, this message translates to:
  /// **'eMail'**
  String get login_emailLabel;

  /// No description provided for @login_passwordLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get login_passwordLabel;

  /// No description provided for @login_signinBtn.
  ///
  /// In en, this message translates to:
  /// **'log in'**
  String get login_signinBtn;

  /// No description provided for @login_forgotPasswordLabel.
  ///
  /// In en, this message translates to:
  /// **'Forgot your password?'**
  String get login_forgotPasswordLabel;

  /// No description provided for @login_recoverPasswordBtn.
  ///
  /// In en, this message translates to:
  /// **'Recover it!'**
  String get login_recoverPasswordBtn;

  /// No description provided for @login_noAccountLabel.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have a Topbrokers account?'**
  String get login_noAccountLabel;

  /// No description provided for @invalid_email.
  ///
  /// In en, this message translates to:
  /// **'Not a correct email'**
  String get invalid_email;

  /// No description provided for @signup_invalid_domain.
  ///
  /// In en, this message translates to:
  /// **'Only agenteunico.net emails allowed'**
  String get signup_invalid_domain;

  /// No description provided for @login_signUpBtn.
  ///
  /// In en, this message translates to:
  /// **'Sign up now!'**
  String get login_signUpBtn;

  /// No description provided for @signup_title.
  ///
  /// In en, this message translates to:
  /// **'Sign up'**
  String get signup_title;

  /// No description provided for @signup_onetimecodeLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter the token to access registry'**
  String get signup_onetimecodeLabel;

  /// No description provided for @signup_onetimecodeHelper.
  ///
  /// In en, this message translates to:
  /// **'The token given by your organisation.'**
  String get signup_onetimecodeHelper;

  /// No description provided for @signup_onetimecodeError.
  ///
  /// In en, this message translates to:
  /// **'That is not the correct code'**
  String get signup_onetimecodeError;

  /// No description provided for @signup_validationcodeLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter the code we have sent by eMail'**
  String get signup_validationcodeLabel;

  /// No description provided for @signup_validationcodeHelper.
  ///
  /// In en, this message translates to:
  /// **'If you haven\'t received anything, please check your spam folder.'**
  String get signup_validationcodeHelper;

  /// No description provided for @signup_resendValidationCodeBtn.
  ///
  /// In en, this message translates to:
  /// **'Reenviar código de validación'**
  String get signup_resendValidationCodeBtn;

  /// No description provided for @signup_fieldsBlocTitle.
  ///
  /// In en, this message translates to:
  /// **'Signup data'**
  String get signup_fieldsBlocTitle;

  /// No description provided for @signup_passwordLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get signup_passwordLabel;

  /// No description provided for @signup_password2Label.
  ///
  /// In en, this message translates to:
  /// **'Password (repeat)'**
  String get signup_password2Label;

  /// No description provided for @signup_password2Error.
  ///
  /// In en, this message translates to:
  /// **'Must match \'Password\''**
  String get signup_password2Error;

  /// No description provided for @signup_acceptEulaTitle.
  ///
  /// In en, this message translates to:
  /// **'I have read and accept the <a href=\'{eulaUrl}\'>conditions of use</a> and the <a href=\'{privacyPolicyUrl}\'>privacy policy</a>'**
  String signup_acceptEulaTitle(String eulaUrl, String privacyPolicyUrl);

  /// No description provided for @signup_welcomeTitle.
  ///
  /// In en, this message translates to:
  /// **'Hi {name}!'**
  String signup_welcomeTitle(String name);

  /// No description provided for @signup_welcomeText.
  ///
  /// In en, this message translates to:
  /// **'welcome to topbrokers.io'**
  String get signup_welcomeText;

  /// No description provided for @signup_loginBtn.
  ///
  /// In en, this message translates to:
  /// **'log in'**
  String get signup_loginBtn;

  /// No description provided for @signup_err_emailalredyexists.
  ///
  /// In en, this message translates to:
  /// **'The email entered already exists'**
  String get signup_err_emailalredyexists;

  /// No description provided for @signup_err_validationcodeinvalid.
  ///
  /// In en, this message translates to:
  /// **'Validation code doens\'t exist or has expired'**
  String get signup_err_validationcodeinvalid;

  /// No description provided for @signup_err_onetimecodeinvalid.
  ///
  /// In en, this message translates to:
  /// **'Onetime code doesn\'t exist or has expired'**
  String get signup_err_onetimecodeinvalid;

  /// No description provided for @home_tabs_search.
  ///
  /// In en, this message translates to:
  /// **'search'**
  String get home_tabs_search;

  /// No description provided for @home_tabs_favorites.
  ///
  /// In en, this message translates to:
  /// **'favorites'**
  String get home_tabs_favorites;

  /// No description provided for @home_tabs_alerts.
  ///
  /// In en, this message translates to:
  /// **'alerts'**
  String get home_tabs_alerts;

  /// No description provided for @home_tabs_offers.
  ///
  /// In en, this message translates to:
  /// **'offers'**
  String get home_tabs_offers;

  /// No description provided for @home_tabs_contacts.
  ///
  /// In en, this message translates to:
  /// **'contacts'**
  String get home_tabs_contacts;

  /// No description provided for @home_tabs_actions.
  ///
  /// In en, this message translates to:
  /// **'actions'**
  String get home_tabs_actions;

  /// No description provided for @offer_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problem loading data'**
  String get offer_errLoading;

  /// No description provided for @offer_errDoesnExist.
  ///
  /// In en, this message translates to:
  /// **'The offer doesn\'t exist'**
  String get offer_errDoesnExist;

  /// No description provided for @offer_favouriteLabel.
  ///
  /// In en, this message translates to:
  /// **'Favorite'**
  String get offer_favouriteLabel;

  /// No description provided for @offer_srcadHeading.
  ///
  /// In en, this message translates to:
  /// **'Original Ad'**
  String get offer_srcadHeading;

  /// No description provided for @offer_srcad_updatedAtLabel.
  ///
  /// In en, this message translates to:
  /// **'Last update'**
  String get offer_srcad_updatedAtLabel;

  /// No description provided for @offer_srcad_advertiserLabel.
  ///
  /// In en, this message translates to:
  /// **'Advertiser'**
  String get offer_srcad_advertiserLabel;

  /// No description provided for @offer_srcad_individualLabel.
  ///
  /// In en, this message translates to:
  /// **'Individual'**
  String get offer_srcad_individualLabel;

  /// No description provided for @offer_srcad_nameLabel.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get offer_srcad_nameLabel;

  /// No description provided for @offer_srcad_webpageLabel.
  ///
  /// In en, this message translates to:
  /// **'Web page'**
  String get offer_srcad_webpageLabel;

  /// No description provided for @offer_srcad_referenceLabel.
  ///
  /// In en, this message translates to:
  /// **'Ref.'**
  String get offer_srcad_referenceLabel;

  /// No description provided for @offer_mainHeading.
  ///
  /// In en, this message translates to:
  /// **'Main data'**
  String get offer_mainHeading;

  /// No description provided for @offer_agentHeading.
  ///
  /// In en, this message translates to:
  /// **'Agent'**
  String get offer_agentHeading;

  /// No description provided for @offer_ownerHeading.
  ///
  /// In en, this message translates to:
  /// **'Owner'**
  String get offer_ownerHeading;

  /// No description provided for @offer_picturesHeading.
  ///
  /// In en, this message translates to:
  /// **'Pictures'**
  String get offer_picturesHeading;

  /// No description provided for @offer_addressHeading.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get offer_addressHeading;

  /// No description provided for @offer_featuresHeading.
  ///
  /// In en, this message translates to:
  /// **'Features'**
  String get offer_featuresHeading;

  /// No description provided for @offer_descriptionHeading.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get offer_descriptionHeading;

  /// No description provided for @offer_internalsHeading.
  ///
  /// In en, this message translates to:
  /// **'Internals'**
  String get offer_internalsHeading;

  /// No description provided for @offer_actionsHeading.
  ///
  /// In en, this message translates to:
  /// **'Actions'**
  String get offer_actionsHeading;

  /// No description provided for @offer_matchingsHeading.
  ///
  /// In en, this message translates to:
  /// **'Matchings (alerts)'**
  String get offer_matchingsHeading;

  /// No description provided for @offer_publicationsHeading.
  ///
  /// In en, this message translates to:
  /// **'Publish in portals'**
  String get offer_publicationsHeading;

  /// No description provided for @offer_cantPublishDisclaimer.
  ///
  /// In en, this message translates to:
  /// **'The offer needs to be in <b>commercialization</b> state before you can publish it on a real estate portal.<br/>Please, edit the offer (pencil button) and change it\'\'s state before'**
  String get offer_cantPublishDisclaimer;

  /// No description provided for @offer_sharingsHeading.
  ///
  /// In en, this message translates to:
  /// **'Share with'**
  String get offer_sharingsHeading;

  /// No description provided for @offer_createBtn.
  ///
  /// In en, this message translates to:
  /// **'Create offer'**
  String get offer_createBtn;

  /// No description provided for @offer_saveBtn.
  ///
  /// In en, this message translates to:
  /// **'Save changes'**
  String get offer_saveBtn;

  /// No description provided for @offer_titleNew.
  ///
  /// In en, this message translates to:
  /// **'New offer'**
  String get offer_titleNew;

  /// No description provided for @offer_title.
  ///
  /// In en, this message translates to:
  /// **'Offer'**
  String get offer_title;

  /// No description provided for @offer_stateDialog_title.
  ///
  /// In en, this message translates to:
  /// **'Status change'**
  String get offer_stateDialog_title;

  /// No description provided for @offer_stateDialog_bodyHTML.
  ///
  /// In en, this message translates to:
  /// **'<p>Only offers in commercialization can be published on real estate portals or shared in working groups. <br/> If you continue, the offer will be withdrawn from all portals and working groups in which it is published.</p><p>Do you want to continue?</p>'**
  String get offer_stateDialog_bodyHTML;

  /// No description provided for @offer_stateDialog_yes.
  ///
  /// In en, this message translates to:
  /// **'Yes continue'**
  String get offer_stateDialog_yes;

  /// No description provided for @offer_stateDialog_not.
  ///
  /// In en, this message translates to:
  /// **'No, cancel'**
  String get offer_stateDialog_not;

  /// No description provided for @offers_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading offers'**
  String get offers_errLoading;

  /// No description provided for @offers_noOffers.
  ///
  /// In en, this message translates to:
  /// **'No offers'**
  String get offers_noOffers;

  /// No description provided for @offers_addFirstOffer.
  ///
  /// In en, this message translates to:
  /// **'Add offer'**
  String get offers_addFirstOffer;

  /// No description provided for @offers_item_surfaceM2.
  ///
  /// In en, this message translates to:
  /// **'{total}m²'**
  String offers_item_surfaceM2(int total);

  /// No description provided for @offers_item_bethroomsCount.
  ///
  /// In en, this message translates to:
  /// **'{total} bedrooms'**
  String offers_item_bethroomsCount(int total);

  /// No description provided for @offers_item_exteriorTxt.
  ///
  /// In en, this message translates to:
  /// **'exterior'**
  String get offers_item_exteriorTxt;

  /// No description provided for @offersfilter_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading data'**
  String get offersfilter_errLoading;

  /// No description provided for @offersfilter_sourceContactPhoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Contact phone'**
  String get offersfilter_sourceContactPhoneLabel;

  /// No description provided for @offersfilter_isVersionLabel.
  ///
  /// In en, this message translates to:
  /// **'Versions?'**
  String get offersfilter_isVersionLabel;

  /// No description provided for @offersfilter_isVersion_yes.
  ///
  /// In en, this message translates to:
  /// **'Versions only'**
  String get offersfilter_isVersion_yes;

  /// No description provided for @offersfilter_isVersion_not.
  ///
  /// In en, this message translates to:
  /// **'Main offers only'**
  String get offersfilter_isVersion_not;

  /// No description provided for @offersfilter_customerTypeLabel.
  ///
  /// In en, this message translates to:
  /// **'Owner type'**
  String get offersfilter_customerTypeLabel;

  /// No description provided for @offersfilter_customerLabel.
  ///
  /// In en, this message translates to:
  /// **'Owner'**
  String get offersfilter_customerLabel;

  /// No description provided for @offersfilter_zoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Zone'**
  String get offersfilter_zoneLabel;

  /// No description provided for @offersfilter_cityLabel.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get offersfilter_cityLabel;

  /// No description provided for @offersfilter_cityEmptyText.
  ///
  /// In en, this message translates to:
  /// **'Select a city'**
  String get offersfilter_cityEmptyText;

  /// No description provided for @offersfilter_typeLabel.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get offersfilter_typeLabel;

  /// No description provided for @offersfilter_operationLabel.
  ///
  /// In en, this message translates to:
  /// **'Operation'**
  String get offersfilter_operationLabel;

  /// No description provided for @offersfilter_m2Label.
  ///
  /// In en, this message translates to:
  /// **'Surface'**
  String get offersfilter_m2Label;

  /// No description provided for @offersfilter_minM2Label.
  ///
  /// In en, this message translates to:
  /// **'Min surface'**
  String get offersfilter_minM2Label;

  /// No description provided for @offersfilter_maxM2Label.
  ///
  /// In en, this message translates to:
  /// **'Max surface'**
  String get offersfilter_maxM2Label;

  /// No description provided for @offersfilter_saleAmountLabel.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get offersfilter_saleAmountLabel;

  /// No description provided for @offersfilter_rentAmountLabel.
  ///
  /// In en, this message translates to:
  /// **'Rent'**
  String get offersfilter_rentAmountLabel;

  /// No description provided for @offersfilter_saleMinAmountLabel.
  ///
  /// In en, this message translates to:
  /// **'Min price'**
  String get offersfilter_saleMinAmountLabel;

  /// No description provided for @offersfilter_saleMaxAmountLabel.
  ///
  /// In en, this message translates to:
  /// **'Max price'**
  String get offersfilter_saleMaxAmountLabel;

  /// No description provided for @offersfilter_rentMinAmountLabel.
  ///
  /// In en, this message translates to:
  /// **'Min rent'**
  String get offersfilter_rentMinAmountLabel;

  /// No description provided for @offersfilter_rentMaxAmountLabel.
  ///
  /// In en, this message translates to:
  /// **'Max rent'**
  String get offersfilter_rentMaxAmountLabel;

  /// No description provided for @offersfilter_showNotMine.
  ///
  /// In en, this message translates to:
  /// **'Show other agents opportunities'**
  String get offersfilter_showNotMine;

  /// No description provided for @cloudoffers_filter_favoritesOnlyLabel.
  ///
  /// In en, this message translates to:
  /// **'Favorites only'**
  String get cloudoffers_filter_favoritesOnlyLabel;

  /// No description provided for @cloudoffers_filter_includeIndividualLabel.
  ///
  /// In en, this message translates to:
  /// **'Individuals'**
  String get cloudoffers_filter_includeIndividualLabel;

  /// No description provided for @cloudoffers_filter_includeProfessionalLabel.
  ///
  /// In en, this message translates to:
  /// **'Professionals'**
  String get cloudoffers_filter_includeProfessionalLabel;

  /// No description provided for @cloudoffers_filter_sourceContactPhoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Contact phone #'**
  String get cloudoffers_filter_sourceContactPhoneLabel;

  /// No description provided for @cloudoffers_filter_publicationDateLabel.
  ///
  /// In en, this message translates to:
  /// **'Publication date'**
  String get cloudoffers_filter_publicationDateLabel;

  /// No description provided for @cloudoffers_filter_publicationDate_manually.
  ///
  /// In en, this message translates to:
  /// **'Enter manually'**
  String get cloudoffers_filter_publicationDate_manually;

  /// No description provided for @cloudoffers_filter_publicationDate_last48h.
  ///
  /// In en, this message translates to:
  /// **'Last 48h'**
  String get cloudoffers_filter_publicationDate_last48h;

  /// No description provided for @cloudoffers_filter_publicationDate_thisWeek.
  ///
  /// In en, this message translates to:
  /// **'This week'**
  String get cloudoffers_filter_publicationDate_thisWeek;

  /// No description provided for @cloudoffers_filter_publicationDate_lastWeek.
  ///
  /// In en, this message translates to:
  /// **'Last week'**
  String get cloudoffers_filter_publicationDate_lastWeek;

  /// No description provided for @cloudoffers_filter_publicationDate_thisMonth.
  ///
  /// In en, this message translates to:
  /// **'This month'**
  String get cloudoffers_filter_publicationDate_thisMonth;

  /// No description provided for @cloudoffers_filter_publicationDate_lastMonth.
  ///
  /// In en, this message translates to:
  /// **'Last month'**
  String get cloudoffers_filter_publicationDate_lastMonth;

  /// No description provided for @cloudoffers_filter_publicationDate_hint.
  ///
  /// In en, this message translates to:
  /// **'Any'**
  String get cloudoffers_filter_publicationDate_hint;

  /// No description provided for @cloudoffers_filter_publicationDate_from.
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get cloudoffers_filter_publicationDate_from;

  /// No description provided for @cloudoffers_filter_publicationDate_to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get cloudoffers_filter_publicationDate_to;

  /// No description provided for @cloudoffers_import_addNotExistsLabel.
  ///
  /// In en, this message translates to:
  /// **'The ad no longer exists'**
  String get cloudoffers_import_addNotExistsLabel;

  /// No description provided for @cloudoffers_import_waitingForLastVersionLabel.
  ///
  /// In en, this message translates to:
  /// **'Getting updated version of ad'**
  String get cloudoffers_import_waitingForLastVersionLabel;

  /// No description provided for @cloudoffers_import_newPropertyOwnerLabel.
  ///
  /// In en, this message translates to:
  /// **'Owner of the new property'**
  String get cloudoffers_import_newPropertyOwnerLabel;

  /// No description provided for @cloudoffers_import_createOfferLabel.
  ///
  /// In en, this message translates to:
  /// **'Create offer'**
  String get cloudoffers_import_createOfferLabel;

  /// No description provided for @cloudoffers_import_creatingOfferLabel.
  ///
  /// In en, this message translates to:
  /// **'Creating offer'**
  String get cloudoffers_import_creatingOfferLabel;

  /// No description provided for @cloudoffers_import_doneLabel.
  ///
  /// In en, this message translates to:
  /// **'Offer created successfully'**
  String get cloudoffers_import_doneLabel;

  /// No description provided for @cloudoffers_import_seeDetailsLabel.
  ///
  /// In en, this message translates to:
  /// **'See details'**
  String get cloudoffers_import_seeDetailsLabel;

  /// No description provided for @cloudoffers_import_err_importing.
  ///
  /// In en, this message translates to:
  /// **'Problems while import'**
  String get cloudoffers_import_err_importing;

  /// No description provided for @cloudoffers_refresh_doneLabel.
  ///
  /// In en, this message translates to:
  /// **'Ad refreshed successfully'**
  String get cloudoffers_refresh_doneLabel;

  /// No description provided for @cloudoffers_refresh_errLabel.
  ///
  /// In en, this message translates to:
  /// **'Problems while refreshing'**
  String get cloudoffers_refresh_errLabel;

  /// No description provided for @cloudoffers_export_reportLabel.
  ///
  /// In en, this message translates to:
  /// **'What to export'**
  String get cloudoffers_export_reportLabel;

  /// No description provided for @cloudoffers_export_formatLabel.
  ///
  /// In en, this message translates to:
  /// **'In which format'**
  String get cloudoffers_export_formatLabel;

  /// No description provided for @cloudoffers_export_maxrowsLabel.
  ///
  /// In en, this message translates to:
  /// **'How many rows (maximum)'**
  String get cloudoffers_export_maxrowsLabel;

  /// No description provided for @cloudoffers_export_exportBtn.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get cloudoffers_export_exportBtn;

  /// No description provided for @cloudoffers_export_retrievingRows.
  ///
  /// In en, this message translates to:
  /// **'Retrieving rows'**
  String get cloudoffers_export_retrievingRows;

  /// No description provided for @cloudoffers_export_retrievedCount.
  ///
  /// In en, this message translates to:
  /// **'Total: {count}'**
  String cloudoffers_export_retrievedCount(int count);

  /// No description provided for @cloudoffers_export_generatingFile.
  ///
  /// In en, this message translates to:
  /// **'Generating file for {count} rows'**
  String cloudoffers_export_generatingFile(int count);

  /// No description provided for @cloudoffers_export_done_titleLabel.
  ///
  /// In en, this message translates to:
  /// **'All done!!'**
  String get cloudoffers_export_done_titleLabel;

  /// No description provided for @cloudoffers_export_done_totalrowsLabel.
  ///
  /// In en, this message translates to:
  /// **'Exported: {count} rows'**
  String cloudoffers_export_done_totalrowsLabel(int count);

  /// No description provided for @contactsfilter_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading data'**
  String get contactsfilter_errLoading;

  /// No description provided for @contactsfilter_searchLabel.
  ///
  /// In en, this message translates to:
  /// **'Search text'**
  String get contactsfilter_searchLabel;

  /// No description provided for @contactsfilter_isOfferCustomerLabel.
  ///
  /// In en, this message translates to:
  /// **'Is offertant?'**
  String get contactsfilter_isOfferCustomerLabel;

  /// No description provided for @contactsfilter_isBankServicerLabel.
  ///
  /// In en, this message translates to:
  /// **'Is Bank Servicer?'**
  String get contactsfilter_isBankServicerLabel;

  /// No description provided for @contactsfilter_hasSiteLabel.
  ///
  /// In en, this message translates to:
  /// **'Has site (tinder.topbroekes.io)?'**
  String get contactsfilter_hasSiteLabel;

  /// No description provided for @contact_titleNew.
  ///
  /// In en, this message translates to:
  /// **'New contact'**
  String get contact_titleNew;

  /// No description provided for @contact_title.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact_title;

  /// No description provided for @contact_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading contact information'**
  String get contact_errLoading;

  /// No description provided for @contact_errDoesnExist.
  ///
  /// In en, this message translates to:
  /// **'The contact doesn\'t exist'**
  String get contact_errDoesnExist;

  /// No description provided for @contact_createBtn.
  ///
  /// In en, this message translates to:
  /// **'Create contact'**
  String get contact_createBtn;

  /// No description provided for @contact_saveBtn.
  ///
  /// In en, this message translates to:
  /// **'Save changes'**
  String get contact_saveBtn;

  /// No description provided for @contact_nameLabel.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get contact_nameLabel;

  /// No description provided for @contact_lastnameLabel.
  ///
  /// In en, this message translates to:
  /// **'Surnames'**
  String get contact_lastnameLabel;

  /// No description provided for @contact_emailLabel.
  ///
  /// In en, this message translates to:
  /// **'eMail'**
  String get contact_emailLabel;

  /// No description provided for @contact_mobileLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone number (mobile)'**
  String get contact_mobileLabel;

  /// No description provided for @contact_siteLabel.
  ///
  /// In en, this message translates to:
  /// **'Contact site (tinder.topbrokers.io)'**
  String get contact_siteLabel;

  /// No description provided for @contact_notesLabel.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get contact_notesLabel;

  /// No description provided for @contact_offersLabel.
  ///
  /// In en, this message translates to:
  /// **'Ofertas'**
  String get contact_offersLabel;

  /// No description provided for @contact_addOfferBtn.
  ///
  /// In en, this message translates to:
  /// **'nueva'**
  String get contact_addOfferBtn;

  /// No description provided for @contact_demandsLabel.
  ///
  /// In en, this message translates to:
  /// **'Alerts'**
  String get contact_demandsLabel;

  /// No description provided for @contact_addDemandBtn.
  ///
  /// In en, this message translates to:
  /// **'nueva'**
  String get contact_addDemandBtn;

  /// No description provided for @contact_actionsLabel.
  ///
  /// In en, this message translates to:
  /// **'Acciones'**
  String get contact_actionsLabel;

  /// No description provided for @contact_addActionBtn.
  ///
  /// In en, this message translates to:
  /// **'nueva'**
  String get contact_addActionBtn;

  /// No description provided for @agent_titleNew.
  ///
  /// In en, this message translates to:
  /// **'New agent'**
  String get agent_titleNew;

  /// No description provided for @agent_title.
  ///
  /// In en, this message translates to:
  /// **'Agent'**
  String get agent_title;

  /// No description provided for @agent_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading agent information'**
  String get agent_errLoading;

  /// No description provided for @agent_errDoesnExist.
  ///
  /// In en, this message translates to:
  /// **'The agent doesn\'t exist'**
  String get agent_errDoesnExist;

  /// No description provided for @agent_saveBtn.
  ///
  /// In en, this message translates to:
  /// **'Save changes'**
  String get agent_saveBtn;

  /// No description provided for @agent_nameLabel.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get agent_nameLabel;

  /// No description provided for @agent_lastnameLabel.
  ///
  /// In en, this message translates to:
  /// **'Surnames'**
  String get agent_lastnameLabel;

  /// No description provided for @agent_emailLabel.
  ///
  /// In en, this message translates to:
  /// **'eMail'**
  String get agent_emailLabel;

  /// No description provided for @agent_mobileLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone number (mobile)'**
  String get agent_mobileLabel;

  /// No description provided for @recoverpass_title.
  ///
  /// In en, this message translates to:
  /// **'Recover password'**
  String get recoverpass_title;

  /// No description provided for @recoverpass_validationcodeHelper.
  ///
  /// In en, this message translates to:
  /// **'Enter the code that we have sent you by eMail. If you have not received anything, it is possible that the indicated address is not associated with any user or, may be, is in your spam folder. .'**
  String get recoverpass_validationcodeHelper;

  /// No description provided for @recoverpass_finalMessageText.
  ///
  /// In en, this message translates to:
  /// **'Password changed'**
  String get recoverpass_finalMessageText;

  /// No description provided for @demand_titleNew.
  ///
  /// In en, this message translates to:
  /// **'New alert'**
  String get demand_titleNew;

  /// No description provided for @demands_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading alerts'**
  String get demands_errLoading;

  /// No description provided for @demands_noDemands.
  ///
  /// In en, this message translates to:
  /// **'No alerts'**
  String get demands_noDemands;

  /// No description provided for @demands_addFirstDemand.
  ///
  /// In en, this message translates to:
  /// **'Create an alert'**
  String get demands_addFirstDemand;

  /// No description provided for @demands_problems.
  ///
  /// In en, this message translates to:
  /// **'Problems getting alerts'**
  String get demands_problems;

  /// No description provided for @demands_notFound.
  ///
  /// In en, this message translates to:
  /// **'No alerts found'**
  String get demands_notFound;

  /// No description provided for @demands_register.
  ///
  /// In en, this message translates to:
  /// **'Register an alert'**
  String get demands_register;

  /// No description provided for @opportunities_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading opportunities'**
  String get opportunities_errLoading;

  /// No description provided for @opportunities_noOpportunities.
  ///
  /// In en, this message translates to:
  /// **'No oportunities found'**
  String get opportunities_noOpportunities;

  /// No description provided for @opportunities_noOffers.
  ///
  /// In en, this message translates to:
  /// **'No offers'**
  String get opportunities_noOffers;

  /// No description provided for @opportunities_noDemands.
  ///
  /// In en, this message translates to:
  /// **'No alerts'**
  String get opportunities_noDemands;

  /// No description provided for @opportunities_noOffersOrDemands.
  ///
  /// In en, this message translates to:
  /// **'No offers or alerts'**
  String get opportunities_noOffersOrDemands;

  /// No description provided for @opportunities_addFirstOffer.
  ///
  /// In en, this message translates to:
  /// **'Create an Offer'**
  String get opportunities_addFirstOffer;

  /// No description provided for @opportunities_addFirstDemand.
  ///
  /// In en, this message translates to:
  /// **'Create an Alert'**
  String get opportunities_addFirstDemand;

  /// No description provided for @matchings_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading matchings from server'**
  String get matchings_errLoading;

  /// No description provided for @matchings_noMatchings.
  ///
  /// In en, this message translates to:
  /// **'No matchings'**
  String get matchings_noMatchings;

  /// No description provided for @action_titleNew.
  ///
  /// In en, this message translates to:
  /// **'New action'**
  String get action_titleNew;

  /// No description provided for @action_title.
  ///
  /// In en, this message translates to:
  /// **'Action'**
  String get action_title;

  /// No description provided for @action_mainHeading.
  ///
  /// In en, this message translates to:
  /// **'Main data'**
  String get action_mainHeading;

  /// No description provided for @action_typeLabel.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get action_typeLabel;

  /// No description provided for @action_whenLabel.
  ///
  /// In en, this message translates to:
  /// **'When'**
  String get action_whenLabel;

  /// No description provided for @action_doneLabel.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get action_doneLabel;

  /// No description provided for @action_contactLabel.
  ///
  /// In en, this message translates to:
  /// **'With whom'**
  String get action_contactLabel;

  /// No description provided for @action_offerLabel.
  ///
  /// In en, this message translates to:
  /// **'Offer'**
  String get action_offerLabel;

  /// No description provided for @action_notesLabel.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get action_notesLabel;

  /// No description provided for @actions_errLoading.
  ///
  /// In en, this message translates to:
  /// **'Problems loading actions from server'**
  String get actions_errLoading;

  /// No description provided for @actions_noActions.
  ///
  /// In en, this message translates to:
  /// **'No actions'**
  String get actions_noActions;

  /// No description provided for @actions_noActionsYet.
  ///
  /// In en, this message translates to:
  /// **'No actions found'**
  String get actions_noActionsYet;

  /// No description provided for @actions_addFirstAction.
  ///
  /// In en, this message translates to:
  /// **'Register a new action'**
  String get actions_addFirstAction;

  /// No description provided for @actions_filterTooltip.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get actions_filterTooltip;

  /// No description provided for @actions_filter_todayLabel.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get actions_filter_todayLabel;

  /// No description provided for @actions_filter_fromTodayLabel.
  ///
  /// In en, this message translates to:
  /// **'From today'**
  String get actions_filter_fromTodayLabel;

  /// No description provided for @actions_filter_untilTodayLabel.
  ///
  /// In en, this message translates to:
  /// **'Until today'**
  String get actions_filter_untilTodayLabel;

  /// No description provided for @actions_filter_doneLabel.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get actions_filter_doneLabel;

  /// No description provided for @actions_filter_undoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Undone'**
  String get actions_filter_undoneLabel;

  /// No description provided for @actions_filter_doneAndUndoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Done and undone'**
  String get actions_filter_doneAndUndoneLabel;

  /// No description provided for @serviceaction_titleNew.
  ///
  /// In en, this message translates to:
  /// **'Superpower'**
  String get serviceaction_titleNew;

  /// No description provided for @serviceaction_title.
  ///
  /// In en, this message translates to:
  /// **'Superpower'**
  String get serviceaction_title;

  /// No description provided for @serviceaction_typeLabel.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get serviceaction_typeLabel;

  /// No description provided for @serviceaction_whenLabel.
  ///
  /// In en, this message translates to:
  /// **'When'**
  String get serviceaction_whenLabel;

  /// No description provided for @serviceaction_doneLabel.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get serviceaction_doneLabel;

  /// No description provided for @serviceaction_contactLabel.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get serviceaction_contactLabel;

  /// No description provided for @serviceaction_offerLabel.
  ///
  /// In en, this message translates to:
  /// **'Offer'**
  String get serviceaction_offerLabel;

  /// No description provided for @serviceaction_notesLabel.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get serviceaction_notesLabel;

  /// No description provided for @offer_cloneDialog_title.
  ///
  /// In en, this message translates to:
  /// **'Create an offer version'**
  String get offer_cloneDialog_title;

  /// No description provided for @offer_cloneDialog_message.
  ///
  /// In en, this message translates to:
  /// **'Do yuou want to create a version of the offer {offer_id} ?'**
  String offer_cloneDialog_message(int offer_id);

  /// No description provided for @offer_cloneDialog_yes.
  ///
  /// In en, this message translates to:
  /// **'Yes, continue'**
  String get offer_cloneDialog_yes;

  /// No description provided for @offer_cloneDialog_not.
  ///
  /// In en, this message translates to:
  /// **'No, cancel'**
  String get offer_cloneDialog_not;

  /// No description provided for @offer_cloneDialog_type_message.
  ///
  /// In en, this message translates to:
  /// **'Select version type'**
  String get offer_cloneDialog_type_message;

  /// No description provided for @offer_cloneDialog_menu_option.
  ///
  /// In en, this message translates to:
  /// **'Create version'**
  String get offer_cloneDialog_menu_option;

  /// No description provided for @offer_versionHeading.
  ///
  /// In en, this message translates to:
  /// **'Version data'**
  String get offer_versionHeading;

  /// No description provided for @common_versionOf.
  ///
  /// In en, this message translates to:
  /// **'Source offer'**
  String get common_versionOf;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'es'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
