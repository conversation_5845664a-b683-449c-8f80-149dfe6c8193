{"app_title": "Percent", "app_err_loading": "App can't be initialized", "common_and": "and", "common_add": "add", "common_edit": "edit", "common_accept": "accept", "common_Accept": "Aceptar", "common_acceptChanges": "accept changes", "common_create": "create", "common_continue": "continue", "common_Continue": "Continue", "common_cancel": "cancel", "common_Cancel": "Cancel", "common_close": "close", "common_Close": "Close", "common_retry": "try again", "common_Retry": "Try again", "common_errLoading": "Problems loading data", "common_formWithErrors": "The form contains errors. Check it out.", "common_forSale": "for sale", "common_forRent": "for rent", "common_sale": "sale", "common_rent": "rent", "common_marketPrice": "market price", "common_try_again": "Try again", "common_see_more": "See more", "common_selectAContact": "Select a contact", "common_selectAnOffer": "Select an offer", "common_AnyLabel": "Any", "common_NotMatterLabel": "Not matter", "common_YesLabel": "Yes", "common_NotLabel": "Not", "common_HouseLabel": "House", "common_FlatLabel": "Flat", "common_SaleLabel": "Sale", "common_RentLabel": "Rent", "common_detailsLabel": "details", "common_msg_urlCopiedToClipboard": "Url copied to clipboard", "common_err_unexpected": "Unexpected problem", "common_err_unexpected_Tryagain": "Unexpected problem. Try again later", "common_err_unexpected_Msg": "Unexpected problem: {message}", "@common_err_unexpected_Msg": {"placeholders": {"message": {"type": "String"}}}, "landing_logIn": "Sign in", "landing_signUp": "Sign up", "landing_legalNotice": "Legal Notice", "login_emailLabel": "eMail", "login_passwordLabel": "Password", "login_signinBtn": "log in", "login_forgotPasswordLabel": "Forgot your password?", "login_recoverPasswordBtn": "Recover it!", "login_noAccountLabel": "Don't have a Topbrokers account?", "invalid_email": "Not a correct email", "signup_invalid_domain": "Only agenteunico.net emails allowed", "login_signUpBtn": "Sign up now!", "signup_title": "Sign up", "signup_onetimecodeLabel": "Enter the token to access registry", "signup_onetimecodeHelper": "The token given by your organisation.", "signup_onetimecodeError": "That is not the correct code", "signup_validationcodeLabel": "Enter the code we have sent by eMail", "signup_validationcodeHelper": "If you haven't received anything, please check your spam folder.", "signup_resendValidationCodeBtn": "Reenviar código de validación", "signup_fieldsBlocTitle": "Signup data", "signup_passwordLabel": "Password", "signup_password2Label": "Password (repeat)", "signup_password2Error": "Must match 'Password'", "signup_acceptEulaTitle": "I have read and accept the <a href='{eulaUrl}'>conditions of use</a> and the <a href='{privacyPolicyUrl}'>privacy policy</a>", "@signup_acceptEulaTitle": {"placeholders": {"eulaUrl": {"type": "String"}, "privacyPolicyUrl": {"type": "String"}}}, "signup_welcomeTitle": "Hi {name}!", "@signup_welcomeTitle": {"placeholders": {"name": {"type": "String"}}}, "signup_welcomeText": "welcome to topbrokers.io", "signup_loginBtn": "log in", "signup_err_emailalredyexists": "The email entered already exists", "signup_err_validationcodeinvalid": "Validation code doens't exist or has expired", "signup_err_onetimecodeinvalid": "Onetime code doesn't exist or has expired", "home_tabs_search": "search", "home_tabs_favorites": "favorites", "home_tabs_alerts": "alerts", "home_tabs_offers": "offers", "home_tabs_contacts": "contacts", "home_tabs_actions": "actions", "offer_errLoading": "Problem loading data", "offer_errDoesnExist": "The offer doesn't exist", "offer_favouriteLabel": "Favorite", "offer_srcadHeading": "Original Ad", "offer_srcad_updatedAtLabel": "Last update", "offer_srcad_advertiserLabel": "Advertiser", "offer_srcad_individualLabel": "Individual", "offer_srcad_nameLabel": "Name", "offer_srcad_webpageLabel": "Web page", "offer_srcad_referenceLabel": "Ref.", "offer_mainHeading": "Main data", "offer_agentHeading": "Agent", "offer_ownerHeading": "Owner", "offer_picturesHeading": "Pictures", "offer_addressHeading": "Address", "offer_featuresHeading": "Features", "offer_descriptionHeading": "Description", "offer_internalsHeading": "Internals", "offer_actionsHeading": "Actions", "offer_matchingsHeading": "Matchings (alerts)", "offer_publicationsHeading": "Publish in portals", "offer_cantPublishDisclaimer": "The offer needs to be in <b>commercialization</b> state before you can publish it on a real estate portal.<br/>Please, edit the offer (pencil button) and change it''s state before", "offer_sharingsHeading": "Share with", "offer_createBtn": "Create offer", "offer_saveBtn": "Save changes", "offer_titleNew": "New offer", "offer_title": "Offer", "offer_stateDialog_title": "Status change", "offer_stateDialog_bodyHTML": "<p>Only offers in commercialization can be published on real estate portals or shared in working groups. <br/> If you continue, the offer will be withdrawn from all portals and working groups in which it is published.</p><p>Do you want to continue?</p>", "offer_stateDialog_yes": "Yes continue", "offer_stateDialog_not": "No, cancel", "offers_errLoading": "Problems loading offers", "offers_noOffers": "No offers", "offers_addFirstOffer": "Add offer", "offers_item_surfaceM2": "{total}m²", "@offers_item_surfaceM2": {"placeholders": {"total": {"type": "int"}}}, "offers_item_bethroomsCount": "{total} bedrooms", "@offers_item_bethroomsCount": {"placeholders": {"total": {"type": "int"}}}, "offers_item_exteriorTxt": "exterior", "offersfilter_errLoading": "Problems loading data", "offersfilter_sourceContactPhoneLabel": "Contact phone", "offersfilter_isVersionLabel": "Versions?", "offersfilter_isVersion_yes": "Versions only", "offersfilter_isVersion_not": "Main offers only", "offersfilter_customerTypeLabel": "Owner type", "offersfilter_customerLabel": "Owner", "offersfilter_zoneLabel": "Zone", "offersfilter_cityLabel": "City", "offersfilter_cityEmptyText": "Select a city", "offersfilter_typeLabel": "Type", "offersfilter_operationLabel": "Operation", "offersfilter_m2Label": "Surface", "offersfilter_minM2Label": "Min surface", "offersfilter_maxM2Label": "Max surface", "offersfilter_saleAmountLabel": "Price", "offersfilter_rentAmountLabel": "Rent", "offersfilter_saleMinAmountLabel": "Min price", "offersfilter_saleMaxAmountLabel": "Max price", "offersfilter_rentMinAmountLabel": "Min rent", "offersfilter_rentMaxAmountLabel": "Max rent", "offersfilter_showNotMine": "Show other agents opportunities", "cloudoffers_filter_favoritesOnlyLabel": "Favorites only", "cloudoffers_filter_includeIndividualLabel": "Individuals", "cloudoffers_filter_includeProfessionalLabel": "Professionals", "cloudoffers_filter_sourceContactPhoneLabel": "Contact phone #", "cloudoffers_filter_publicationDateLabel": "Publication date", "cloudoffers_filter_publicationDate_manually": "Enter manually", "cloudoffers_filter_publicationDate_last48h": "Last 48h", "cloudoffers_filter_publicationDate_thisWeek": "This week", "cloudoffers_filter_publicationDate_lastWeek": "Last week", "cloudoffers_filter_publicationDate_thisMonth": "This month", "cloudoffers_filter_publicationDate_lastMonth": "Last month", "cloudoffers_filter_publicationDate_hint": "Any", "cloudoffers_filter_publicationDate_from": "From", "cloudoffers_filter_publicationDate_to": "To", "cloudoffers_import_addNotExistsLabel": "The ad no longer exists", "cloudoffers_import_waitingForLastVersionLabel": "Getting updated version of ad", "cloudoffers_import_newPropertyOwnerLabel": "Owner of the new property", "cloudoffers_import_createOfferLabel": "Create offer", "cloudoffers_import_creatingOfferLabel": "Creating offer", "cloudoffers_import_doneLabel": "Offer created successfully", "cloudoffers_import_seeDetailsLabel": "See details", "cloudoffers_import_err_importing": "Problems while import", "cloudoffers_refresh_doneLabel": "Ad refreshed successfully", "cloudoffers_refresh_errLabel": "Problems while refreshing", "cloudoffers_export_reportLabel": "What to export", "cloudoffers_export_formatLabel": "In which format", "cloudoffers_export_maxrowsLabel": "How many rows (maximum)", "cloudoffers_export_exportBtn": "Export", "cloudoffers_export_retrievingRows": "Retrieving rows", "cloudoffers_export_retrievedCount": "Total: {count}", "@cloudoffers_export_retrievedCount": {"placeholders": {"count": {"type": "int"}}}, "cloudoffers_export_generatingFile": "Generating file for {count} rows", "@cloudoffers_export_generatingFile": {"placeholders": {"count": {"type": "int"}}}, "cloudoffers_export_done_titleLabel": "All done!!", "cloudoffers_export_done_totalrowsLabel": "Exported: {count} rows", "@cloudoffers_export_done_totalrowsLabel": {"placeholders": {"count": {"type": "int"}}}, "contactsfilter_errLoading": "Problems loading data", "contactsfilter_searchLabel": "Search text", "contactsfilter_isOfferCustomerLabel": "Is offertant?", "contactsfilter_isBankServicerLabel": "Is Bank Servicer?", "contactsfilter_hasSiteLabel": "Has site (tinder.topbroekes.io)?", "contact_titleNew": "New contact", "contact_title": "Contact", "contact_errLoading": "Problems loading contact information", "contact_errDoesnExist": "The contact doesn't exist", "contact_createBtn": "Create contact", "contact_saveBtn": "Save changes", "contact_nameLabel": "Name", "contact_lastnameLabel": "Surnames", "contact_emailLabel": "eMail", "contact_mobileLabel": "Phone number (mobile)", "contact_siteLabel": "Contact site (tinder.topbrokers.io)", "contact_notesLabel": "Notes", "contact_offersLabel": "<PERSON><PERSON><PERSON>", "contact_addOfferBtn": "nueva", "contact_demandsLabel": "<PERSON><PERSON><PERSON>", "contact_addDemandBtn": "nueva", "contact_actionsLabel": "Acciones", "contact_addActionBtn": "nueva", "agent_titleNew": "New agent", "agent_title": "Agent", "agent_errLoading": "Problems loading agent information", "agent_errDoesnExist": "The agent doesn't exist", "agent_saveBtn": "Save changes", "agent_nameLabel": "Name", "agent_lastnameLabel": "Surnames", "agent_emailLabel": "eMail", "agent_mobileLabel": "Phone number (mobile)", "recoverpass_title": "Recover password", "recoverpass_validationcodeHelper": "Enter the code that we have sent you by eMail. If you have not received anything, it is possible that the indicated address is not associated with any user or, may be, is in your spam folder. .", "recoverpass_finalMessageText": "Password changed", "demand_titleNew": "New alert", "demands_errLoading": "Problems loading alerts", "demands_noDemands": "No alerts", "demands_addFirstDemand": "Create an alert", "demands_problems": "Problems getting alerts", "demands_notFound": "No alerts found", "demands_register": "Register an alert", "opportunities_errLoading": "Problems loading opportunities", "opportunities_noOpportunities": "No oportunities found", "opportunities_noOffers": "No offers", "opportunities_noDemands": "No alerts", "opportunities_noOffersOrDemands": "No offers or alerts", "opportunities_addFirstOffer": "Create an Offer", "opportunities_addFirstDemand": "Create an Alert", "matchings_errLoading": "Problems loading matchings from server", "matchings_noMatchings": "No matchings", "action_titleNew": "New action", "action_title": "Action", "action_mainHeading": "Main data", "action_typeLabel": "Type", "action_whenLabel": "When", "action_doneLabel": "Done", "action_contactLabel": "With whom", "action_offerLabel": "Offer", "action_notesLabel": "Notes", "actions_errLoading": "Problems loading actions from server", "actions_noActions": "No actions", "actions_noActionsYet": "No actions found", "actions_addFirstAction": "Register a new action", "actions_filterTooltip": "Filter", "actions_filter_todayLabel": "Today", "actions_filter_fromTodayLabel": "From today", "actions_filter_untilTodayLabel": "Until today", "actions_filter_doneLabel": "Done", "actions_filter_undoneLabel": "Undone", "actions_filter_doneAndUndoneLabel": "Done and undone", "serviceaction_titleNew": "Superpower", "serviceaction_title": "Superpower", "serviceaction_typeLabel": "Type", "serviceaction_whenLabel": "When", "serviceaction_doneLabel": "Done", "serviceaction_contactLabel": "Contact", "serviceaction_offerLabel": "Offer", "serviceaction_notesLabel": "Notes", "offer_cloneDialog_title": "Create an offer version", "offer_cloneDialog_message": "Do y<PERSON><PERSON> want to create a version of the offer {offer_id} ?", "@offer_cloneDialog_message": {"placeholders": {"offer_id": {"type": "int"}}}, "offer_cloneDialog_yes": "Yes, continue", "offer_cloneDialog_not": "No, cancel", "offer_cloneDialog_type_message": "Select version type", "offer_cloneDialog_menu_option": "Create version", "offer_versionHeading": "Version data", "common_versionOf": "Source offer"}