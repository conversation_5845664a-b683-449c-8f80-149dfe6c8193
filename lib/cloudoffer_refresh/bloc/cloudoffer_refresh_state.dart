part of 'cloudoffer_refresh_bloc.dart';

enum CloudofferRefreshStep {
  // Nota:
  //   Posibilidad 1: Api donde se da "entidad" a los checks de una oferta (semántica "añadir check", "consultar estado del check")
  //     POST cloud_offers/:offer_id/checks --> check_id.
  //     GET cloud_offers/:offer_id/checks/:check_id/status -> IN_PROCESS, FINISHED
  //   Posibilidad 2: Api orientada a "operaciones" (RPC) donde se encolan operaciones y se espera resultado
  //     POST agents/me/rpc_ops --> op_id
  //        BODY: {code: "refresh_cloud_offer", params: {id:<string>} }
  //     GET  agents/me/rpc_ops/:op_id -> IN_PROCESS, SUCCESS, ERROR
  //        BODY: {id:<string> /* op_id */, code: "refresh_cloud_offer", params: {id:<string>}, status: "IN_PROGRESS"|"SUCCESS"|"DONE", result: undefined|}
  //     Únicamente permitiríamos una operación RPC "IN_PROGRESS" por agente
  step01WaitingLastVersion,
  // Seleccionar el propietario o añadir uno nuevo (como sucede con  olicitar número de teléfono del propietario (si se desea indicar propietario)
  step02SelectCustomer,
  // Creando oferta: Se muestra un "ruca ruca"
  step03CreatingOffer,
  // Oferta creada:  Mostramos resumen operación y link para abrir la ficha
  step04Done,
  // Anuncio del cloud no existe
  step05NotExists
}

abstract class CloudofferRefreshState extends Equatable {
  /// Identificador de la oferta que deseamos importar.
  final String cloudofferId;

  CloudofferRefreshState({required this.cloudofferId}) : super();

  @override
  List<Object> get props => [];
}

/// Inicialización (cargar formulario, datos asociados...)
class CloudofferRefreshInitState extends CloudofferRefreshState {
  CloudofferRefreshInitState({required String cloudofferId}) : super(cloudofferId: cloudofferId);
}

class CloudofferRefreshStepsState extends CloudofferRefreshState {
  static int _id = 0;

  final CloudofferRefreshStep step;
  
  // Step2 data:
  //  Datos de la oferta del cloud.  Se obtiene en cuanto tenemos una respuesta RPC de que el refresco ha acabao OK
  final OfferDto? cloudOffer;

  final OfferDto? createdOffer;


  final _myid = ++_id;
  @override
  List<Object> get props => [_myid];

  CloudofferRefreshStepsState({
    required String cloudoffer_id,
    required this.step,
    this.cloudOffer,
    this.createdOffer,
  }) : super(cloudofferId: cloudoffer_id);

  /// Clonar objeto por completo
  factory CloudofferRefreshStepsState.copyFrom({
    required CloudofferRefreshStepsState stepsState,
  }) =>
      CloudofferRefreshStepsState(
        cloudoffer_id: stepsState.cloudofferId,
        step: stepsState.step,
        cloudOffer: stepsState.cloudOffer,
        createdOffer: stepsState.createdOffer,
      );

  /// Copiar a un nuevo objeto cambiando algunos valores
  CloudofferRefreshStepsState copyWith({
    CloudofferRefreshStep? step,
    OfferDto? cloudOffer,
    OfferDto? createdOffer,
  }) =>
      CloudofferRefreshStepsState(
        cloudoffer_id: this.cloudofferId,
        step: step ?? this.step,
        cloudOffer: cloudOffer ?? this.cloudOffer,
        createdOffer: createdOffer ?? this.createdOffer,
      );
}

class CloudofferRefreshNotExistsState extends CloudofferRefreshState {
  CloudofferRefreshNotExistsState({required String cloudofferId}) : super(cloudofferId: cloudofferId);
}

class CloudofferRefreshFailureState extends CloudofferRefreshStepsState {
  final String error;
  CloudofferRefreshFailureState({
    required this.error,
    required String cloudoffer_id,
    required CloudofferRefreshStep step,
    String? rpc_id,
    OfferDto? cloudOffer,
    OfferDto? createdOffer,
  }) : super(
          step: step,
          cloudoffer_id: cloudoffer_id,
          cloudOffer: cloudOffer,
          createdOffer: createdOffer,
        );

  factory CloudofferRefreshFailureState.fromStepsState({
    required CloudofferRefreshStepsState stepsState,
    required String error,
  }) {
    return CloudofferRefreshFailureState(
      error: error,
      cloudoffer_id: stepsState.cloudofferId,
      step: stepsState.step,
      cloudOffer: stepsState.cloudOffer,
      createdOffer: stepsState.createdOffer,
    );
  }

  @override
  List<Object> get props => [error, step, cloudofferId];
}
