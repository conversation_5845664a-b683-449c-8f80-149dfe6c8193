part of 'cloudoffer_refresh_bloc.dart';

abstract class CloudofferRefreshEvent extends Equatable {
  const CloudofferRefreshEvent();

  @override
  List<Object> get props => [];
}

///
/// Evento inicial
///
class CloudofferRefreshOnLoadEvent extends CloudofferRefreshEvent {
  CloudofferRefreshOnLoadEvent() : super();

  @override
  List<Object> get props => [];
  @override
  String toString() => "${super.toString()}";
}

class CloudofferRefreshOnTickEvent extends CloudofferRefreshEvent {
  /// Ticks que quedan aún para cumplir con el número de ticks indicado
  final int remaining;
  CloudofferRefreshOnTickEvent({required this.remaining}) : super();
}

///
/// Empezar importación
///
class CloudofferRefreshOnCreateEvent extends CloudofferRefreshEvent {

  CloudofferRefreshOnCreateEvent() : super();
}

class CloudofferRefreshOnCreatedEvent extends CloudofferRefreshEvent {
  final OfferDto createdOffer;
  CloudofferRefreshOnCreatedEvent({required this.createdOffer}) : super();
}

///
/// Se ha producido un error procesando otro evento
///
class CloudofferRefreshOnFailureEvent extends CloudofferRefreshEvent {
  final String message;
  CloudofferRefreshOnFailureEvent({required this.message}) : super();
}
