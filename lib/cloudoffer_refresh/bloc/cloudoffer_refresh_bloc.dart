import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'cloudoffer_refresh_event.dart';
part 'cloudoffer_refresh_state.dart';

class CloudofferRefreshBloc extends Bloc<CloudofferRefreshEvent, CloudofferRefreshState> {
  final AppModelsNS appModels;

  // Subscripción que se crea al iniciar el stream de TickEvents y que se encarga de añadir los eventos a la cola del propio bloque;
  StreamSubscription<CloudofferRefreshOnTickEvent>? _ticksSubscription;

  CloudofferRefreshBloc({required this.appModels, required String cloudofferId}) : super(CloudofferRefreshInitState(cloudofferId: cloudofferId));

  factory CloudofferRefreshBloc.create({required BuildContext context, required String cloudofferId}) {
    return CloudofferRefreshBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      cloudofferId: cloudofferId,
    );
  }

  @override
  close() async {
    await _ticksSubscription?.cancel();
    super.close();
  }

  @override
  Stream<Transition<CloudofferRefreshEvent, CloudofferRefreshState>> transformEvents(
    Stream<CloudofferRefreshEvent> events,
    TransitionFunction<CloudofferRefreshEvent, CloudofferRefreshState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<CloudofferRefreshState> mapEventToState(CloudofferRefreshEvent event) async* {
    if (event is CloudofferRefreshOnLoadEvent)
      yield* _mapOnLoad(state, event);
    else if (state is CloudofferRefreshStepsState && event is CloudofferRefreshOnTickEvent) yield* _mapOnTick(state as CloudofferRefreshStepsState, event);
  }

  Stream<CloudofferRefreshState> _mapOnLoad(CloudofferRefreshState state, CloudofferRefreshOnLoadEvent event) async* {
    yield CloudofferRefreshStepsState(step: CloudofferRefreshStep.step01WaitingLastVersion, cloudoffer_id: state.cloudofferId);
    // Generar OnTickEvent inmediatamente para cambiar al siguiente estado
    _startOnTickEventsGenerator(lapse: 1);
  }

  Stream<CloudofferRefreshState> _mapOnTick(CloudofferRefreshStepsState state, CloudofferRefreshOnTickEvent event) async* {
    if (state.step == CloudofferRefreshStep.step01WaitingLastVersion) {
      // De momento no hacemos nada especial: suponemos que tenemos una oferta en el cloud buena para ser importada
      await this._cancelOnTickEventsGenerator();
      // Leemos los detalles de la oferta y seguimos
      try {
        OfferDto? cloudOffer = null;
        bool resultIsDelayed = false;
        try {
          cloudOffer = await appModels.readOffer(state.cloudofferId, ensure_last_version: true);
        } on ApiAcceptedRequest {
          resultIsDelayed = true;
        }
        if (resultIsDelayed && event.remaining > 0) {
          this._startOnTickEventsGenerator(ticks: event.remaining);
          yield CloudofferRefreshStepsState.copyFrom(stepsState: state);
        } else if (resultIsDelayed) {
          yield CloudofferRefreshFailureState.fromStepsState(
            stepsState: state,
            error: "Tiempo de espera excedido\nInténtalo más tarde",
          );
        } else if (cloudOffer == null || cloudOffer.status.vn?.code.vn == OfferstatusCode.historic) {
          yield CloudofferRefreshNotExistsState(
            cloudofferId: state.cloudofferId,
          );
        } else {
          yield state.copyWith(
            step: CloudofferRefreshStep.step04Done,
          );
        }
      } on Exception {
        yield CloudofferRefreshFailureState.fromStepsState(
          stepsState: state,
          error: "Problemas obteniendo los detalles del anuncio",
        );
      }
    }
  }

  ///
  /// Inicia la generación regular de eventos CloudofferRefreshOnTickEvent  (generar N eventos espaciados en el tiempo)
  /// @param ticks número de eventos a generar
  /// @param lapse segundos entre eventos
  ///
  /// utiliza _cancelTicksStream para dejar de producir eventos antes de tiempo
  ///

  Future<void> _startOnTickEventsGenerator({int ticks = 40, int lapse = 3}) async {
    await this._ticksSubscription?.cancel();
    this._ticksSubscription = Stream.periodic(
      Duration(seconds: lapse),
      (x) => CloudofferRefreshOnTickEvent(remaining: ticks - x - 1),
    ).take(ticks).listen(
      (event) {
        this.add(event);
      },
    );
  }

  Future<void> _cancelOnTickEventsGenerator() async {
    await _ticksSubscription?.cancel();
    this._ticksSubscription = null;
  }
}
