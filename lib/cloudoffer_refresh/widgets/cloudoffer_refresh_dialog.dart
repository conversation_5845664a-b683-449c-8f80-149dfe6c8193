import 'package:topbrokers/cloudoffer_refresh/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

Future<String?> refreshCloudofferDialog(
  context, {
  required String cloudOfferId,
}) {
  return showGeneralDialog<String?>(
      context: context,
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      pageBuilder: (
        BuildContext context,
        Animation animation,
        Animation secondaryAnimation,
      ) {
        return AlertDialog(
          insetPadding: EdgeInsets.all(5.0),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          contentPadding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 5),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            //crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CloudofferRefreshWizzard(
                offerId: cloudOfferId,
                onCanceled: (){
                  Navigator.of(context).pop();
                },                
                onEnded: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          actions: [],
        );
      });
}
