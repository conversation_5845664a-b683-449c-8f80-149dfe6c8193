import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:form_builder/form_builder.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:topbrokers/common/localizations_helper.dart';
import 'package:topbrokers/cloudoffer_refresh/bloc/cloudoffer_refresh_bloc.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/cloudoffers_export/reports/cloudoffers_export_reports.dart';
import 'package:topbrokers/common/widgets/select_contact_field.dart';

class CloudofferRefreshWizzard extends StatefulWidget {
  final String offerId;

  final void Function() onEnded;
  final void Function() onCanceled;

  CloudofferRefreshWizzard({
    Key? key,
    required this.offerId,
    required this.onEnded,
    required this.onCanceled,
  }) : super(key: key ?? AgentorKeys.offerImportPage);

  @override
  _CloudofferRefreshWizzardState createState() => _CloudofferRefreshWizzardState();
}

class _CloudofferRefreshWizzardState extends State<CloudofferRefreshWizzard> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  late CloudofferRefreshBloc _formBloc;
  // Form data
  CloudoffersReportCode reportCode = CloudoffersReportCode.basicinfo;
  ReportFormatCode reportFormatCode = ReportFormatCode.csv;
  //
  _CloudofferRefreshWizzardState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));

    final textTheme = Theme.of(context).textTheme;
    return BlocProvider<CloudofferRefreshBloc>(
      create: (context) {
        _formBloc = CloudofferRefreshBloc.create(context: context, cloudofferId: widget.offerId)
          ..add(CloudofferRefreshOnLoadEvent());
        return _formBloc;
      },
      child: BlocConsumer<CloudofferRefreshBloc, CloudofferRefreshState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          final apploc = context.apploc;
          if (state is CloudofferRefreshFailureState) {
            return _unexpectedError(context, state, this._formBloc);
          } else if (state is CloudofferRefreshNotExistsState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(apploc.cloudoffers_import_addNotExistsLabel, style: textTheme.headline6),
                  ),
                  Divider(height: 20.0, color: Colors.transparent),
                  TextButton.icon(
                    icon: Icon(Icons.close),
                    label: Text(apploc.common_Close),
                    onPressed: () {
                      widget.onCanceled();
                    },
                  ),
                ],
              ),
            );
          } else if (state is CloudofferRefreshStepsState) {
            switch (state.step) {
              case CloudofferRefreshStep.step01WaitingLastVersion:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(16),
                        child: Text(apploc.cloudoffers_import_waitingForLastVersionLabel, style: textTheme.headline6),
                      ),
                      Divider(height: 20.0, color: Colors.transparent),
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                      Divider(height: 20.0, color: Colors.transparent),
                      TextButton.icon(
                        icon: Icon(Icons.close),
                        label: Text(apploc.common_Cancel),
                        onPressed: () {
                          widget.onCanceled();
                        },
                      ),
                    ],
                  ),
                );

              case CloudofferRefreshStep.step04Done:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: Center(
                          child: Text(apploc.cloudoffers_refresh_doneLabel, style: textTheme.headline6),
                        ),
                      ),
                      Divider(height: 40.0, color: Colors.transparent),
                      TextButton.icon(
                        icon: Icon(Icons.check),
                        label: Text(apploc.common_close),
                        onPressed: () {
                          widget.onEnded();
                        },
                      ),
                    ],
                  ),
                );
              default:
                throw Exception("Unexpected wizzard state");
            }
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _unexpectedError(BuildContext context, CloudofferRefreshFailureState state, CloudofferRefreshBloc _formBloc) {
    final localizations = context.getAppLocalizationsOrThrow();
    final textTheme = Theme.of(context).textTheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(16, 0, 16, 4),
            child: Text(
              localizations.cloudoffers_refresh_errLabel,
              style: textTheme.headline6,
            ),
          ),
          Divider(height: 20.0, color: Colors.transparent),
          Center(
            child: Text(
              state.error,
              style: textTheme.bodyLarge,
            ),
          ),
          Divider(height: 40.0, color: Colors.transparent),
          Padding(
            padding: EdgeInsets.all(5),
            child: Center(
              child: TextButton.icon(
                icon: Icon(Icons.check),
                label: Text(localizations.common_Close),
                onPressed: () {
                  widget.onCanceled();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Step02Form extends StatefulWidget {
  final void Function() onCancel;
  final void Function({
    String? contactId,
  }) onContinue;

  const _Step02Form({Key? key, required this.onCancel, required this.onContinue}) : super(key: key);

  @override
  _Step02FormState createState() {
    return _Step02FormState();
  }
}

class _Step02FormState extends State<_Step02Form> {
  final _formKey = GlobalKey<FormState>();
  ContactDto? contact = null;

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    // Build a Form widget using the _formKey created above.
    return Form(
      key: _formKey,
      child: Column(
        children: [
          GroupEntry(
            //label: "Datos del anuncio",
            isSubgroup: true,
            children: [
              SelectContactFieldEntry(
                getValue: () => contact,
                setValue: (ContactDto? selected) {
                  setState(() {
                    this.contact = selected;
                  });
                },
                isRequired: false,
              ),
            ],
          ),
          Divider(height: 40.0, color: Colors.transparent),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Padding(
                padding: EdgeInsets.all(0),
                child: TextButton.icon(
                  icon: Icon(Icons.check),
                  label: Text(localization.common_Continue),
                  onPressed: () {
                    widget.onContinue(
                      contactId: contact?.id.vn,
                    );
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.all(0),
                child: TextButton.icon(
                  icon: Icon(Icons.close),
                  label: Text(localization.common_Cancel),
                  onPressed: () {
                    widget.onCancel();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
