import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/propertyzoneslist/views/embedded_propertyzones.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

Future<PropertyzoneDto?> selectPropertyzoneDialog(context, {Optional<String> initialSelectedId = const None()}) {
  final contentPadding = const EdgeInsets.fromLTRB(12.0, 16.0, 12.0, 24.0);
  final actionsPadding = const EdgeInsets.all(5.0);

  return showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    pageBuilder: (
      BuildContext context,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      return ChangeNotifierProvider<_SelectZoneModel>(
        create: (_) => _SelectZoneModel(),
        child: Consumer<_SelectZoneModel>(builder: (context, selectZoneModel, child) {
          return Padding(
            padding: contentPadding,
            child: Material(
              child: IntrinsicWidth(
                child: Padding(
                  padding: EdgeInsets.all(5.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Flexible(
                        child: EmbeddedPropertyzonesList(
                          initialSelectedId: initialSelectedId,
                          onSelected: (PropertyzoneDto? zone) {
                            selectZoneModel.changeSelected(zone);
                          },
                        ),
                      ),
                      Padding(
                        padding: actionsPadding,
                        child: OverflowBar(
                          alignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            if (selectZoneModel.selectedZone != null)
                              TextButton(
                                child: Padding(
                                  padding: EdgeInsets.all(5.0),
                                  child: Text("Seleccionar ${selectZoneModel.selectedZone?.name.vn ?? ""}"),
                                ),
                                onPressed: () {
                                  Navigator.pop(context, selectZoneModel.selectedZone);
                                },
                              ),
                            TextButton(
                              child: Padding(
                                padding: EdgeInsets.all(5.0),
                                child: Text("Cancelar"),
                              ),
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      );
    },
  );
}

class _SelectZoneModel extends ChangeNotifier {
  PropertyzoneDto? selectedZone;

  _SelectZoneModel() : super();

  void changeSelected(PropertyzoneDto? zone) {
    selectedZone = zone;
    notifyListeners();
  }
}
