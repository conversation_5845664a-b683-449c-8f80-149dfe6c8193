part of 'propertyzoneslist_bloc.dart';

enum PropertyzoneslistStatus { initial, success, failure }

class PropertyzoneslistState extends Equatable {
  PropertyzoneslistState({
    this.status = PropertyzoneslistStatus.initial,
    this.path = const <PropertyzoneDto>[],
    this.children = const <PropertyzoneDto>[],
    this.hasReachedMax = false,
  });

  // Actual status of the screen
  final PropertyzoneslistStatus status;
  final List<PropertyzoneDto> path;
  // The list of actual fetched zones
  final List<PropertyzoneDto> children;
  // There is no more zones to be fetched
  final bool hasReachedMax;

  PropertyzoneslistState copyWith({
    PropertyzoneslistStatus? status,
    List<PropertyzoneDto>? children,
    List<PropertyzoneDto>? path,
    bool? hasReachedMax,
  }) =>
      PropertyzoneslistState(
        status: status ?? this.status,
        children: children ?? this.children,
        path: path ?? this.path,
        hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      );

  factory PropertyzoneslistState.create({
    PropertyzoneslistStatus status = PropertyzoneslistStatus.initial,
    List<PropertyzoneDto> path = const <PropertyzoneDto>[],
    List<PropertyzoneDto> children = const <PropertyzoneDto>[],
    bool hasReachedMax = false,
  }) =>
      PropertyzoneslistState(
        status: status,
        children: children,
        hasReachedMax: hasReachedMax,
      );

  @override
  List<Object> get props => [status, hasReachedMax, children];
}

class PropertyzoneslistLoadFailure extends PropertyzoneslistState {}
