import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:provider/provider.dart';
part 'propertyzoneslist_event.dart';
part 'propertyzoneslist_state.dart';

///
/// Maneja los estados de un árbol de zonas tal que
/// - Tiene un "path", es decir: la lista de zonas hasta alcanzar el nivel actual.  Si está vacía [] es que estamos en el nivel raíz.
/// - Tiene la lista de zonas del nivel seleccionado.
///   - Esta lista puede cargarse de forma incremental (Eventos [PropertyzoneslistOnFetch]).
/// - Se puede cambiar de nodo (evento [PropertyzoneslistOnExpand] ) pero el nodeo "expandido" debe ser:
///   - Uno de los presentes en el path.
///   - Vac<PERSON> (nivel raíz)
///   - Uno de los presentes en la lista de zonas actual.
/// - Puede recargarse nuevamente la lista de zonas y el path  [PropertyzoneslistOnRefresh]
///

///
///
class PropertyzoneslistBloc extends Bloc<PropertyzoneslistEvent, PropertyzoneslistState> {
  static const _PAGE_SIZE = 300;
  final AppModelsNS appModels;

  PropertyzoneslistBloc({required ModelsChannelBloc modelsChannel, required this.appModels})
      : super(PropertyzoneslistState());

  factory PropertyzoneslistBloc.create(BuildContext context) {
    return PropertyzoneslistBloc(
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      appModels: Provider.of<AppModelsNS>(context, listen: false),
    );
  }

  @override
  Stream<Transition<PropertyzoneslistEvent, PropertyzoneslistState>> transformEvents(
    Stream<PropertyzoneslistEvent> events,
    TransitionFunction<PropertyzoneslistEvent, PropertyzoneslistState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<PropertyzoneslistState> mapEventToState(PropertyzoneslistEvent event) async* {
    if (event is PropertyzoneslistOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is PropertyzoneslistOnFetch) {
      yield* _mapOnFetch(state, event);
    } else if (event is PropertyzoneslistOnExpand) {
      yield* _mapOnExpand(state, event);
    }
  }

  Stream<PropertyzoneslistState> _mapOnRefresh(PropertyzoneslistState state, PropertyzoneslistOnRefresh event) async* {
    try {
      final propertyzones =
          state.path.isEmpty ? await _listPropertyzones() : await _listPropertyzones(parentId: state.path.last.id);

      yield state.copyWith(
        status: PropertyzoneslistStatus.success,
        children: propertyzones,
        hasReachedMax: propertyzones.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: PropertyzoneslistStatus.failure);
    }
  }

  Stream<PropertyzoneslistState> _mapOnFetch(PropertyzoneslistState state, PropertyzoneslistOnFetch event) async* {
    if (state.hasReachedMax) {
      yield state;
    } else {
      try {
        if (state.status == PropertyzoneslistStatus.initial) {
          final propertyzones =
              state.path.isEmpty ? await _listPropertyzones() : await _listPropertyzones(parentId: state.path.last.id);
          yield state.copyWith(
            status: PropertyzoneslistStatus.success,
            children: propertyzones,
            hasReachedMax: propertyzones.length != _PAGE_SIZE,
          );
        } else {
          // Este punto sirve tanto en estado success como failure si se reintentase
          final propertyzones = state.path.isEmpty
              ? await _listPropertyzones(offset: state.children.length)
              : await _listPropertyzones(parentId: state.path.last.id, offset: state.children.length);
          yield propertyzones.isEmpty
              ? state.copyWith(
                  hasReachedMax: true,
                )
              : state.copyWith(
                  status: PropertyzoneslistStatus.success,
                  children: List.of(state.children)..addAll(propertyzones),
                  hasReachedMax: false,
                );
        }
      } on Exception {
        yield state.copyWith(status: PropertyzoneslistStatus.failure);
      }
    }
  }

  Stream<PropertyzoneslistState> _mapOnExpand(PropertyzoneslistState state, PropertyzoneslistOnExpand event) async* {
    try {
      if (event.zoneId.isEmpty) {
        // No se ha clicado ninguna zona: Equivale a haber clicado sobre la raíz absoluta (null) del árbol
        final propertyzones = await _listPropertyzones();
        yield state.copyWith(
          status: PropertyzoneslistStatus.success,
          children: propertyzones,
          path: const [],
          hasReachedMax: propertyzones.length != _PAGE_SIZE,
        );
      } else {
        final children = await _listPropertyzones(parentId: event.zoneId);
        final path = await _getPropertyzonePath(event.zoneId);
        yield state.copyWith(
          status: PropertyzoneslistStatus.success,
          children: children,
          path: path,
          hasReachedMax: children.length != _PAGE_SIZE,
        );
      }
    } on Exception {
      yield state.copyWith(status: PropertyzoneslistStatus.failure);
    }
  }

  Future<List<PropertyzoneDto>> _listPropertyzones({int offset = 0, Optional<String> parentId = const None()}) {
    return appModels.listPropertyzones(
      filter: PropertyzonesListFilter(parentId: parentId),
      offset: offset,
      limit: _PAGE_SIZE,
    );
  }

  Future<List<PropertyzoneDto>> _getPropertyzonePath([Optional<String> zoneId = const None()]) {
    return appModels.getPropertyzonePath(zoneId.vn);
  }

  static DateTime endOfToday() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 23, 59, 59, 999, 999);
  }

  static DateTime startOfToday() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }
}
