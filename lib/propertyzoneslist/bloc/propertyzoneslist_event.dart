part of 'propertyzoneslist_bloc.dart';

abstract class PropertyzoneslistEvent extends Equatable {
  const PropertyzoneslistEvent();

  @override
  List<Object> get props => [];
}

class PropertyzoneslistOnRefresh extends PropertyzoneslistEvent {}

class PropertyzoneslistOnExpand extends PropertyzoneslistEvent {
  final Optional<String> zoneId;
  PropertyzoneslistOnExpand({this.zoneId = const None()}) : super();
}

class PropertyzoneslistOnFetch extends PropertyzoneslistEvent {}
