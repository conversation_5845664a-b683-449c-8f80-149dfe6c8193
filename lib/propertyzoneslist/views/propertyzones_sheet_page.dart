import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/propertyzoneslist/views/embedded_propertyzones.dart';
import 'package:flutter/material.dart';

class PropertyzonesSheetPage extends StatefulWidget {
  final Optional<String> initalZoneId;

  PropertyzonesSheetPage({
    this.initalZoneId = const None(),
  }) : super();

  @override
  _PropertyzonesSheetPage createState() => _PropertyzonesSheetPage();
}

class _PropertyzonesSheetPage extends State<PropertyzonesSheetPage> {
  @override
  Widget build(BuildContext context) {
    final t = Theme.of(context).textTheme;
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(2, 5, 2, 2),
            child: Wrap(
              children: [Text("Seleccionae una zona", style: t.headline5)],
            ),
          ),
          Divider(
            height: 8,
          ),
          Expanded(
            child: EmbeddedPropertyzonesList(
              initialSelectedId: widget.initalZoneId,
            ),
          )
        ],
      ),
    );
  }
}
