import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/propertyzoneslist/bloc/propertyzoneslist_bloc.dart';
import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

class EmbeddedPropertyzonesList extends StatefulWidget {
  final Optional<String> initialSelectedId;
  final Function(PropertyzoneDto?)? onLeafSelected;
  final Function(PropertyzoneDto?)? onSelected;

  EmbeddedPropertyzonesList({
    this.initialSelectedId = const None(),
    this.onLeafSelected,
    this.onSelected,
  }) : super();

  final int Function(int) x = (n) => n * n;
  @override
  _EmbeddedZonesList createState() => _EmbeddedZonesList();
}

class _EmbeddedZonesList extends State<EmbeddedPropertyzonesList> {
  final _scrollController = ScrollController();
  late PropertyzoneslistBloc _propertyzonesBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<PropertyzoneslistBloc>(
      create: (context) {
        _propertyzonesBloc = PropertyzoneslistBloc.create(context)
          ..add(PropertyzoneslistOnExpand(zoneId: widget.initialSelectedId));
        return _propertyzonesBloc;
      },
      child: BlocConsumer<PropertyzoneslistBloc, PropertyzoneslistState>(
        listener: (context, state) {
          if (!state.hasReachedMax && _isBottom) {
            _propertyzonesBloc.add(PropertyzoneslistOnFetch());
          }
        },
        builder: (context, state) {
          switch (state.status) {
            case PropertyzoneslistStatus.failure:
              return const Center(child: Text('Problemas obteniendo zonas'));
            case PropertyzoneslistStatus.success:
              return state.path.isEmpty && state.children.isEmpty
                  ? Center(
                      child: Column(mainAxisSize: MainAxisSize.min, children: [
                        Text(
                          'No hay zonas',
                          style: TextStyle(color: Colors.grey),
                        )
                      ]),
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPath(state.path),
                        Divider(thickness: 2, color: Colors.grey),
                        Expanded(
                          child: RefreshIndicator(
                            onRefresh: () async {
                              _propertyzonesBloc.add(PropertyzoneslistOnRefresh());
                            },
                            child: ListView.builder(
                              //key: AgentorKeys.actionsList,
                              physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                              itemBuilder: (BuildContext context, int index) {
                                final hasBackButton = state.path.length != 0;
                                if (hasBackButton && index == 0) {
                                  // Añadir un item ficticio "atrás"
                                  return _buildBackItem(state.path);
                                } else {
                                  final firstItemIndex = hasBackButton ? 1 : 0;
                                  if (index - firstItemIndex >= state.children.length) {
                                    return BottomLoader();
                                  } else {
                                    return _buildListItem(state.children[index - firstItemIndex]);
                                  }
                                }
                              },
                              itemCount: (state.path.length > 0 ? 1 : 0) +
                                  (state.hasReachedMax ? state.children.length : state.children.length + 1),
                            ),
                          ),
                        ),
                      ],
                    );

            default:
              return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  Widget _buildPath(List<PropertyzoneDto> path) {
    return Padding(
      padding: EdgeInsets.all(2),
      child: Wrap(
        direction: Axis.horizontal,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        children: [
          InkWell(
            child: Icon(Icons.home),
            onTap: () {
              _doZoneClicked(null);
            },
          ),
          if (path.length > 2) _buildPathItem(path[path.length - 3], hideName: true),
          if (path.length > 1) _buildPathItem(path[path.length - 2]),
          if (path.length > 0) _buildPathItem(path[path.length - 1]),
        ],
      ),
    );
  }

  Widget _buildPathItem(PropertyzoneDto pathItem, {bool hideName = false}) {
    return InkWell(
      child: Wrap(
          direction: Axis.horizontal,
          alignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            Icon(Icons.keyboard_arrow_right),
            if (hideName) Text("...") else Text(pathItem.name.vn ?? ""),
          ]),
      onTap: () {
        _doZoneClicked(pathItem);
      },
    );
  }

  Widget _buildListItem(PropertyzoneDto zone) {
    return ListTile(
      onTap: () {
        _doZoneClicked(zone);
      },
      leading: (zone.hasChildren.vn ?? false) ? Icon(Icons.keyboard_arrow_right) : Icon(Icons.circle),
      title: Text(zone.name.v),
      dense: true,
    );
  }

  Widget _buildBackItem(List<PropertyzoneDto> path) {
    return ListTile(
      onTap: () {
        _doZoneClicked(path.length > 1 ? path[path.length - 2] : null);
      },
      leading: Icon(Icons.keyboard_arrow_left),
      title: Text("... (atrás)", style: TextStyle(fontStyle: FontStyle.italic)),
      dense: true,
    );
  }

  void _doZoneClicked(PropertyzoneDto? zone) {
    if (zone != null) {
      _propertyzonesBloc.add(PropertyzoneslistOnExpand(zoneId: zone.id));
      if (widget.onLeafSelected != null && (zone.hasChildren.vn ?? false)) {
        widget.onLeafSelected!(zone);
      }
      if (widget.onSelected != null) {
        widget.onSelected!(zone);
      }
    } else {
      _propertyzonesBloc.add(PropertyzoneslistOnExpand());
      if (widget.onSelected != null) {
        widget.onSelected!(null);
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _propertyzonesBloc.add(PropertyzoneslistOnRefresh());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
