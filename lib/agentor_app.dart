import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/generated/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/action_edit/models/action_edit_page_params.dart';
import 'package:topbrokers/action_edit/views/views.dart';
import 'package:topbrokers/actionslist/bloc/actionslist_bloc.dart';
import 'package:topbrokers/agent_edit/views/views.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/channels/channels.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/cloudoffers_list/cloudoffers.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/common/localizations_helper.dart';
import 'package:topbrokers/contact_edit/contact_edit.dart';
import 'package:topbrokers/contact_sheet/views/contact_sheet_page.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/demand_edit/models/demand_edit_page_params.dart';
import 'package:topbrokers/demand_edit/views/demand_edit_page.dart';
import 'package:topbrokers/demand_sheet/views/demand_sheet_page.dart';
import 'package:topbrokers/demands_list/bloc/demands_list_bloc.dart';
import 'package:topbrokers/deposit_create/widgets/deposit_create_page.dart';
import 'package:topbrokers/ecommerce/purchases_list/views/purchases_page.dart';
import 'package:topbrokers/ecommerce/purchases_list/views/views.dart';
import 'package:topbrokers/fav_cloudoffers_list/bloc/fav_cloudoffers_list_bloc.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/home/<USER>';
import 'package:topbrokers/landing/views/landing_page.dart';
import 'package:topbrokers/login_form/views/login_page.dart';
import 'package:topbrokers/offer_edit/models/offer_edit_page_params.dart';
import 'package:topbrokers/offer_edit/views/offer_edit_page.dart';
import 'package:topbrokers/offer_sheet/views/offer_sheet_page.dart';
import 'package:topbrokers/offers_list/bloc/offers_list_bloc.dart';
import 'package:topbrokers/propertyzoneslist/views/propertyzones_sheet_page.dart';
import 'package:topbrokers/recoverpass_form/views/recoverpass_page.dart';
import 'package:topbrokers/routes.dart';
import 'package:topbrokers/scroll_behaviour.dart';
import 'package:topbrokers/serviceaction_new/models/serviceaction_new_page_params.dart';
import 'package:topbrokers/serviceaction_new/views/serviceaction_new_page.dart';
import 'package:topbrokers/serviceaction_sheet/models/serviceaction_sheet_page_params.dart';
import 'package:topbrokers/serviceaction_sheet/serviceaction_sheet.dart';
import 'package:topbrokers/signup_form/views/signup_page.dart';
import 'package:topbrokers/support_request/view/support_request_page.dart';
import 'package:topbrokers/theme.dart';

class TopbrokersApp extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _TopbrokersApp();
}

class _TopbrokersApp extends State<TopbrokersApp> with WidgetsBindingObserver {
  final api = Deps.solve<ApiServices>();

  @override
  didChangeLocales(locales) {
    super.didChangeLocales(locales);

    if (locales != null && locales.length != 0)
      setState(() {
        Intl.systemLocale = locales[0].toString();
        Intl.defaultLocale = locales[0].toString();
      });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    Intl.defaultLocale = WidgetsBinding.instance.platformDispatcher.locale.toString(); // await findSystemLocale(); //Intl.canonicalizedLocale(locale);
  }

  @override
  void dispose() {
    try {
      WidgetsBinding.instance.removeObserver(this);
    } finally {
      super.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ModelsChannelBloc>(
      create: (_) => ModelsChannelBloc(),
      child: Provider<AppModelsNS>(
        create: (context) => AppModelsNS.create(context: context),
        child: BlocProvider<SessionBloc>(
          create: (context) => SessionBloc.create(context: context)..add(SessionOnInit()),
          child: BlocConsumer<SessionBloc, SessionState>(
            listener: (context, state) {},
            buildWhen: (previous, next) => previous.status != next.status || previous.userToken != next.userToken,
            builder: (context, state) {
              switch (state.status) {
                case SessionStatus.failure:
                  return _scafoldApp(
                    context,
                    home: Scaffold(
                      body: Center(
                        child: Column(
                          children: [
                            Text(
                              context.apploc.app_err_loading,
                            ), //AppLocalizations.of(context).app_err_loading),
                            Divider(height: 42),
                            ElevatedButton(
                              child: Text(context.apploc.common_try_again), //AppLocalizations.of(context).common_try_again),
                              onPressed: () {
                                BlocProvider.of<SessionBloc>(context).add(SessionOnInit());
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  );

                case SessionStatus.notAthentified:
                  // Aquí montar pantalla de login.
                  return _scafoldApp(context, home: LandingPage(), routes: {
                    AgentorRoutes.signIn: _signInWidget,
                    AgentorRoutes.signUp: _signUpWidget,
                    AgentorRoutes.recoverPass: _recoverPassWidget,
                  });

                case SessionStatus.authentified:
                  return MultiBlocProvider(
                    providers: [
                      //BlocProvider<PurchasesBloc>(
                      //  create: (BuildContext context) => PurchasesBloc.create(context)..add(PurchaseslistOnRefresh()),
                      //),
                      BlocProvider<CloudoffersListBloc>(
                        create: (context) => CloudoffersListBloc.create(
                          context,
                          filter: CloudoffersListFilter(
                            saleAllowed: True,
                            rentAllowed: False,
                          ),
                        )..add(CloudoffersListOnFetch()),
                      ),
                      BlocProvider<FavCloudoffersListBloc>(
                        create: (context) => FavCloudoffersListBloc.create(
                          context,
                        )..add(FavCloudoffersListOnFetch()),
                      ),
                      BlocProvider<ContactsBloc>(
                        create: (context) => ContactsBloc.create(context)..add(ContactslistOnFetch()),
                      ),
                      BlocProvider<DemandsListBloc>(
                        create: (context) => DemandsListBloc.create(context)..add(DemandsListOnFetch()),
                      ),
                      BlocProvider<OffersListBloc>(
                        create: (context) => OffersListBloc.create(context)..add(OffersListOnFetch()),
                      ),
                      /*BlocProvider<OpportunitiesListBloc>(
                        create: (context) => OpportunitiesListBloc.create(context)..add(OpportunitiesListOnFetch()),
                      ),*/
                      BlocProvider<ActionslistBloc>(
                        create: (context) => ActionslistBloc.create(context)..add(ActionslistOnFetch()),
                      ),
                    ],
                    child: _scafoldApp(context, routes: {
                      AgentorRoutes.home: (context) => HomePage(),
                      AgentorRoutes.editAgent: _editAgentWidget,
                      AgentorRoutes.addDemand: _addDemandWidget,
                      AgentorRoutes.showDemand: _showDemandWidget,
                      AgentorRoutes.editDemand: _editDemandWidget,
                      AgentorRoutes.addOffer: _addOfferWidget,
                      AgentorRoutes.addContact: _addContactWidget,
                      AgentorRoutes.addAction: _addActionWidget,
                      AgentorRoutes.editAction: _editActionWidget,
                      AgentorRoutes.showServiceaction: _showServiceactionWidget,
                      AgentorRoutes.newServiceaction: _newServiceactionWidget,
                      AgentorRoutes.editOffer: _editOfferWidget,
                      AgentorRoutes.showOffer: _showOfferWidget,
                      AgentorRoutes.editContact: _editContactWidget,
                      AgentorRoutes.showContact: _showContactWidget,
                      AgentorRoutes.zoneSelector: _navigateZonesWidget,
                      AgentorRoutes.addDeposit: _addDepositWidget,
                      AgentorRoutes.ecomPurchases: _showPurchasesWidget,
                      AgentorRoutes.supportRequest: _requestSupportWidget,
                    }),
                  );

                default:
                  return const Center(child: CircularProgressIndicator());
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _scafoldApp(
    BuildContext context, {
    Widget? home,
    Map<String, WidgetBuilder> routes = const <String, WidgetBuilder>{},
  }) {
    return MaterialApp(
      title: "Percent", //AppLocalizations.of(context).app_title,
      theme: AgentorTheme.theme,
      scrollBehavior: AgentorScrollBehavior(),
      debugShowCheckedModeBanner: false,
      home: home,
      routes: routes,
      localizationsDelegates: [
        // ... app-specific localization delegate[s] here
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: [
        const Locale('en', "EN"),
        const Locale('es', 'ES'),

        // ... other locales the app supports
      ],
    );
  }

  Widget _signUpWidget(BuildContext context) {
    return SignupPage();
  }

  Widget _signInWidget(BuildContext context) {
    return LoginPage();
  }

  Widget _recoverPassWidget(BuildContext context) {
    return RecoverpassPage();
  }

  Widget _addOfferWidget(BuildContext context) {
    final params = (ModalRoute.of(context)?.settings.arguments as NewOfferEditParams?) ?? NewOfferEditParams();
    return OfferEditPage(params: params);
  }

  Widget _editOfferWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as ExistingOfferEditParams?;
    if (params != null)
      return OfferEditPage(params: params);
    else
      return BackToHomeWidget();
  }

  Widget _showOfferWidget(BuildContext context) {
    final offerId = (ModalRoute.of(context)?.settings.arguments as String?);
    if (offerId != null)
      return OfferSheetPage(key: AgentorKeys.showOfferPage, offerId: offerId);
    else
      return BackToHomeWidget();
  }

  Widget _showDemandWidget(BuildContext context) {
    final demandId = ModalRoute.of(context)?.settings.arguments as String?;
    if (demandId != null)
      return DemandSheetPage(key: AgentorKeys.showDemandPage, demandId: demandId);
    else
      return BackToHomeWidget();
  }

  Widget _addContactWidget(BuildContext context) {
    return ContactEditPage(key: AgentorKeys.editContactPage);
  }

  Widget _addActionWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as NewActionEditParams? ?? NewActionEditParams();
    return ActionEditPage(params: params);
  }

  Widget _newServiceactionWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as NewServiceactionParams? ?? NewServiceactionParams();
    return NewServiceactionPage(params: params);
  }

  Widget _editActionWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as ExistingActionEditParams?;
    if (params != null)
      return ActionEditPage(params: params);
    else
      return BackToHomeWidget();
  }

  Widget _showServiceactionWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as ServiceactionSheetPageParams?;
    if (params != null)
      return ServiceactionSheetPage(params: params);
    else
      return BackToHomeWidget();
  }

  Widget _editContactWidget(BuildContext context) {
    final contactId = ModalRoute.of(context)?.settings.arguments as String?;
    if (contactId != null)
      return ContactEditPage(key: AgentorKeys.editContactPage, contactId: contactId);
    else
      return BackToHomeWidget();
  }

  Widget _editAgentWidget(BuildContext context) {
    final agentId = ModalRoute.of(context)?.settings.arguments as String?;
    if (agentId != null)
      return AgentEditPage(key: AgentorKeys.editAgentPage, agentId: agentId);
    else
      return BackToHomeWidget();
  }

  Widget _showContactWidget(BuildContext context) {
    final contactId = ModalRoute.of(context)?.settings.arguments as String?;
    if (contactId != null)
      return ContactSheetPage(key: AgentorKeys.showContactPage, contactId: contactId);
    else
      return BackToHomeWidget();
  }

  Widget _navigateZonesWidget(BuildContext context) {
    final zoneId = ModalRoute.of(context)?.settings.arguments as String?;
    if (zoneId != null)
      return PropertyzonesSheetPage(initalZoneId: Some(zoneId));
    else
      return PropertyzonesSheetPage(initalZoneId: None());
  }

  Widget _addDemandWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as NewDemandEditParams? ?? NewDemandEditParams();
    return DemandEditPage(params: params);
  }

  Widget _editDemandWidget(BuildContext context) {
    final params = ModalRoute.of(context)?.settings.arguments as ExistingDemandEditParams?;
    if (params != null)
      return DemandEditPage(params: params);
    else
      return BackToHomeWidget();
  }

  Widget _addDepositWidget(BuildContext context) {
    return DepositCreatePage();
  }

  Widget _showPurchasesWidget(BuildContext context) {
    return PurchasesPage();
  }

  Widget _requestSupportWidget(BuildContext context) {
    return SupportRequestPage();
  }
}

///
/// Las URLs de enrutamiento no incluyen parámetros (éstos van "a parte"), por lo que al refrescar una página nos encontramos
/// que los datos necesarios (ej: el identificador de una oferta) no existen y no podemos cargar el widget deseado.
/// Para estos casos, lo que hacemos es cargar un widget "vacío" y, seguídamente, navegar a la página "home"
///
class BackToHomeWidget extends StatefulWidget {
  @override
  _BackToHomeWidgetState createState() => _BackToHomeWidgetState();
}

class _BackToHomeWidgetState extends State<BackToHomeWidget> {
  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Navigator.of(context).pushNamedAndRemoveUntil(AgentorRoutes.home, (route) => false);
    });

    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}
