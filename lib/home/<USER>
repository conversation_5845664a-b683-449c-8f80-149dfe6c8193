import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/routes.dart';
import 'package:url_launcher/url_launcher.dart';

class LateralMenu extends StatelessWidget {
  final String helpNumber = '34644828760';

  void launchWhatsApp() async {
    final whatsUrl = Uri.parse('https://wa.me/$helpNumber');
    if (await canLaunchUrl(whatsUrl)) {
      await launchUrl(whatsUrl);
    } else {
      throw 'No se pudo contactar con $helpNumber automáticamente... utilice su whatsapp de forma manual';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SessionBloc, SessionState>(
      listener: (context, state) {},
      buildWhen: (previous, next) => true, //previous.status != next.status || previous.userToken != next.userToken,
      builder: (context, state) {
        if (state.status == SessionStatus.authentified) {
          return Drawer(
            child: ListView(children: [
              ListTile(
                title: Text(state.agent.vn?.name.vn ?? ""),
                subtitle: Text(state.agent.vn?.email.vn ?? ""),
                trailing: TextButton.icon(
                  icon: Icon(Icons.edit),
                  label: Text(""),
                  onPressed: () {
                    Navigator.pushNamed(context, AgentorRoutes.editAgent, arguments: state.agent.vn?.id.vn);
                  },
                ),
              ),
              ListTile(
                title: Text("Crédito disponible: ${state.ecomAccount.vn?.balance.vn ?? 0} €"),
                trailing: TextButton.icon(
                  icon: Icon(Icons.add_box),
                  label: Text(""),
                  onPressed: () {
                    Navigator.pushNamed(context, AgentorRoutes.addDeposit);
                  },
                ),
              ),
              if (!state.imIndividual)
                ListTile(
                  title: Text("Mis compras"),
                  trailing: TextButton.icon(
                    icon: Icon(Icons.sell),
                    label: Text(""),
                    onPressed: () {
                      Navigator.pushNamed(context, AgentorRoutes.ecomPurchases);
                    },
                  ),
                ),
              /*
              ListTile(
                title: TextButton(
                  child: Text("Ayuda"),
                  onPressed: () async {
                    final lnk = "${Deps.solve<AppConfig>().webserverUrl}tb_help.pdf";
                    if (await canLaunch(lnk)) {
                      await launch(lnk);
                    }
                  },
                ),
              ),
              ListTile(
                title: TextButton(
                  child: Text("Soporte"),
                  onPressed: () {
                    Navigator.pushNamed(context, AgentorRoutes.supportRequest);
                  },
                ),
              ),
              */
              /*ListTile(
                title: TextButton(
                  child: Text("Ayuda"),
                  onPressed: () async {
                    final lnk = "https://wa.me/message/KIP7CVK5SRQZN1";
                    if (await canLaunchUrlString(lnk)) {
                      await launchUrlString(lnk);
                    }
                  },
                ),
              ),*/
              ListTile(
                title: TextButton(
                  child: Text("Ayuda"),
                  onPressed: () async {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text('Información'),
                          content: Text('Puede usted contactar por WhatsApp con el teléfono 34644828760.\nPor favor, coméntenos en detalle por que motivo se comunica con el canal \nde soporte de Topbrokers. Le responderemos en breve. ¡Muchas gracias!.'),
                          actions: <Widget>[
                            TextButton(
                              onPressed: () {
                                launchWhatsApp();
                              },
                              child: Text('Contactar'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text('Cerrar'),
                            ),
                          ],
                        );
                      }    
                    );
                  },
                ),
              ),
              //if (!state.imIndividual)
              // ListTile(
              //   title: TextButton(
              //     child: Text("Recursos Partners"),
              //     onPressed: () async {
              //       final lnk = "https://linktr.ee/topbrokers.io";
              //       if (await canLaunchUrlString(lnk)) {
              //         await launchUrlString(lnk);
              //       }
              //     },
              //   ),
              // ),
              ListTile(
                dense: true,
                visualDensity: VisualDensity.compact,
                title: ElevatedButton.icon(
                  icon: Icon(Icons.logout, size: 14),
                  label: Text("Cerrar sesión"),
                  onPressed: () {
                    BlocProvider.of<SessionBloc>(context).add(SessionOnLoggedOut());
                  },
                ),
              ),
            ]),
          );
        } else {
          return Text("No autenticado");
        }
      },
    );
  }
}
