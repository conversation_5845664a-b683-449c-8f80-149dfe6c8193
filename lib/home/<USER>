import 'package:fab_circular_menu/fab_circular_menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:topbrokers/actionslist/views/actionslist.dart';
import 'package:topbrokers/actionslist/widgets/actions_filter_button.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/cloudoffers_list/cloudoffers.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contact_edit/models/contact_edit_page_result.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/contacts_list/widgets/contacts_filter_button.dart';
import 'package:topbrokers/contacts_list/widgets/contacts_options_button.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/demand_edit/models/demand_edit_page_result.dart';
import 'package:topbrokers/demands_list/widgets/demands_filter_button.dart';
import 'package:topbrokers/fav_cloudoffers_list/views/views.dart';
import 'package:topbrokers/home/<USER>';
import 'package:topbrokers/demands_list/demands_list.dart';
import 'package:topbrokers/offer_edit/models/offer_edit_page_result.dart';
import 'package:topbrokers/offers_list/offers.dart';
import 'package:topbrokers/routes.dart';
import 'package:topbrokers/tabs/tabs.dart';
import 'package:topbrokers/global/session.dart';

class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key ?? AgentorKeys.homePage);

  @override
  Widget build(BuildContext context) {
    final GlobalKey<FabCircularMenuState> fabKey = GlobalKey();
    final localizations = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return BlocProvider<TabBloc>(
      create: (context) => TabBloc(),
      child: BlocBuilder<TabBloc, AppTab>(
        builder: (context, activeTab) {
          return Scaffold(
            appBar: AppBar(
              title: Image(
                image: AssetImage('images/topbrokers_io_white_174x22.png'),
                //fit: BoxFit.contain,
                alignment: FractionalOffset.centerLeft,
                height: 22,
                width: 174,
                //width: CONTENT_Width, //double.infinity,
                filterQuality: FilterQuality.high,
              ),
              centerTitle: true,
              actions: [
                if (activeTab == AppTab.demands) DemandsFilterButton(visible: true),
                if (activeTab == AppTab.offers) OffersOptionsButton(visible: true),
                if (activeTab == AppTab.offers && !context.imIndividual()) OffersFilterButton(visible: true),
                if (activeTab == AppTab.contacts && !context.imIndividual()) ContactsFilterButton(visible: true),
                if (activeTab == AppTab.actions) ActionsFilterButton(visible: true),
                if (activeTab == AppTab.cloud) CloudofferListOrderButton(visible: true),
                if (activeTab == AppTab.cloud) CloudofferListMainmenuButton(visible: true),
                //if (activeTab == AppTab.cloud) CloudofferListExportButton(visible: true),
              ],
            ),
            drawer: LateralMenu(),
            body: WidthLimiter(
              child: activeTab == AppTab.cloud
                  ? CloudoffersList()
                  : activeTab == AppTab.favourites
                      ? FavCloudoffersList()
                      : activeTab == AppTab.demands
                          ? DemandsList()
                          : activeTab == AppTab.offers
                              ? OffersList()
                              : activeTab == AppTab.contacts
                                  ? ContactsList()
                                  : ActionsList(),
            ),
            floatingActionButton: FabCircularMenu(
              key: fabKey,
              fabSize: 58,
              fabOpenIcon: Icon(
                Icons.add,
                color: theme.floatingActionButtonTheme.foregroundColor,
              ),
              fabCloseIcon: Icon(
                Icons.close,
                color: theme.floatingActionButtonTheme.foregroundColor,
              ),
              fabOpenColor: theme.floatingActionButtonTheme.backgroundColor,
              fabCloseColor: theme.floatingActionButtonTheme.backgroundColor,
              fabColor: theme.floatingActionButtonTheme.foregroundColor,
              ringWidth: 96,
              ringDiameter: 400,
              ringColor: theme.floatingActionButtonTheme.backgroundColor,
              children: <Widget>[
                IconButton(
                  icon: Icon(CustomAppIcons.offer, color: Colors.white),
                  iconSize: 32,
                  tooltip: localizations?.offer_titleNew,
                  onPressed: () async {
                    fabKey.currentState?.close();
                    final result = await Navigator.pushNamed(context, AgentorRoutes.addOffer);
                    if (result != null && result is OfferEditPageResult && result.id.vn != null) {
                      Navigator.pushNamed(context, AgentorRoutes.showOffer, arguments: result.id.v);
                    }
                  },
                ),
                IconButton(
                  icon: Icon(
                    CustomAppIcons.demand,
                    size: 32,
                    color: Colors.white,
                  ),
                  iconSize: 32,
                  tooltip: localizations?.demand_titleNew,
                  onPressed: () async {
                    fabKey.currentState?.close();
                    final result = await Navigator.pushNamed(context, AgentorRoutes.addDemand);
                    if (result is DemandEditPageResult && result.id.vn != null) {
                      Navigator.pushNamed(context, AgentorRoutes.showDemand, arguments: result.id.v);
                    }
                  },
                ),
                IconButton(
                  icon: Icon(Icons.person /* CustomAppIcons.contact */, color: Colors.white),
                  iconSize: 32,
                  tooltip: localizations?.contact_titleNew,
                  onPressed: () async {
                    fabKey.currentState?.close();
                    //Navigator.pushNamed(context, AgentorRoutes.addContact);
                    final result = await Navigator.pushNamed(context, AgentorRoutes.addContact);
                    if (result is ContactEditPageResult && result.id != null) {
                      Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: result.id);
                    }
                  },
                ),
                IconButton(
                  icon: Icon(
                    CustomAppIcons.action,
                    color: Colors.white,
                  ),
                  iconSize: 32,
                  tooltip: localizations?.action_titleNew,
                  onPressed: () {
                    fabKey.currentState?.close();
                    Navigator.pushNamed(context, AgentorRoutes.addAction);
                  },
                ),
                IconButton(
                  icon: Icon(Icons.auto_fix_high, color: Colors.white),
                  iconSize: 32,
                  tooltip: "Superpoderes",
                  onPressed: () {
                    fabKey.currentState?.close();
                    Navigator.pushNamed(context, AgentorRoutes.newServiceaction);
                  },
                ),
              ],
            ),
            bottomNavigationBar: TabSelector(
              activeTab: activeTab,
              onTabSelected: (tab) => BlocProvider.of<TabBloc>(context).add(TabUpdated(tab)),
            ),
          );
        },
      ),
    );
  }
}
