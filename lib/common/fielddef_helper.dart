import 'dart:core';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/common/datetime_helper.dart';
import 'package:url_launcher/url_launcher_string.dart';

import 'string_helper.dart';

extension FieldDefinitionHelper on FieldDefDto {
  static const List<String> _anStringList = const [];
  static const double _aDouble = 0;
  static final DateTime _aDateTime = DateTime(1971);
  static const String _anString = "";
  static const int _anInt = 0;
  static const bool _aBool = false;

  Iterable<TextSpan> buildCustomEntrySpan<T>(BuildContext context, {required String? Function() getStringValue}) {
    //if (this == null) return [];
    final fieldDef = this;
    final textTheme = Theme.of(context).textTheme;
    final lbStyle = textTheme.bodyText2;
    final txtStyle = textTheme.bodyText1;
    final label = TextSpan(text: "${fieldDef.label?.localized}: ".capitalize, style: lbStyle);
    final value = getStringValue();
    if (value != null && value.length != 0) {
      return [label, TextSpan(text: "$value", style: txtStyle)];
    } else {
      return [];
    }
  }

  Iterable<TextSpan> buildEntrySpans<T>(BuildContext context,
      {required T? Function() getValue, bool includeLabel = true}) {
    final fieldDef = this;
    final textTheme = Theme.of(context).textTheme;
    final lbStyle = textTheme.bodyText2;
    final txtStyle = textTheme.bodyText1;
    final sufixStyle = textTheme.bodyText1;
    final label = TextSpan(text: "${fieldDef.label?.localized}: ".capitalize, style: lbStyle);

    if (_aDouble is T && fieldDef.type == FieldDefType.m2) {
      final value = getValue() as double?;
      if (value != null && value > 0) {
        return [
          if (includeLabel) label,
          TextSpan(text: "$value", style: txtStyle),
          TextSpan(text: "m²", style: sufixStyle)
        ];
      } else {
        return [];
      }
    } else if (_aDouble is T && fieldDef.type == FieldDefType.currency) {
      final value = getValue() as double?;
      if (value != null) {
        return [
          if (includeLabel) label,
          TextSpan(text: "$value", style: txtStyle),
          TextSpan(text: "€", style: sufixStyle)
        ];
      } else {
        return [];
      }
    } else if (_aDouble is T && fieldDef.type == FieldDefType.percent) {
      final value = getValue() as double?;
      if (value != null) {
        return [
          if (includeLabel) label,
          TextSpan(text: "$value", style: txtStyle),
          TextSpan(text: "%", style: sufixStyle)
        ];
      } else {
        return [];
      }
    } else if (_anInt is T && fieldDef.type == FieldDefType.year) {
      final value = getValue() as int?;
      if (value != null) {
        return [
          if (includeLabel) label,
          TextSpan(text: "$value", style: txtStyle),
        ];
      } else {
        return [];
      }
    } else if (_aDateTime is T && fieldDef.type == FieldDefType.date) {
      final value = getValue() as DateTime?;
      if (value != null) {
        return [
          if (includeLabel) label,
          TextSpan(text: value.toLocal().formatRelativeToNow(), style: txtStyle),
        ];
      } else {
        return [];
      }
    } else if (_anInt is T && fieldDef.type == FieldDefType.count) {
      final value = getValue() as int?;
      if (value != null && value > 0) {
        return [
          if (includeLabel) label,
          TextSpan(text: "$value", style: txtStyle),
        ];
      } else {
        return [];
      }
    } else if (_anString is T && fieldDef.type == FieldDefType.code) {
      final value = getValue() as String?;
      final txts = (fieldDef.values ?? [])
          .where((codeValue) => codeValue.code.v == value)
          .map((fDef) => fDef.label.vn?.localized);
      if (txts.length != 0) {
        return [
          if (includeLabel) label,
          TextSpan(text: "${txts.first}", style: txtStyle),
        ];
      } else {
        return [];
      }
    } else if (_anString is T && (fieldDef.type == FieldDefType.text || fieldDef.type == FieldDefType.description)) {
      final value = getValue() as String?;
      if (value != null) {
        return [
          if (includeLabel) label,
          TextSpan(text: "$value", style: txtStyle),
        ];
      } else {
        return [];
      }
    } else if (_anString is T && fieldDef.type == FieldDefType.phonenumber) {
      final value = getValue() as String?;
      if (value != null) {
        return [
          if (includeLabel) label,
          TextSpan(
            text: "$value",
            style: txtStyle == null ? null : txtStyle.copyWith(decoration: TextDecoration.underline),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                final url = "tel:$value";
                if (await canLaunchUrlString(url)) {
                  await launchUrlString(url);
                }
              },
          ),
          /*TextSpan(text: " "),
          TextSpan(
            text: "Whatsapp",
            style: txtStyle == null ? null : txtStyle.copyWith(decoration: TextDecoration.underline),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                final url = "https://api.whatsapp.com/send?phone=${Uri.encodeComponent(value)}";
                if (await canLaunch(url)) {
                  await launch(url);
                }
              },
          )*/
        ];
      } else {
        return [];
      }
    } else if (_anString is T && fieldDef.type == FieldDefType.email) {
      final value = getValue() as String?;
      if (value != null) {
        return [
          label,
          TextSpan(
            text: "$value",
            style: txtStyle == null ? null : txtStyle.copyWith(decoration: TextDecoration.underline),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                final url = "mailto:$value";
                if (await canLaunchUrlString(url)) {
                  await launchUrlString(url);
                }
              },
          )
        ];
      } else {
        return [];
      }
    } else if (_anStringList is T && fieldDef.type == FieldDefType.multicode) {
      final values = (getValue() as List<String>?) ?? <String>[];
      final txts = (fieldDef.values ?? [])
          .where((fdef) => values.contains(fdef.code.v))
          .map((fDef) => fDef.label.vn?.localized ?? "");
      if (txts.length != 0) {
        return [label] +
            txts.fold([], (acum, txt) {
              if (acum.length != 0) {
                acum.add(TextSpan(text: "/", style: txtStyle));
              }
              acum.add(TextSpan(text: "$txt", style: txtStyle));
              return acum;
            });
      } else {
        return [];
      }
    } else if (_aBool is T && fieldDef.type == FieldDefType.yesnot) {
      final value = getValue() as bool?;
      if (value ?? false) {
        return [TextSpan(text: fieldDef.label?.localized.capitalize ?? "", style: lbStyle)];
      } else {
        return [];
      }
    } else if (_anInt is T && fieldDef.type == FieldDefType.rank) {
      final value = getValue() as int?;
      if (value != null) {
        final txts =
            (fieldDef.ranks ?? []).where((rank) => rank.value.v == value).map((rank) => rank.label.v.localized);
        final stars = "".padRight(value, "★");
        if (txts.length != 0) {
          return [
            if (includeLabel) label,
            TextSpan(text: "${txts.first}", style: txtStyle),
            TextSpan(text: "$stars", style: sufixStyle),
          ];
        }
      }
      return [];
    } else {
      throw Exception("Unexpected definition field ${fieldDef.toJson()}");
    }
  }

  FieldEntry<T> buildEntry<T>({
    required T? Function() getValue,
    required void Function(T?) setValue,
    bool isRequired = false,
    String Function()? getValueComment,
  }) {
    final fieldDef = this;

    if (_aDouble is T && fieldDef.type == FieldDefType.m2) {
      return SimpleFieldEntry<double>(
        label: fieldDef.label?.localized,
        getValue: (getValue as double? Function()),
        setValue: setValue as void Function(double?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        isM2: true,
        min: (fieldDef.min ?? 0),
        max: fieldDef.max,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_aDouble is T && fieldDef.type == FieldDefType.currency) {
      return SimpleFieldEntry<double>(
        label: fieldDef.label?.localized,
        getValue: getValue as double? Function(),
        setValue: setValue as void Function(double?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        isCurrency: true,
        min: (fieldDef.min ?? 0),
        max: fieldDef.max,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_aDouble is T && fieldDef.type == FieldDefType.percent) {
      return SimpleFieldEntry<double>(
        label: fieldDef.label?.localized,
        getValue: getValue as double? Function(),
        setValue: setValue as void Function(double?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        isPercent: true,
        min: fieldDef.min ?? 0,
        max: fieldDef.max ?? 100,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_anInt is T && fieldDef.type == FieldDefType.year) {
      return SimpleFieldEntry<int>(
        label: fieldDef.label?.localized,
        getValue: getValue as int? Function(),
        setValue: setValue as Function(int?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        min: 0,
        max: DateTime.now().year + 1,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_aDateTime is T && fieldDef.type == FieldDefType.date) {
      return SimpleFieldEntry<DateTime>(
        label: fieldDef.label?.localized,
        getValue: getValue as DateTime? Function(),
        setValue: setValue as Function(DateTime?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        isDateOnly: true,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_anInt is T && fieldDef.type == FieldDefType.count) {
      return SimpleFieldEntry<int>(
        label: fieldDef.label?.localized,
        getValue: getValue as int? Function(),
        setValue: setValue as Function(int?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        isCount: true,
        min: (fieldDef.min ?? 0).toInt(),
        max: fieldDef.max != null ? fieldDef.max!.toInt() : null,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_anString is T && fieldDef.type == FieldDefType.code) {
      return SelectFieldEntry<String>(
        label: fieldDef.label?.localized,
        options: (fieldDef.values ?? [])
            .map((value) => SelectOption<String>(value: value.code.v, label: value.label.v.localized))
            .toList(),
        getValue: getValue as String? Function(),
        setValue: setValue as Function(String?),
        isRequired: isRequired,
      ) as FieldEntry<T>;
    } else if (_anString is T &&
        (fieldDef.type == FieldDefType.text ||
            fieldDef.type == FieldDefType.email ||
            fieldDef.type == FieldDefType.phonenumber ||
            fieldDef.type == FieldDefType.description)) {
      return SimpleFieldEntry<String>(
        label: fieldDef.label?.localized,
        getValue: getValue as String? Function(),
        setValue: setValue as Function(String?),
        getValueComment: getValueComment,
        isRequired: isRequired,
        isEmail: fieldDef.type == FieldDefType.email,
        isPhoneNumber: fieldDef.type == FieldDefType.phonenumber,
        isMultiline: fieldDef.type == FieldDefType.description,
        readOnly: fieldDef.readOnly ?? false,
      ) as FieldEntry<T>;
    } else if (_anStringList is T && fieldDef.type == FieldDefType.multicode) {
      return MultiSelectFieldEntry<String>(
        label: fieldDef.label?.localized,
        options: (fieldDef.values ?? [])
            .map((value) => SelectOption<String>(value: value.code.v, label: value.label.v.localized))
            .toList(),
        getValue: (getValue as List<String>? Function()),
        setValue: (setValue as void Function(List<String>?)),
        isRequired: isRequired,
      ) as FieldEntry<T>;
    } else if (_aBool is T && fieldDef.type == FieldDefType.yesnot) {
      return SimpleFieldEntry<bool>(
        label: fieldDef.label?.localized,
        getValue: getValue as bool? Function(),
        setValue: setValue as Function(bool?),
      ) as FieldEntry<T>;
    } else if (_anInt is T && fieldDef.type == FieldDefType.rank) {
      return RankFieldEntry<int>(
        label: fieldDef.label?.localized,
        getValue: getValue as int? Function(),
        setValue: setValue as void Function(int?),
        options: (fieldDef.ranks ?? [])
            .map((rank) => SelectOption<int>(value: rank.value.v, label: rank.label.v.localized))
            .toList(),
      ) as FieldEntry<T>;
    } else {
      throw Exception("Unexpected definition field ${fieldDef.toJson()}");
    }
  }
}
