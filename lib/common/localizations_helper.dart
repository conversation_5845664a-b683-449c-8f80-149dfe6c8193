

import 'package:agentor_repositoryns/exception_utils.dart';
import 'package:flutter/widgets.dart';
import 'package:topbrokers/generated/app_localizations.dart';
extension AppLocalizationsHelper on BuildContext {
  ///
  /// Obtiene AppLocalizations del contexto o lanza una excepción si no se encuentra
  ///
  ///
  AppLocalizations getAppLocalizationsOrThrow([String errorMessage="Can't resolve AppLocalizations"]){
    return AppLocalizations.of(this) ?? doThrow<AppLocalizations>(errorMessage);
  }
  AppLocalizations get apploc {
    return AppLocalizations.of(this) ?? doThrow<AppLocalizations>("Can't resolve AppLocalizations");
  }
}