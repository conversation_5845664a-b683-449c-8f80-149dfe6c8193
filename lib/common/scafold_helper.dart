import 'package:flutter/material.dart';

extension ScafoldHelper on BuildContext {
  void showError(String error) {
    final sm = ScaffoldMessenger.maybeOf(this);
    if (sm != null)
      sm.showSnackBar(
        SnackBar(
          content: Text(
            error,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Theme.of(this).errorColor.withOpacity(0.9),
        ),
      );
  }

  void showNotification(String message) {
    final sm = ScaffoldMessenger.maybeOf(this);
    if (sm != null)
      sm.showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: TextStyle(
              color:  Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
           backgroundColor: Theme.of(this).hintColor.withOpacity(0.9),
        ),
      );
  }
}
