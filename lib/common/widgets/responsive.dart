import 'package:flutter/material.dart';

class Responsive extends StatelessWidget {
  final List<Widget> children;
  final int nColumns;

  Responsive({required this.children, this.nColumns = 1});

  @override
  Widget build(BuildContext context) {
    int nRows = (children.length ~/ nColumns) + (children.length % nColumns == 0 ? 0:1);

    int nChild = 0;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int nRow = 0; nRow < nRows; nRow++)
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              for (int nCol = 0; nCol < nColumns; nCol++, nChild++)
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: nChild < this.children.length ? [this.children[nChild]] : [],
                  ), //this.children[nChild],
                ),
            ],
          ),
      ],
    );
  }
}
