import 'package:flutter/material.dart';

const _c_card_padding = const EdgeInsets.fromLTRB(10, 10, 10, 10);
const _c_card_margin = const EdgeInsets.fromLTRB(5, 5, 5, 5);

class TitledCard extends StatelessWidget {
  final Widget heading;
  final List<Widget> children;
  final List<Widget> barActions;
  final EdgeInsetsGeometry margin;
  final EdgeInsetsGeometry padding;

  TitledCard(
    this.heading, {
    required this.children,
    this.barActions = const [],
    this.margin = _c_card_margin,
    this.padding = _c_card_padding,
  }) : super();

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin,
      child: Container(
        padding: padding,
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey)),
                    ),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                    child: Row(
                      children: [
                        Expanded(child: heading),
                        if (barActions.length != 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.min,
                            children: barActions,
                          ),
                      ],
                    ),
                  )
                ]..addAll(children),
              ),
            )
          ],
        ),
      ),
    );
  }
}
