import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';

class AgentLink extends StatelessWidget {
  final AgentDto agent;
  final bool isDense;
  final bool isEnabled;
  final TextAlign textAlign;
  AgentLink({required this.agent, this.isDense = true, this.textAlign = TextAlign.right, this.isEnabled = false})
      : super();
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textIconSize = (theme.iconTheme.size ?? 24) * (isDense ? 0.6 : 1);

    return InkWell(
      enableFeedback: isEnabled,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (textAlign == TextAlign.left && isEnabled)
            Icon(
              Icons.open_in_new,
              color: theme.textTheme.bodyMedium?.color,
              size: textIconSize,
            ),
          if (textAlign == TextAlign.left)
            Icon(
              Icons.business_center,
              size: textIconSize,
            ),
          Expanded(
            child: Text(
              agent.name.vn ?? "",
              textAlign: textAlign,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (textAlign == TextAlign.right)
            Icon(
              Icons.business_center,
              size: textIconSize,
            ),
          if (textAlign == TextAlign.right && isEnabled)
            Icon(
              Icons.open_in_new,
              color: theme.textTheme.bodyMedium?.color,
              size: textIconSize,
            ),
        ],
      ),
      onTap: () {}, //Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: agent.id.v),
    );
  }
}
