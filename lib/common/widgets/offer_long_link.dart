import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';

class OfferLongLink extends StatelessWidget {
  final OfferDto offer;
  final bool isDense;
  final TextAlign textAlign;
  OfferLongLink({required this.offer, this.isDense = true, this.textAlign = TextAlign.right}) : super();
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textIconSize = (theme.iconTheme.size ?? 24) * (isDense ? 0.6 : 1);
    final url = offer.property.vn?.favouritePicture.vn?.thumbnail.vn?.url.vn;
    return Container(
      child: InkWell(
        child: Row(
          children: [
            if (url != null)
              Image.network(
                url,
                width: textIconSize,
                height: textIconSize,
                fit: BoxFit.cover,
              )
            else
              Icon(
                CustomAppIcons.offer,
                color: theme.textTheme.bodyText2?.color,
                size: textIconSize,
              ),
            VerticalDivider(width: 10),
            Expanded(
              child: Text(
                offer.name.vn ?? "",
                textAlign: textAlign,
              ),
            ),
            Icon(
              Icons.open_in_new,
              color: theme.textTheme.bodyText2?.color,
              size: textIconSize,
            )
          ],
        ),
        onTap: () {
          if (offer.id.vn != null) Navigator.pushNamed(context, AgentorRoutes.showOffer, arguments: offer.id.v);
        },
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ), // (top: BorderSide(color: Colors.grey.shade200, width: 1)),
        borderRadius: BorderRadius.all(Radius.circular(2)),
      ),
      padding: EdgeInsets.fromLTRB(2, 2, 2, 2),
      margin: EdgeInsets.fromLTRB(0, 2, 0, 0),
    );
  }
}
