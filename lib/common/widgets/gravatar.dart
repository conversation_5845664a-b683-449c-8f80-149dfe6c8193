import 'package:flutter/material.dart';

class Gravatar extends StatelessWidget {
  final double size;
  final double corners;
  final double ledSize;
  final Color? backgroundColor;
  final Color? ledColor;
  final Widget? child;
  final String? childUrl;
  final IconData? led;
  //
  final String alertText;
  final Color alertColor;

  Gravatar({
    this.size = 40,
    this.ledSize = 4,
    this.corners = 2,
    this.ledColor,
    this.child,
    this.childUrl,
    this.backgroundColor,
    this.led,
    this.alertText = "",
    this.alertColor = Colors.blueGrey,
  })  : assert(!(child != null && childUrl != null)),
        super();

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(corners), //or 15.0
      child: Container(
        height: size,
        width: size,
        color: backgroundColor ?? Colors.transparent,
        padding: EdgeInsets.all(2),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (child != null)
              FittedBox(
                fit: BoxFit.contain,
                alignment: Alignment.center,
                child: child,
              ),
            if (childUrl != null)
              Image.network(
                childUrl!,
                width: size,
                height: size,
                fit: BoxFit.cover,
              ),
            if (led != null)
              Align(
                alignment: Alignment.bottomRight,
                child: Container(
                  margin: EdgeInsets.fromLTRB(0, 0, 2, 2),
                  width: ledSize + 2,
                  height: ledSize + 2,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    shape: BoxShape.circle,
                  ),
                  child: ledColor != null
                      ? Icon(
                          led,
                          size: ledSize,
                          color: ledColor,
                        )
                      : Icon(
                          led,
                          size: ledSize,
                        ),
                ),
              ),
            if (alertText != "")
              Align(
                alignment: Alignment.topRight,
                child: Container(
                  margin: EdgeInsets.fromLTRB(0, 2, 2, 0),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Container(
                    height: Theme.of(context).textTheme.bodyText1?.fontSize ?? 12 + 4,
                    padding: EdgeInsets.all(1),
                    child: Container(
                      decoration: BoxDecoration(
                        color: alertColor,
                        shape: BoxShape.rectangle,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: EdgeInsets.all(1),
                      child: FittedBox(
                        fit: BoxFit.contain,
                        alignment: Alignment.centerRight,
                        child: Text(
                          alertText,
                          style: Theme.of(context).textTheme.bodyText1?.copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            /*Align(
                alignment: Alignment.topRight,
                child: Container(
                  margin: EdgeInsets.fromLTRB(0, 0, 4, 4),
                  padding: EdgeInsets.all(2),
                  //width: 40,
                  height: 10,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: FittedBox(
                    fit: BoxFit.contain,
                    alignment: Alignment.centerRight,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.rectangle,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(2),
                        child: Text(alertText),
                      ),
                    ),
                  ),
                ),
                
              ),*/
          ],
        ),
      ),
    );
  }
}
