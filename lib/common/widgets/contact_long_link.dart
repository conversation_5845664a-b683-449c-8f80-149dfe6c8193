import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';

class ContactLongLink extends StatelessWidget {
  final ContactDto contact;
  final bool isDense;
  final TextAlign textAlign;
  ContactLongLink({required this.contact, this.isDense = true, this.textAlign = TextAlign.right}) : super();
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textIconSize = (theme.iconTheme.size ?? 24) * (isDense ? 0.6 : 1);
    return Container(
      child: InkWell(
        child: Row(
          children: [
            Icon(
              CustomAppIcons.contact,
              color: theme.textTheme.bodyText2?.color,
              size: textIconSize,
            ),
            VerticalDivider(width: 10),
            Expanded(
              child: Text(contact.name.vn ?? "", textAlign: textAlign),
            ),
            Icon(
              Icons.open_in_new,
              color: theme.textTheme.bodyText2?.color,
              size: textIconSize,
            ),
          ],
        ),
        onTap: () {
          if (contact.id.vn != null) Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: contact.id.v);
        },
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ), // (top: BorderSide(color: Colors.grey.shade200, width: 1)),
        borderRadius: BorderRadius.all(Radius.circular(2)),
      ),
      padding: EdgeInsets.fromLTRB(2, 2, 2, 2),
      margin: EdgeInsets.fromLTRB(0, 2, 0, 0),
    );
  }
}
