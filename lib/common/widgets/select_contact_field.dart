import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/contact_edit/models/contact_edit_page_result.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/routes.dart';

///
///  Campo que incorpora la lógica para seleccionar/crear un contacto.
///  El campo ofrece
///  - Selector (búsqueda textual) entre los contactos del agente
///  - Botón para añadir un nuevo contacto.
///  - Botón para ver la ficha del contacto.
///  
class SelectContactFieldEntry extends FieldEntry<ContactDto> {
  final api = Deps.solve<ApiServices>();
  SelectContactFieldEntry({
    String? name,
    String? label,
    required ContactDto? Function() getValue,
    required void Function(ContactDto?) setValue,
    bool isRequired = false,
  }) : super(name: name, label: label, getValue: getValue, setValue: setValue, isRequired: isRequired);

  @override
  Widget build(BuildContext context) {
    final ContactDto? customer = getValue();
    return SearchFieldEntry<ContactDto>(
      label: label,
      getValue: getValue,
      setValue: setValue,
      onSearch: (String search) => api.listContacts(filter: ContactsListFilter(search: Some(search))),
      onAdd: () async {
        final result = await Navigator.pushNamed(context, AgentorRoutes.addContact);
        if (result is ContactEditPageResult && result.id != null) {
          try {
            return await api.getContact(result.id as String);
          } on Exception {
            print("Problemas obteniendo/asignando contacto");
          }
        }
        return null;
      },
      itemBuilder: (context, contact, {bool isSelected = false}) => ContactListItem(contact: contact),
      valueToString: (contact) => contact != null ? contact.name.vn ?? "" : "Seleccione un contacto",
      isRequired: isRequired,
      rightWidget: Row(
        children: [
          if (customer?.id.vn != null)
            InkWell(
              child: Icon(Icons.open_in_new),
              onTap: () async {
                final customerId = customer!.id.v;
                Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: customerId);
              },
            ),
        ],
      ),
    );
  }
}
