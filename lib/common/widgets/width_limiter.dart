import 'package:flutter/material.dart';

class WidthLimiter extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final Color color;

  WidthLimiter({required this.child, this.maxWidth = 492, this.color = const Color(0xFFEEEEEE)}) : super();
//Image.asset('assets/images/GeeksforGeeks.jpg')
  @override
  Widget build(BuildContext context) {

    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          color: this.color,
        ),
        Container(
          height: double.infinity,
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: this.child,
        ),
      ],
    );
  }
}
