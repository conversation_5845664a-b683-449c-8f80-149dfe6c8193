import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';

class ContactLink extends StatelessWidget {
  final ContactDto contact;
  final bool isDense;
  final bool isEnabled;
  final TextAlign textAlign;
  ContactLink({required this.contact, this.isDense = true, this.textAlign = TextAlign.right, this.isEnabled = true})
      : super();
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textIconSize = (theme.iconTheme.size ?? 24) * (isDense ? 0.6 : 1);

    return InkWell(
      child: Row(
        children: [
          if (textAlign == TextAlign.left && isEnabled)
            Icon(
              Icons.open_in_new,
              color: theme.textTheme.bodyMedium?.color,
              size: textIconSize,
            ),
          Expanded(
            child: Text(
              contact.name.vn ?? "",
              textAlign: textAlign,
            ),
          ),
          if (textAlign == TextAlign.right && isEnabled)
            Icon(
              Icons.open_in_new,
              color: theme.textTheme.bodyMedium?.color,
              size: textIconSize,
            ),
        ],
      ),
      onTap: () {
        if (isEnabled) Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: contact.id.v);
      },
    );
  }
}
