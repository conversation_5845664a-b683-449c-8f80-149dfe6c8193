import 'package:intl/intl.dart';

extension DateTimeHelper on DateTime {
  bool isToday() {
    final now = DateTime.now();
    return this.isAfter(now.beginOfDay()) && this.isBefore(now.beginOfTomorrow());
  }

  bool isActualMonth() => _sameMonth(this, DateTime.now());

  bool isActualYear() => this.year == DateTime.now().year;

  bool isActualWeek() => this.beginOfWeek().isAtSameMomentAs(DateTime.now().beginOfWeek());

  DateTime beginOfWeek() => this.beginOfDay().subtract(Duration(days: this.weekday - 1));

  DateTime beginOfDay() => new DateTime(this.year, this.month, this.day);

  DateTime beginOfTomorrow() => new DateTime(this.year, this.month, this.day + 1);

  DateTime at(int hour, int minute, int second) => new DateTime(this.year, this.month, this.day, hour, minute, second);

  DateTime beginOfYesterday() => new DateTime(this.year, this.month, this.day - 1);

  DateTime beginOfYear() => new DateTime(this.year, 1, 1);

  String formatRelativeToNow() {
    if (this.isToday()) {
      return DateFormat.Hm().format(this);
    } else {
      final hh = this.hour==0 && this.minute==0 && this.second==0 ? "" : " ${DateFormat.Hm().format(this)}";
      if (this.isActualWeek()) {
        return '${DateFormat.EEEE().format(this)}$hh';
      } else if (this.isActualMonth()) {
        return '${DateFormat.MMMd().format(this)}$hh';
      } else if (this.isActualYear()) {
        return '${DateFormat.MMMd().format(this)}$hh';
      } else {
        return '${DateFormat.yMMMd().format(this)}$hh';
      }
    }
  }

  static bool _sameMonth(DateTime a, DateTime b) => a.year == b.year && a.month == b.month;
}
