class PropertyLabels {
  static const Map<String, String> _locales = {
    "address_city": "Ciudad",
    "status": "Status",
    "lb_totalSurfaceM2": "m² totales",
    "txt_totalSurfaceM2": "%1 m²",
    "lb_usefulSurfaceM2": "m² útiles",
    "txt_usefulSurfaceM2": "%1 m² útiles",
    "lb_solarSurfaceM2": "m² solar",
    "txt_solarSurfaceM2": "%1 m² solar",
    "lb_constructionYear": "Año de construcción",
    "lb_conservationStatus": "Estado de conservación",
    "individualBedroomsCount": "Habitaciones individuales",
    "doubleBedroomsCount": "Habitaciones dobles",
    "suiteBedroomsCount": "Habitaciones suite",
    "totalBedroomsCount": "Habitaciones totales",
    "bathroomsCount": "Baños",
    "toiletsCount": "Aseos",
    "buddleHas": "Labadero",
    "kitchenHas": "Cocina",
    "dinningRoomHas": "Comedor",
    "storageRoomHas": "Trastero",
    "balconyHas": "Balcón",
    "terraceHas": "Terraza",
    "builtInCabinetsCount": "Armarios empotrados",
    "externalJoineryCode": "Carpintería exterior",
    "groundCodes": "Tipo de suelo",
    "waterSupplyHas": "Suministro de agua",
    "powerSupplyHas": "Suministro eléctrico",
    "gasSupplyHas": "Suministro de gas",
    "airConditioningCode": "Aire acondicionado",
    "heatingCode": "Calefacción",
    "fireplaceHas": "Chimenea",
    "intercomHas": "Interfono",
    "reinforcedDoorHas": "Puerta blindada",
    "alarmSystemHas": "Sistema de alarma",
    "elevatorHas": "Ascensor",
    "handicappedAccessibleIs": "Accesible para discapacitados",
    "furnishedIs": "Amueblado",
    "gardenCode": "Jardín",
    "outsideAreaCode": "Zona exterior",
    "swimmingPoolCode": "Piscina",
    "parkingPlacesCount": "Plazas de parking",
    "optionalParkingIs": "Parking opcional",
    "facadeCodes": "Fachada",
    "orientationCodes": "Orientación",
    "sunnyIs": "Soleado",
    "communityFeesAmount": "Cuota comunidad (mensual)",
    "neighborsPerFloorCount": "Vecinos por planta",
    "buildingFloorsCount": "Plantas del edificio",
    "floorCode": "Planta",
    "energyCertificateCode": "Certificado energético",
    "consumptionLevelCode": "Nivel de consumo energético",
    "emissionLevelCode": "Nivel de Emisione",
  };

  static get status {
    return _locale("status");
  }
  static String get address_city {
    return _locale("address_city");
  }

  static String get lb_totalSurfaceM2 {
    return _locale("lb_totalSurfaceM2");
  }
  static String get txt_totalSurfaceM2 {
    return _locale("txt_totalSurfaceM2");
  }

    static String get lb_solarSurfaceM2 {
    return _locale("lb_solarSurfaceM2");
  }
  static String get txt_solarSurfaceM2 {
    return _locale("txt_solarSurfaceM2");
  }

  static String get lb_constructionYear {
    return _locale("lb_constructionYear");
  }

  static String get lb_conservationStatus {
    return _locale("lb_conservationStatus");
  }

  static String get individualBedroomsCount {
    return _locale("individualBedroomsCount");
  }

  static String get doubleBedroomsCount {
    return _locale("doubleBedroomsCount");
  }

  static String get suiteBedroomsCount {
    return _locale("suiteBedroomsCount");
  }

  static String get totalBedroomsCount {
    return _locale("totalBedroomsCount");
  }

  static String get bathroomsCount {
    return _locale("bathroomsCount");
  }

  static String get toiletsCount {
    return _locale("toiletsCount");
  }

  static String get buddleHas {
    return _locale("buddleHas");
  }

  static String get kitchenHas {
    return _locale("kitchenHas");
  }

  static String get dinningRoomHas {
    return _locale("dinningRoomHas");
  }

  static String get storageRoomHas {
    return _locale("storageRoomHas");
  }

  static String get balconyHas {
    return _locale("balconyHas");
  }

  static String get terraceHas {
    return _locale("terraceHas");
  }

  static String get builtInCabinetsCount {
    return _locale("builtInCabinetsCount");
  }

  static String get externalJoineryCode {
    return _locale("externalJoineryCode");
  }

  static String get groundCodes {
    return _locale("groundCodes");
  }

  static String get waterSupplyHas {
    return _locale("waterSupplyHas");
  }

  static String get powerSupplyHas {
    return _locale("powerSupplyHas");
  }

  static String get gasSupplyHas {
    return _locale("gasSupplyHas");
  }

  static String get airConditioningCode {
    return _locale("airConditioningCode");
  }

  static String get heatingCode {
    return _locale("heatingCode");
  }

  static String get fireplaceHas {
    return _locale("fireplaceHas");
  }

  static String get intercomHas {
    return _locale("intercomHas");
  }

  static String get reinforcedDoorHas {
    return _locale("reinforcedDoorHas");
  }

  static String get alarmSystemHas {
    return _locale("alarmSystemHas");
  }

  static String get elevatorHas {
    return _locale("elevatorHas");
  }

  static String get handicappedAccessibleIs {
    return _locale("handicappedAccessibleIs");
  }

  static String get furnishedIs {
    return _locale("furnishedIs");
  }

  static String get gardenCode {
    return _locale("gardenCode");
  }

  static String get outsideAreaCode {
    return _locale("outsideAreaCode");
  }

  static String get swimmingPoolCode {
    return _locale("swimmingPoolCode");
  }

  static String get parkingPlacesCount {
    return _locale("parkingPlacesCount");
  }

  static String get optionalParkingIs {
    return _locale("optionalParkingIs");
  }

  static String get facadeCodes {
    return _locale("facadeCodes");
  }

  static String get orientationCodes {
    return _locale("orientationCodes");
  }

  static String get sunnyIs {
    return _locale("sunnyIs");
  }

  static String get communityFeesAmount {
    return _locale("communityFeesAmount");
  }

  static String get neighborsPerFloorCount {
    return _locale("neighborsPerFloorCount");
  }

  static String get buildingFloorsCount {
    return _locale("buildingFloorsCount");
  }

  static String get floorCode {
    return _locale("floorCode");
  }

  static String get energyCertificateCode {
    return _locale("energyCertificateCode");
  }

  static String get consumptionLevelCode {
    return _locale("consumptionLevelCode");
  }

  static String get emissionLevelCode {
    return _locale("emissionLevelCode");
  }

  static String _locale(String key) {
    return _locales[key] ?? "";
  }
}

