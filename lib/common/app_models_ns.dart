import 'dart:typed_data';

import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:agentor_repositoryns/services/ecom_purchases_srv.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';

///
///
/// Ofrece todos los métodos para acceso a los modelos (a través de la API).
/// Cualquier creación, modificación, lectura realizada a través de [AppModelsNS] es notificada usando [modelsChannelBloc] lo cual permite
/// a los consumidores de ese canal enterarse de cambios en entidades de las que depende
///
///
class AppModelsNS {
  final ApiServices _api;
  final ModelsChannelBloc _modelsChannelBloc;

  AppModelsNS({
    required ApiServices api,
    required ModelsChannelBloc modelsChannelBloc,
  })  : this._api = api,
        this._modelsChannelBloc = modelsChannelBloc;

  factory AppModelsNS.create({required BuildContext context}) => AppModelsNS(
        api: Deps.solve<ApiServices>(),
        modelsChannelBloc: BlocProvider.of<ModelsChannelBloc>(context),
      );
  Future<void> addOfferToMyFavourites(String offerId) async {
    await _api.addOfferToMyFavourites(offerId);
    _modelsChannelBloc.add(ModelsChannelOnAddedOfferToMyFavourites(offerId: offerId));
  }

  Future<ActionDto> createAction(ActionDto action) => notifyOnCreated(() => _api.postAction(action));
  Future<ContactDto> createContact(ContactDto contact) => notifyOnCreated(() => _api.postContact(contact));
  Future<DemandDto> createDemand(DemandDto demand) => notifyOnCreated(() => _api.postDemand(demand));
  Future<OfferDto> createOffer(OfferDto offer) => notifyOnCreated(() => _api.postOffer(offer));
  Future<PropertymediaDto> createOfferPropertyMedia(
    String offerId,
    Uint8List content,
    String? filename,
    String mimetype,
  ) async {
    final created = await _api.uploadOfferPropertyMedia(offerId, content, filename, mimetype);
    _modelsChannelBloc.add(ModelsChannelOnPropertymediaCreated(offerId: offerId, propertymedias: [created]));
    return created;
  }

  Future<List<PropertyzoneDto>> getPropertyzonePath([String? zoneId]) => _api.getPropertyzonePath(zoneId);

  Future<bool> isOfferInMyFavourites(String offerId) async => _api.isOfferInMyFavourites(offerId);
  Future<List<ActionDto>> listActions({
    ActionsListFilter filter = const ActionsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listActions(filter: filter, offset: offset, limit: limit);

  Future<List<ActiontypeDto>> listActiontypes({
    ActiontypesListFilter filter = const ActiontypesListFilter(),
  }) =>
      _api.listActiontypes(filter: filter);

  Future<List<WorkgroupMemberDto>> listAgentAffiliations({
    AgentAffiliationsListFilter filter = const AgentAffiliationsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listAgentAffiliations(filter: filter, offset: offset, limit: limit);

  Future<List<CityDto>> listCities({required CitiesListFilter filter, int offset = 0, int limit = 100}) =>
      _api.getCities(filter: filter, limit: limit, offset: offset);

  Future<List<OfferDto>> listCloudOffers({
    CloudoffersListFilter filter = const CloudoffersListFilter(),
    CloudoffersListOrderBy orderBy = CloudoffersListOrderBy.created_at_desc,
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listCloudoffers(filter: filter, orderBy: orderBy, offset: offset, limit: limit);

  Future<List<FieldDefDto>> listContactFielddefs() => _api.listContactFielddefs();

  Future<List<ContactDto>> listContacts({
    ContactsListFilter filter = const ContactsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listContacts(filter: filter, offset: offset, limit: limit);

  Future<List<FieldDefDto>> listDemandFielddefs() => _api.listDemandFielddefs();

  Future<List<DemandDto>> listDemands({
    DemandsListFilter filter = const DemandsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listDemands(filter: filter, offset: offset, limit: limit);
  Future<List<MatchingDto>> listMatchings({
    MatchingsListFilter filter = const MatchingsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listMatchings(filter: filter, offset: offset, limit: limit);
  Future<List<FieldDefDto>> listOfferFielddefs() => _api.listOfferFielddefs();

  Future<List<PropertymediaDto>> listOfferPropertymedias(String id) => _api.listOfferPropertymedias(id);

  Future<List<WorkgroupDto>> listOfferPublications(String offerId) async {
    final result = await _api.listOfferPublications(offerId);
    return result;
  }

  Future<List<OfferDto>> listOffers({
    OffersListFilter filter = const OffersListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listOffers(filter: filter, offset: offset, limit: limit);

  Future<List<PropertytypeDto>> listPropertytypes({
    PropertytypesListFilter filter = const PropertytypesListFilter(),
  }) =>
      _api.listPropertytypes(filter: filter);

  Future<List<PropertyzoneDto>> listPropertyzones({
    PropertyzonesListFilter filter = const PropertyzonesListFilter(),
    int offset = 0,
    int limit = 100,
  }) =>
      _api.listPropertyzones(filter: filter);

  Future<List<EcomPurchaseDto>> listPurchases({
    EcomPurchasesListFilter filter = const EcomPurchasesListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listPurchases(filter: filter, offset: offset, limit: limit);

  Future<List<WorkgroupDto>> listWorkgroups({
    WorkgroupsListFilter filter = const WorkgroupsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      _api.listWorkgroups(filter: filter);

  Future<T> notifyOnCreated<T>(Future<T> Function() callback) async {
    final entity = await callback();
    if (entity != null) {
      _modelsChannelBloc.add(ModelsChannelOnCreated(entities: [entity]));
    }
    return entity;
  }

  Future<T?> notifyOnFetched<T>(Future<T?> Function() callback) async {
    final entity = await callback();
    if (entity != null) {
      _modelsChannelBloc.add(ModelsChannelOnFetched(entities: [entity]));
    }
    return entity;
  }

  Future<T> notifyOnFetchedOld<T>(Future<T> Function() callback) async {
    final entity = await callback();
    if (entity != null) {
      _modelsChannelBloc.add(ModelsChannelOnFetched(entities: [entity]));
    }
    return entity;
  }

  Future<T?> notifyOnUpdated<T>(Future<T?> Function() callback) async {
    final entity = await callback();
    if (entity != null) {
      _modelsChannelBloc.add(ModelsChannelOnUpdated(entities: [entity]));
    }
    return entity;
  }

  Future<T> notifyOnUpdatedOld<T>(Future<T> Function() callback) async {
    final entity = await callback();
    if (entity != null) {
      _modelsChannelBloc.add(ModelsChannelOnUpdated(entities: [entity]));
    }
    return entity;
  }

  Future<WorkgroupDto> publishOffer(String offerId, String workgroupId) async {
    final publication = await _api.publishOffer(offerId, workgroupId);
    _modelsChannelBloc.add(ModelsChannelOnWorkgroupOfferCreated(offerId: offerId, workgroupId: workgroupId));
    return publication;
  }

  Future<ActionDto?> readAction(String id) => notifyOnFetchedOld(() => _api.getAction(id));

  Future<AgentDto?> readAgent(String id) => notifyOnFetched(() => _api.getAgent(id));

  Future<ContactDto?> readContact(String id) => notifyOnFetched(() => _api.getContact(id));

  Future<DemandDto?> readDemand(String id) => notifyOnFetchedOld(() => _api.getDemand(id));

  Future<AgentDto?> readMyAgent() => _api.getMyAgent();

  Future<OfferDto?> readOffer(String id, {bool ensure_last_version = false}) {
    if (ensure_last_version)
      return notifyOnUpdated(
        // Si leemos la última versión OK suponemos que ha habido un refresco interno y notificamos a todo el mundo un "updated" para que
        // refresquen si lo desean la versión mostrada, ...
        () => _api.getOffer(
          id,
          ensure_last_version: true,
        ),
      );
    else
      return notifyOnFetched(
        () => _api.getOffer(
          id,
          ensure_last_version: false,
        ),
      );
  }

  Future<void> removeOfferFromMyFavourites(String offerId) async {
    await _api.removeOfferFromMyFavourites(offerId);
    _modelsChannelBloc.add(ModelsChannelOnRemovedOfferFromMyFavourites(offerId: offerId));
  }

  Future<int> removeOfferPropertymedia(String offerId, String mediaKey) async {
    final count = await _api.deleteOfferPropertymedia(offerId, mediaKey);
    if (count != 0) {
      _modelsChannelBloc.add(ModelsChannelOnPropertymediaDeleted(offerId: offerId, mediaKey: mediaKey));
    }
    return count;
  }

  Future<int> unpublishOffer(String offerId, String workgroupId) async {
    final count = await _api.unpublishOffer(offerId, workgroupId);
    _modelsChannelBloc.add(ModelsChannelOnWorkgroupOfferDeleted(offerId: offerId, workgroupId: workgroupId));
    return count;
  }

  Future<ActionDto> updateAction(ActionDto action) => notifyOnUpdatedOld(() => _api.putAction(action));

  Future<AgentDto?> updateAgent(AgentDto agent) => notifyOnUpdated(() => _api.putAgent(agent));

  Future<ContactDto> updateContact(ContactDto contact) => notifyOnUpdatedOld(() => _api.putContact(contact));

  Future<DemandDto> updateDemand(DemandDto demand) => notifyOnUpdatedOld(() => _api.putDemand(demand));

  Future<OfferDto> updateOffer(OfferDto offer) => notifyOnUpdatedOld(() => _api.putOffer(offer));

  Future<PropertymediaDto> updateOfferPropertymedia(
    String offerId,
    String mediaKey,
    PropertymediaDto propertymedia,
  ) async {
    final updated = await _api.updateOfferPropertymedia(offerId, mediaKey, propertymedia);
    _modelsChannelBloc.add(ModelsChannelOnPropertymediaUpdated(offerId: offerId, entities: [updated]));
    return updated;
  }

  Future<List<OfferversiontypeDto>> getOfferVersiontypes() => _api.listOfferversiontypes();
}
