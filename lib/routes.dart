class AgentorRoutes {
  static final home = "/";
  static final signUp = "/signUp";
  static final signIn = "/signIn";
  static final supportRequest = "/supportRequest";
  static final recoverPass = "/recoverPass";
  static final editAgent = "/editAgent";
  static final addOffer = "/addOffer";
  static final showOffer = "/showOffer";
  static final editOffer = "/editOffer";
  static final addContact = "/addContact";
  static final editContact = "/editContact";
  static final showContact = "/showContact";
  static final addAction = "/addAction";
  static final editAction = "/editAction";
  static final showServiceaction = "/showServiceaction";
  static final newServiceaction = "/newServiceaction";
  static final addDemand = "/addDemand";
  static final editDemand = "/editDemand";
  static final showDemand = "/showDemand";
  static final zoneSelector = "/zoneSelector";
  static final addDeposit = "/addDeposit";
  static final ecomPurchases="/ecomPurchases";
}
