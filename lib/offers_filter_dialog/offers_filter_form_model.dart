import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class OffersFilterFormModel {
  Optional<String> offerId;
  Optional<ContactDto> customer;
  Optional<bool> isVersion;
  Optional<String> propertyTypeCode;
  Optional<double> propertyM2Min;
  Optional<double> propertyM2Max;
  Optional<bool?> customerIsBankServicer;
  Optional<bool> includeSales;
  Optional<double> saleAmountMin;
  Optional<double> saleAmountMax;
  Optional<bool> includeRents;
  Optional<double> rentAmountMin;
  Optional<double> rentAmountMax;
  Optional<PropertyzoneDto> zone;
  Optional<CityDto> city;

  /// Incluir ofertas y demandas de otros agentes.  Por defecto False
  Optional<bool> includeNotMine;
  OffersFilterFormModel({
    this.offerId = const None(),
    this.customer = const None(),
    this.isVersion = const None(),
    this.customerIsBankServicer = const None(),
    this.propertyTypeCode = const None(),
    this.propertyM2Min = const None(),
    this.propertyM2Max = const None(),
    this.includeRents = const None(),
    this.rentAmountMin = const None(),
    this.rentAmountMax = const None(),
    this.includeSales = const None(),
    this.saleAmountMin = const None(),
    this.saleAmountMax = const None(),
    this.zone = const None(),
    this.city = const None(),
    this.includeNotMine = const None(),
  });
}
