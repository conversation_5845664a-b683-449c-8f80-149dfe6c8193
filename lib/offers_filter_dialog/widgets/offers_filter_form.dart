import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/offer_edit/widgets/city_list_item.dart';
import 'package:topbrokers/offers_filter_dialog/offers_filter.dart';

import '../../propertyzoneslist/selectPropertyzoneDialog.dart';

class OffersFilterForm extends StatefulWidget {
  final OffersListFilter actualFilter;
  //final void Function(OpportunitiesListFilter changedFilter) onAccept;
  final void Function(OffersListFilter changedFilter) onChanged;

  OffersFilterForm({
    Key? key,
    required this.actualFilter,
    required this.onChanged,
  }) : super(key: key ?? AgentorKeys.offersFilterPage);

  @override
  _OffersFilterFormState createState() => _OffersFilterFormState();
}

class _OffersFilterFormState extends State<OffersFilterForm> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  //OffersFilterFormModel _formData;

  _OffersFilterFormState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));
    late OffersFilterFormBloc _formBloc;

    return BlocProvider<OffersFilterFormBloc>(
      create: (context) {
        _formBloc = OffersFilterFormBloc.create(context)..add(OffersFilterFormOnLoadEvent(filterModel: widget.actualFilter));
        return _formBloc;
      },
      child: BlocConsumer<OffersFilterFormBloc, OffersFilterFormState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          if (state is OffersFilterFormFailureState) {
            return Center(
              child: Text(localizations.offersfilter_errLoading),
            );
          } else if (state is OffersFilterFormLoadedState) {
            //this._formData = state.formData;
            return SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  _buildForm(context, state.imIndividual, state.formData, () {
                    widget.onChanged(_formDataToListFilter(state.formData, state.originalFilter));
                  }),
                ],
              ),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, bool imIndividual, OffersFilterFormModel formData, void Function() onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.max,
      children: [
        _formEntries(context, imIndividual, formData, onChanged).buildForm(key: _formKey, context: context),
      ],
    );
  }

  OffersListFilter _formDataToListFilter(
    OffersFilterFormModel formData,
    OffersListFilter originalFilter,
  ) =>
      originalFilter.copyWith(
        offerId: formData.offerId,
        customerId: formData.customer is None ? None() : formData.customer.v.id,
        isVersion: formData.isVersion.flatMap((b) => b == null ? None() : Some(b)),
        customerIsBankServicer: formData.customerIsBankServicer.flatMap((b) => b == null ? None() : Some(b)),
        propertytypeCode: formData.propertyTypeCode,
        propertyM2Min: formData.propertyM2Min,
        propertyM2Max: formData.propertyM2Max,
        rentAllowed: formData.includeRents,
        rentAmountMin: formData.rentAmountMin,
        rentAmountMax: formData.rentAmountMax,
        saleAllowed: formData.includeSales,
        saleAmountMin: formData.saleAmountMin,
        saleAmountMax: formData.saleAmountMax,
        containerzoneId: formData.zone.vn?.id ?? const None(),
        propertyAddrCityCode: formData.city is None ? None() : formData.city.v.code,
        includeNotMine: formData.includeNotMine,
      );

  List<Widget> _formEntries(BuildContext context, bool isIndividual, OffersFilterFormModel formData, Function() onChanged) {
    const c_any = "any";
    const c_rent = "rent";
    const c_sale = "sale";

    final localizations = context.getAppLocalizationsOrThrow();

    return [
      GroupEntry(
        //label: "Datos del anuncio",
        isSubgroup: true,
        children: [
          SimpleFieldEntry<int>(
            label: "Referencia",
            getValue: () => formData.offerId.isSome ? int.tryParse(formData.offerId.v) : null,
            setValue: (int? v) {
              setState(() {
                formData.offerId = v == null ? None() : Some(v.toString());
              });
              onChanged();
            },
            isCount: true,
          ),
          if (!isIndividual)
            SelectFieldEntry<String>(
              label: localizations.offersfilter_isVersionLabel,
              getValue: () {
                switch (formData.isVersion.vn) {
                  case true:
                    return "versions";
                  case false:
                    return "no_versions";
                  default:
                    return c_any;
                }
              },
              setValue: (String? code) {
                setState(() {
                  switch (code) {
                    case "versions":
                      formData.isVersion = True;
                      break;
                    case "no_versions":
                      formData.isVersion = False;
                      break;
                    default:
                      formData.isVersion = None();
                      break;
                  }
                });
                onChanged();
              },
              options: [
                SelectOption(label: localizations.common_AnyLabel, value: c_any),
                SelectOption(label: localizations.offersfilter_isVersion_yes, value: "versions"),
                SelectOption(label: localizations.offersfilter_isVersion_not, value: "no_versions"),
              ],
            ),
          if (!isIndividual)
            SelectFieldEntry<String>(
              label: "Tipo de propietario",
              getValue: () {
                switch (formData.customerIsBankServicer.vn) {
                  case true:
                    return "bankservicer";
                  case false:
                    return "no_bankservicer";
                  default:
                    return c_any;
                }
              },
              setValue: (String? code) {
                setState(() {
                  switch (code) {
                    case "bankservicer":
                      formData.customerIsBankServicer = True;
                      break;
                    case "no_bankservicer":
                      formData.customerIsBankServicer = False;
                      break;
                    default:
                      formData.customerIsBankServicer = None();
                      break;
                  }
                });
                onChanged();
              },
              options: [
                SelectOption(label: localizations.common_AnyLabel, value: c_any),
                SelectOption(label: "Bank Servicer", value: "bankservicer"),
                SelectOption(label: "No Bank Servicer", value: "no_bankservicer"),
              ],
            ),
          SearchFieldEntry<ContactDto>(
            label: localizations.offersfilter_customerLabel,
            getValue: () => formData.customer.vn,
            setValue: (ContactDto? value) {
              this.setState(() => formData.customer = value == null ? None() : Some(value));
              onChanged();
            },
            onSearch: (String search) => api.listContacts(
              filter: ContactsListFilter(search: Some(search), isOfferCustomer: True),
              limit: 50,
            ),
            itemBuilder: (context, contact, {bool isSelected = false}) => ContactListItem(contact: contact),
            valueToString: (contact) => contact?.name.vn ?? "",
          ),
          CustomSelectFieldEntry<PropertyzoneDto>(
            label: localizations.offersfilter_zoneLabel,
            valueToString: (PropertyzoneDto? value) => value?.composedName ?? "",
            onSelect: () => selectPropertyzoneDialog(context, initialSelectedId: formData.zone.vn?.id ?? const None()),
            getValue: () => formData.zone.vn,
            setValue: (PropertyzoneDto? value) {
              this.setState(() => formData.zone = value == null ? None() : Some(value));
              onChanged();
            },
          ),
          SearchFieldEntry<CityDto>(
            label: localizations.offersfilter_cityLabel,
            getValue: () => formData.city.vn,
            setValue: (CityDto? value) {
              this.setState(() => formData.city = value == null ? None() : Some(value));
              onChanged();
            },
            onSearch: (String search) => api.getCities(
              filter: CitiesListFilter(
                search: Some(search),
                usedBy: formData.includeNotMine == True ? Some(CitiesUsageContext.meAndCoworers) : Some(CitiesUsageContext.me),
              ),
              limit: 50,
            ),
            itemBuilder: (context, city, {bool isSelected = false}) => CityListItem(city: city),
            valueToString: (city) => city?.label.vn?.localized ?? localizations.offersfilter_cityEmptyText,
          ),
          SelectFieldEntry<String>(
            label: localizations.offersfilter_typeLabel,
            getValue: () => formData.propertyTypeCode.vn ?? c_any,
            setValue: (String? code) {
              setState(() {
                if (code == null || code == c_any) {
                  formData.propertyTypeCode = None();
                } else {
                  formData.propertyTypeCode = Some(code);
                }
              });
              onChanged();
            },
            options: [
              SelectOption(label: localizations.common_AnyLabel, value: c_any),
              SelectOption(label: localizations.common_HouseLabel, value: "house"),
              SelectOption(label: localizations.common_FlatLabel, value: "flat"),
            ],
          ),
          SimpleFieldEntry<double>(
            label: localizations.offersfilter_m2Label,
            getValue: () => formData.propertyM2Min.vn,
            setValue: (double? v) {
              setState(() {
                formData.propertyM2Min = v == null ? None() : Some(v);
                formData.propertyM2Max = v == null ? None() : Some(v * 1.2);
              });
              onChanged();
            },
            //max: formData.propertyM2Max.vn,
            isM2: true,
          ),
          SelectFieldEntry<String>(
            label: localizations.offersfilter_operationLabel,
            getValue: () {
              if (formData.includeSales == True && formData.includeRents == False)
                return c_sale;
              else if (formData.includeRents == True && formData.includeSales == False)
                return c_rent;
              else
                return c_any;
            },
            setValue: (String? code) {
              setState(() {
                switch (code) {
                  case c_rent:
                    formData.includeRents = True;
                    formData.includeSales = False;
                    formData.saleAmountMin = const None();
                    formData.saleAmountMax = const None();
                    break;
                  case c_sale:
                    formData.includeRents = False;
                    formData.includeSales = True;
                    formData.rentAmountMin = const None();
                    formData.rentAmountMax = const None();
                    break;
                  default:
                    formData.includeRents = True;
                    formData.includeSales = True;
                    formData.saleAmountMin = const None();
                    formData.saleAmountMax = const None();
                    formData.rentAmountMin = const None();
                    formData.rentAmountMax = const None();
                }
              });
              onChanged();
            },
            options: [
              SelectOption(label: localizations.common_AnyLabel, value: c_any),
              SelectOption(label: localizations.common_SaleLabel, value: c_sale),
              SelectOption(label: localizations.common_RentLabel, value: c_rent),
            ],
          ),
          if (formData.includeSales == True && formData.includeRents == False)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_saleAmountLabel,
              getValue: () => formData.saleAmountMax.vn,
              setValue: (double? v) {
                setState(() {
                  formData.saleAmountMax = v == null ? None() : Some(v);
                  formData.saleAmountMin = v == null ? None() : Some(v * 0.8); // Min es 10% menos
                });
                onChanged();
              },
              //min: formData.saleAmountMin.vn ?? 0,
              isCurrency: true,
              isRequired: false,
            ),
          if (formData.includeRents == True && formData.includeSales == False)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_rentAmountLabel,
              getValue: () => formData.rentAmountMax.vn,
              setValue: (double? v) {
                setState(() {
                  formData.rentAmountMax = v == null ? None() : Some(v);
                  formData.rentAmountMin = v == null ? None() : Some(v * 0.8); // Min es 10% menos
                });
                onChanged();
              },
              min: formData.rentAmountMin.vn ?? 0,
              isCurrency: true,
            ),
          if (!isIndividual)
            SimpleFieldEntry<bool>(
                label: localizations.offersfilter_showNotMine,
                getValue: () => formData.includeNotMine.valueOrDefault(false),
                setValue: (bool? v) {
                  setState(() {
                    formData.includeNotMine = v == null ? None() : Some(v);
                  });
                  onChanged();
                }),
        ],
      ),
    ];
  }
}
