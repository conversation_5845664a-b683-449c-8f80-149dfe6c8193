part of 'offers_filter_form_bloc.dart';

abstract class OffersFilterFormState extends Equatable {
  final bool imIndividual;
  final OffersListFilter originalFilter;

  OffersFilterFormState({this.originalFilter = const OffersListFilter(), this.imIndividual = false}) : super();
  @override
  List<Object> get props => [];
}

class OffersFilterFormLoadedState extends OffersFilterFormState {
  final OffersFilterFormModel formData;
  OffersFilterFormLoadedState({
    required this.formData,
    required OffersListFilter originalFilter,
    required bool imIndividual,
  }) : super(originalFilter: originalFilter, imIndividual: imIndividual);

  OffersFilterFormLoadedState copyWith({OffersFilterFormModel? formData}) {
    return OffersFilterFormLoadedState(
        formData: formData ?? this.formData, originalFilter: this.originalFilter, imIndividual: this.imIndividual);
  }

  @override
  List<Object> get props => [formData];
}

class OffersFilterFormFailureState extends OffersFilterFormState {
  final String error;
  OffersFilterFormFailureState({
    required this.error,
    required OffersListFilter originalFilter,
    required bool imIndividual,
  }) : super(originalFilter: originalFilter, imIndividual: imIndividual);
  @override
  List<Object> get props => [error];
}

class OffersListFilterInitState extends OffersFilterFormState {}
