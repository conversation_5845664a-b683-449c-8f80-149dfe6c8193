part of 'offers_filter_form_bloc.dart';

int _id = 0;

abstract class OffersFilterFormEvent extends Equatable {
  const OffersFilterFormEvent();

  @override
  List<Object> get props => [];
}

class OffersFilterFormOnLoadEvent extends OffersFilterFormEvent {
  final _myid = ++_id;

  final OffersListFilter filterModel;

  OffersFilterFormOnLoadEvent({required this.filterModel}) : super();

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}
