import 'dart:async';

//import 'package:topbrokers/cloudoffers_export/offers_filter.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/global/session_bloc.dart';

import 'package:topbrokers/offers_filter_dialog/offers_filter.dart';

part 'offers_filter_form_event.dart';
part 'offers_filter_form_state.dart';

class OffersFilterFormBloc extends Bloc<OffersFilterFormEvent, OffersFilterFormState> {
  final AppModelsNS appModels;
  final SessionBloc globalBloc;

  OffersFilterFormBloc({required this.appModels, required this.globalBloc}) : super(OffersListFilterInitState());

  factory OffersFilterFormBloc.create(BuildContext context) {
    return OffersFilterFormBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      globalBloc: BlocProvider.of<SessionBloc>(context, listen: false),
    );
  }

  @override
  Stream<Transition<OffersFilterFormEvent, OffersFilterFormState>> transformEvents(
    Stream<OffersFilterFormEvent> events,
    TransitionFunction<OffersFilterFormEvent, OffersFilterFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<OffersFilterFormState> mapEventToState(OffersFilterFormEvent event) async* {
    if (event is OffersFilterFormOnLoadEvent) {
      yield* _mapOnLoad(state, event);
    }
  }

  Stream<OffersFilterFormState> _mapOnLoad(OffersFilterFormState state, OffersFilterFormOnLoadEvent event) async* {
    try {
      final filterModel = event.filterModel;

      Optional<PropertyzoneDto> zone = None();

      if (filterModel.containerzoneId is! None) {
        final zones = await appModels.listPropertyzones(
          filter: PropertyzonesListFilter(
            id: filterModel.containerzoneId,
            parentId: const None(),
            includeParent: True,
          ),
          limit: 1,
        );
        if (zones.length != 0) {
          zone = Some(zones[0]);
        }
      }

      Optional<CityDto> city = None();

      if (filterModel.propertyAddrCityCode is! None) {
        final cities = await appModels.listCities(
          filter: CitiesListFilter(code: filterModel.propertyAddrCityCode),
          limit: 1,
        );
        if (cities.length != 0) {
          city = Some(cities[0]);
        }
      }

      Optional<ContactDto> customer = None();
      if (filterModel.customerId is! None) {
        customer = await appModels.readContact(filterModel.customerId.v).then((contact) => contact != null ? Some(contact) : None());
      }

      yield OffersFilterFormLoadedState(
        originalFilter: filterModel,
        imIndividual: globalBloc.state.agent.vn?.isIndividual == True,
        formData: OffersFilterFormModel(
          offerId: filterModel.offerId,
          isVersion: filterModel.isVersion,
          customer: customer,
          customerIsBankServicer: filterModel.customerIsBankServicer,
          propertyTypeCode: filterModel.propertytypeCode,
          propertyM2Min: filterModel.propertyM2Min,
          propertyM2Max: filterModel.propertyM2Max,
          includeRents: filterModel.rentAllowed,
          rentAmountMin: filterModel.rentAmountMin,
          rentAmountMax: filterModel.rentAmountMax,
          includeSales: filterModel.saleAllowed,
          saleAmountMin: filterModel.saleAmountMin,
          saleAmountMax: filterModel.saleAmountMax,
          zone: zone,
          city: city,
          includeNotMine: filterModel.includeNotMine,
        ),
      );
    } on Exception {
      yield OffersFilterFormFailureState(originalFilter: event.filterModel, imIndividual: state.imIndividual, error: "Problemas al obtener datos");
    }
  }
}
