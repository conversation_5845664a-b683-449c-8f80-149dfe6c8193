part of 'demands_filter_form_bloc.dart';

int _id = 0;

abstract class DemandsFilterFormEvent extends Equatable {
  const DemandsFilterFormEvent();

  @override
  List<Object> get props => [];
}

class DemandsFilterFormOnLoadEvent extends DemandsFilterFormEvent {
  final _myid = ++_id;

  final DemandsListFilter filterModel;

  DemandsFilterFormOnLoadEvent({required this.filterModel}) : super();

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}
