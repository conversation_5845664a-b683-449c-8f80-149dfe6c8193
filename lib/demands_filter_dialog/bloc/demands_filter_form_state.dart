part of 'demands_filter_form_bloc.dart';

abstract class DemandsFilterFormState extends Equatable {
  final DemandsListFilter originalFilter;

  DemandsFilterFormState({this.originalFilter=const DemandsListFilter()}) : super();
  @override
  List<Object> get props => [];
}

class DemandsFilterFormLoadedState extends DemandsFilterFormState {
  final DemandsFilterFormModel formData;
  DemandsFilterFormLoadedState({required this.formData, required DemandsListFilter originalFilter})
      : super(originalFilter: originalFilter);

  DemandsFilterFormLoadedState copyWith({DemandsFilterFormModel? formData}) {
    return DemandsFilterFormLoadedState(
      formData: formData ?? this.formData,
      originalFilter: this.originalFilter,
    );
  }

  @override
  List<Object> get props => [formData];
}

class DemandsFilterFormFailureState extends DemandsFilterFormState {
  final String error;
  DemandsFilterFormFailureState({required this.error, required DemandsListFilter originalFilter})
      : super(originalFilter: originalFilter);
  @override
  List<Object> get props => [error];
}

class DemandsListFilterInitState extends DemandsFilterFormState {}
