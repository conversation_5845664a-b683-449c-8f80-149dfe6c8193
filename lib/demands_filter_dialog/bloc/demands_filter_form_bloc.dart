import 'dart:async';

import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/demands_filter_dialog/demands_filter.dart';

part 'demands_filter_form_event.dart';
part 'demands_filter_form_state.dart';

class DemandsFilterFormBloc extends Bloc<DemandsFilterFormEvent, DemandsFilterFormState> {
  final AppModelsNS appModels;

  DemandsFilterFormBloc({required this.appModels}) : super(DemandsListFilterInitState());

  factory DemandsFilterFormBloc.create(BuildContext context) {
    return DemandsFilterFormBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
    );
  }

  @override
  Stream<Transition<DemandsFilterFormEvent, DemandsFilterFormState>> transformEvents(
    Stream<DemandsFilterFormEvent> events,
    TransitionFunction<DemandsFilterFormEvent, DemandsFilterFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<DemandsFilterFormState> mapEventToState(DemandsFilterFormEvent event) async* {
    if (event is DemandsFilterFormOnLoadEvent) {
      yield* _mapOnLoad(state, event);
    }
  }

  Stream<DemandsFilterFormState> _mapOnLoad(
      DemandsFilterFormState state, DemandsFilterFormOnLoadEvent event) async* {
    try {
      final filterModel = event.filterModel;

      Optional<CityDto> city = None();

      if (filterModel.propertyAddrCityCode is! None) {
        final cities = await appModels.listCities(
          filter: CitiesListFilter(code: filterModel.propertyAddrCityCode),
          limit: 1,
        );
        if (cities.length != 0) {
          city = Some(cities[0]);
        }
      }

      yield DemandsFilterFormLoadedState(
        originalFilter: filterModel,
        formData: DemandsFilterFormModel(
          propertyTypeCode: filterModel.propertytypeCode,
          propertyM2Min: filterModel.propertyM2Min,
          propertyM2Max: filterModel.propertyM2Max,
          includeRents: filterModel.rentAllowed,
          rentAmountMin: filterModel.rentAmountMin,
          rentAmountMax: filterModel.rentAmountMax,
          includeSales: filterModel.saleAllowed,
          saleAmountMin: filterModel.saleAmountMin,
          saleAmountMax: filterModel.saleAmountMax,
          city: city,
        ),
      );
    } on Exception {
      yield DemandsFilterFormFailureState(originalFilter: event.filterModel, error: "Problemas al obtener datos");
    }
  }
}
