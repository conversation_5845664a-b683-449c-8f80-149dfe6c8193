import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class DemandsFilterFormModel {
  
  Optional<String> propertyTypeCode;
  Optional<double> propertyM2Min;
  Optional<double> propertyM2Max;
  Optional<bool> includeSales;
  Optional<double> saleAmountMin;
  Optional<double> saleAmountMax;
  Optional<bool> includeRents;
  Optional<double> rentAmountMin;
  Optional<double> rentAmountMax;
  Optional<CityDto> city;
  
  DemandsFilterFormModel({
  
    this.propertyTypeCode = const None(),
    this.propertyM2Min = const None(),
    this.propertyM2Max = const None(),
    this.includeRents = const None(),
    this.rentAmountMin = const None(),
    this.rentAmountMax = const None(),
    this.includeSales = const None(),
    this.saleAmountMin = const None(),
    this.saleAmountMax = const None(),
    this.city = const None(),
  });
}
