import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/demands_filter_dialog/demands_filter.dart';
import 'package:topbrokers/offer_edit/widgets/city_list_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';


class DemandsFilterForm extends StatefulWidget {
  final DemandsListFilter actualFilter;
  //final void Function(OpportunitiesListFilter changedFilter) onAccept;
  final void Function(DemandsListFilter changedFilter) onChanged;

  DemandsFilterForm({
    Key? key,
    required this.actualFilter,
    required this.onChanged,
  }) : super(key: key ?? AgentorKeys.offersFilterPage);

  @override
  _DemandsFilterFormState createState() => _DemandsFilterFormState();
}

class _DemandsFilterFormState extends State<DemandsFilterForm> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  //OffersFilterFormModel _formData;

  _DemandsFilterFormState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));
    late DemandsFilterFormBloc _formBloc;

    return BlocProvider<DemandsFilterFormBloc>(
      create: (context) {
        _formBloc = DemandsFilterFormBloc.create(context)
          ..add(DemandsFilterFormOnLoadEvent(filterModel: widget.actualFilter));
        return _formBloc;
      },
      child: BlocConsumer<DemandsFilterFormBloc, DemandsFilterFormState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          if (state is DemandsFilterFormFailureState) {
            return Center(
              child: Text(localizations.offersfilter_errLoading),
            );
          } else if (state is DemandsFilterFormLoadedState) {
            //this._formData = state.formData;
            return SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  _buildForm(context, state.formData, () {
                    widget.onChanged(_formDataToListFilter(state.formData, state.originalFilter));
                  }),
                ],
              ),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, DemandsFilterFormModel formData, void Function() onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.max,
      children: [
        _formEntries(context, formData, onChanged).buildForm(key: _formKey, context: context),
      ],
    );
  }

  DemandsListFilter _formDataToListFilter(
    DemandsFilterFormModel formData,
    DemandsListFilter originalFilter,
  ) =>
      originalFilter.copyWith(
        propertytypeCode: formData.propertyTypeCode,
        propertyM2Min: formData.propertyM2Min,
        propertyM2Max: formData.propertyM2Max,
        rentAllowed: formData.includeRents,
        rentAmountMin: formData.rentAmountMin,
        rentAmountMax: formData.rentAmountMax,
        saleAllowed: formData.includeSales,
        saleAmountMin: formData.saleAmountMin,
        saleAmountMax: formData.saleAmountMax,
        propertyAddrCityCode: formData.city.vn?.code ?? const None(),
      );

  List<Widget> _formEntries(BuildContext context, DemandsFilterFormModel formData, Function() onChanged) {
    const c_any = "any";
    const c_rent = "rent";
    const c_sale = "sale";

    final localizations = context.getAppLocalizationsOrThrow();

    return [
      GroupEntry(
        //label: "Datos del anuncio",
        isSubgroup: true,
        children: [
          
          SearchFieldEntry<CityDto>(
            label: localizations.offersfilter_cityLabel,
            getValue: () => formData.city.vn,
            setValue: (CityDto? value) {
              this.setState(() => formData.city = value == null ? None() : Some(value));
              onChanged();
            },
            onSearch: (String search) => api.getCities(
              filter: CitiesListFilter(
                search: Some(search),
                usedBy: Some(CitiesUsageContext.me),
              ),
              limit: 50,
            ),
            itemBuilder: (context, city, {bool isSelected = false}) => CityListItem(city: city),
            valueToString: (city) => city?.label.vn?.localized ?? localizations.offersfilter_cityEmptyText,
          ),
          SelectFieldEntry<String>(
            label: localizations.offersfilter_typeLabel,
            getValue: () => formData.propertyTypeCode.vn ?? c_any,
            setValue: (String? code) {
              setState(() {
                if (code == null || code == c_any) {
                  formData.propertyTypeCode = None();
                } else {
                  formData.propertyTypeCode = Some(code);
                }
              });
              onChanged();
            },
            options: [
              SelectOption(label: localizations.common_AnyLabel, value: c_any),
              SelectOption(label: localizations.common_HouseLabel, value: "house"),
              SelectOption(label: localizations.common_FlatLabel, value: "flat"),
            ],
          ),
          SimpleFieldEntry<double>(
            label: localizations.offersfilter_m2Label,
            getValue: () => formData.propertyM2Min.vn,
            setValue: (double? v) {
              setState(() {
                formData.propertyM2Min = v == null ? None() : Some(v);
                formData.propertyM2Max = v == null ? None() : Some(v * 1.2);
              });
              onChanged();
            },
            //max: formData.propertyM2Max.vn,
            isM2: true,
          ),
          /*SimpleFieldEntry<double>(
            label: localizations.offersfilter_maxM2Label,
            getValue: () => formData.propertyM2Max.vn,
            setValue: (double? v) {
              setState(() {
                formData.propertyM2Max = v == null ? None() : Some(v);
              });
              onChanged();
            },
            min: formData.propertyM2Min.vn ?? 0,
            isM2: true,
          ),*/
          SelectFieldEntry<String>(
            label: localizations.offersfilter_operationLabel,
            getValue: () {
              if (formData.includeSales == True && formData.includeRents == False)
                return c_sale;
              else if (formData.includeRents == True && formData.includeSales == False)
                return c_rent;
              else
                return c_any;
            },
            setValue: (String? code) {
              setState(() {
                switch (code) {
                  case c_rent:
                    formData.includeRents = True;
                    formData.includeSales = False;
                    formData.saleAmountMin = const None();
                    formData.saleAmountMax = const None();
                    break;
                  case c_sale:
                    formData.includeRents = False;
                    formData.includeSales = True;
                    formData.rentAmountMin = const None();
                    formData.rentAmountMax = const None();
                    break;
                  default:
                    formData.includeRents = True;
                    formData.includeSales = True;
                    formData.saleAmountMin = const None();
                    formData.saleAmountMax = const None();
                    formData.rentAmountMin = const None();
                    formData.rentAmountMax = const None();
                }
              });
              onChanged();
            },
            options: [
              SelectOption(label: localizations.common_AnyLabel, value: c_any),
              SelectOption(label: localizations.common_SaleLabel, value: c_sale),
              SelectOption(label: localizations.common_RentLabel, value: c_rent),
            ],
          ),
          /*MultiSelectFieldEntry<String>(
            label: localizations.offersfilter_operationLabel,
            getValue: () => [
              if (formData.includeSales == True) c_sale,
              if (formData.includeRents == True) c_rent,
            ],
            setValue: (values) {
              if (values != null)
                setState(() {
                  if (values.indexOf(c_rent) != -1) {
                    formData.includeRents = True;
                  } else {
                    formData.includeRents = False;
                    formData.rentAmountMin = const None();
                    formData.rentAmountMax = const None();
                  }
                  if (values.indexOf(c_sale) != -1) {
                    formData.includeSales = True;
                  } else {
                    formData.includeSales = False;
                    formData.saleAmountMin = const None();
                    formData.saleAmountMax = const None();
                  }
                });
              onChanged();
            },
            options: [
              SelectOption(label: localizations.common_SaleLabel, value: c_sale),
              SelectOption(label: localizations.common_RentLabel, value: c_rent),
            ],
            isRequired: true,
          ),*/
          /*if (formData.includeSales == True && formData.includeRents == False)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_saleMinAmountLabel,
              getValue: () => formData.saleAmountMin.vn,
              setValue: (double? v) {
                setState(() {
                  formData.saleAmountMin = v == null ? None() : Some(v);
                });
                onChanged();
              },
              max: formData.saleAmountMax.vn,
              isCurrency: true,
            ),*/
          if (formData.includeSales == True && formData.includeRents == False)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_saleAmountLabel,
              getValue: () => formData.saleAmountMax.vn,
              setValue: (double? v) {
                setState(() {
                  formData.saleAmountMax = v == null ? None() : Some(v);
                  formData.saleAmountMin = v == null ? None() : Some(v * 0.8); // Min es 10% menos
                });
                onChanged();
              },
              //min: formData.saleAmountMin.vn ?? 0,
              isCurrency: true,
              isRequired: false,
            ),
          /*if (formData.includeRents == True && formData.includeSales == False)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_rentMinAmountLabel,
              getValue: () => formData.rentAmountMin.vn,
              setValue: (double? v) {
                setState(() {
                  formData.rentAmountMin = v == null ? None() : Some(v);
                });
                onChanged();
              },
              max: formData.rentAmountMax.vn,
              isCurrency: true,
            ),*/
          if (formData.includeRents == True && formData.includeSales == False)
            SimpleFieldEntry<double>(
              label: localizations.offersfilter_rentAmountLabel,
              getValue: () => formData.rentAmountMax.vn,
              setValue: (double? v) {
                setState(() {
                  formData.rentAmountMax = v == null ? None() : Some(v);
                  formData.rentAmountMin = v == null ? None() : Some(v * 0.8); // Min es 10% menos
                });
                onChanged();
              },
              min: formData.rentAmountMin.vn ?? 0,
              isCurrency: true,
            ),
        ],
      ),
    ];
  }
}
