// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get app_title => 'Percent';

  @override
  String get app_err_loading => 'No se ha podido iniciar la aplicación';

  @override
  String get common_and => 'y';

  @override
  String get common_add => 'añadir';

  @override
  String get common_edit => 'editar';

  @override
  String get common_accept => 'aceptar';

  @override
  String get common_Accept => 'Aceptar';

  @override
  String get common_acceptChanges => 'aceptar cambios';

  @override
  String get common_create => 'crear';

  @override
  String get common_continue => 'continuar';

  @override
  String get common_Continue => 'Continuar';

  @override
  String get common_cancel => 'cancelar';

  @override
  String get common_Cancel => 'Cancelar';

  @override
  String get common_close => 'cerrar';

  @override
  String get common_Close => 'Cerrar';

  @override
  String get common_retry => 'reintentar';

  @override
  String get common_Retry => 'Reintentar';

  @override
  String get common_errLoading => 'Problemas obteniendo datos';

  @override
  String get common_formWithErrors =>
      'El formulario contiene errores. Revíselo.';

  @override
  String get common_forSale => 'en venta';

  @override
  String get common_forRent => 'en alquiler';

  @override
  String get common_sale => 'venta';

  @override
  String get common_rent => 'alquiler';

  @override
  String get common_marketPrice => 'precio de mercado';

  @override
  String get common_try_again => 'Intentar de nuevo';

  @override
  String get common_see_more => 'Ver más';

  @override
  String get common_selectAContact => 'Seleccione un contacto';

  @override
  String get common_selectAnOffer => 'Seleccione una oferta';

  @override
  String get common_AnyLabel => 'Cualquiera';

  @override
  String get common_NotMatterLabel => 'Indiferente';

  @override
  String get common_YesLabel => 'Sí';

  @override
  String get common_NotLabel => 'No';

  @override
  String get common_HouseLabel => 'Casa';

  @override
  String get common_FlatLabel => 'Piso';

  @override
  String get common_SaleLabel => 'Venta';

  @override
  String get common_RentLabel => 'Alquiler';

  @override
  String get common_detailsLabel => 'detalles';

  @override
  String get common_msg_urlCopiedToClipboard => 'Url copiada al portapapeles';

  @override
  String get common_err_unexpected => 'Problema inesperado';

  @override
  String get common_err_unexpected_Tryagain =>
      'Problema inesperado, Inténtelo más tarde.';

  @override
  String common_err_unexpected_Msg(String message) {
    return 'Problema inesperado: $message';
  }

  @override
  String get landing_logIn => 'Iniciar sesión';

  @override
  String get landing_signUp => 'Registrate';

  @override
  String get landing_legalNotice => 'Aviso Legal';

  @override
  String get login_emailLabel => 'Dirección de email';

  @override
  String get login_passwordLabel => 'Contraseña';

  @override
  String get login_signinBtn => 'acceder';

  @override
  String get login_forgotPasswordLabel => '¿Has olvidado tu contraseña?';

  @override
  String get login_recoverPasswordBtn => 'Recupérala';

  @override
  String get login_noAccountLabel => '¿Aún no tienes una cuenta Topbrokers?';

  @override
  String get invalid_email => 'No es un email correcto';

  @override
  String get signup_invalid_domain => 'Solo se permiten emails agenteunico.net';

  @override
  String get login_signUpBtn => 'Regístrate';

  @override
  String get signup_title => 'Registro';

  @override
  String get signup_onetimecodeLabel =>
      'Introduce el token para acceder al registro';

  @override
  String get signup_onetimecodeHelper =>
      'El código que te han dado para registrarte.';

  @override
  String get signup_onetimecodeError => 'No es el código correcto';

  @override
  String get signup_validationcodeLabel =>
      'Introduce el código que hemos enviado por eMail';

  @override
  String get signup_validationcodeHelper =>
      'Si no has recibido nada, revisa tu carpeta de spam.';

  @override
  String get signup_resendValidationCodeBtn => 'Reenviar código de validación';

  @override
  String get signup_fieldsBlocTitle => 'Datos de registro';

  @override
  String get signup_passwordLabel => 'Contraseña';

  @override
  String get signup_password2Label => 'Contraseña (repetida)';

  @override
  String get signup_password2Error => 'Debe coincidir con \'Contraseña\'';

  @override
  String signup_acceptEulaTitle(String eulaUrl, String privacyPolicyUrl) {
    return 'He leído y acepto las <a href=\'$eulaUrl\'>condiciones de uso</a> y la <a href=\'$privacyPolicyUrl\'>política de privacidad</a>';
  }

  @override
  String signup_welcomeTitle(String name) {
    return 'Hola $name';
  }

  @override
  String get signup_welcomeText => 'bienvenido a Percent App';

  @override
  String get signup_loginBtn => 'iniciar sesión';

  @override
  String get signup_err_emailalredyexists => 'El email introducido ya existe';

  @override
  String get signup_err_validationcodeinvalid =>
      'El código de validación no existe o ha expirado';

  @override
  String get signup_err_onetimecodeinvalid => 'El código inicial no es válido';

  @override
  String get home_tabs_search => 'buscar';

  @override
  String get home_tabs_favorites => 'favoritos';

  @override
  String get home_tabs_alerts => 'alertas';

  @override
  String get home_tabs_offers => 'offertas';

  @override
  String get home_tabs_contacts => 'contactos';

  @override
  String get home_tabs_actions => 'acciones';

  @override
  String get offer_errLoading => 'Problemas al cargar datos';

  @override
  String get offer_errDoesnExist => 'La oferta no existe';

  @override
  String get offer_favouriteLabel => 'Favorito';

  @override
  String get offer_srcadHeading => 'Anuncio origen';

  @override
  String get offer_srcad_updatedAtLabel => 'Última actualización';

  @override
  String get offer_srcad_advertiserLabel => 'Anunciante';

  @override
  String get offer_srcad_individualLabel => 'Particular';

  @override
  String get offer_srcad_nameLabel => 'Nombre';

  @override
  String get offer_srcad_webpageLabel => 'Página web';

  @override
  String get offer_srcad_referenceLabel => 'Ref.';

  @override
  String get offer_mainHeading => 'Datos principales';

  @override
  String get offer_agentHeading => 'Agente';

  @override
  String get offer_ownerHeading => 'Propietario';

  @override
  String get offer_picturesHeading => 'Fotos';

  @override
  String get offer_addressHeading => 'Ubicación';

  @override
  String get offer_featuresHeading => 'Características';

  @override
  String get offer_descriptionHeading => 'Descripción';

  @override
  String get offer_internalsHeading => 'Internos';

  @override
  String get offer_actionsHeading => 'Acciones';

  @override
  String get offer_matchingsHeading => 'Cruces (alertas)';

  @override
  String get offer_publicationsHeading => 'Publicar en portales';

  @override
  String get offer_cantPublishDisclaimer =>
      'La oferta necesita estar en estado de <b>commercialización</b> para que la puedas publicar en un portal inmobiliario.<br/>Por favor, editala (botón lápiz) y cambia su estado primero.';

  @override
  String get offer_sharingsHeading => 'Compartir con';

  @override
  String get offer_createBtn => 'Crear oferta';

  @override
  String get offer_saveBtn => 'Aceptar cambios';

  @override
  String get offer_titleNew => 'Nueva oferta';

  @override
  String get offer_title => 'Oferta';

  @override
  String get offer_stateDialog_title => 'Cambio de estado';

  @override
  String get offer_stateDialog_bodyHTML =>
      '<p>Sólo las ofertas en comercialización pueden publicarse en portales inmobiliarios o compartirse en grupos de trabajo.<br/>Si continúa, la oferta se retirará de todos los portales y grupos de trabajo en los que esté publicada.</p><p>¿Desea continuar?</p>';

  @override
  String get offer_stateDialog_yes => 'Sí, continuar';

  @override
  String get offer_stateDialog_not => 'No, cancelar';

  @override
  String get offers_errLoading => 'Problemas obteniendo las ofertas';

  @override
  String get offers_noOffers => 'No hay ofertas';

  @override
  String get offers_addFirstOffer => 'Añadir oferta';

  @override
  String offers_item_surfaceM2(int total) {
    return '${total}m²';
  }

  @override
  String offers_item_bethroomsCount(int total) {
    return '$total habitaciones';
  }

  @override
  String get offers_item_exteriorTxt => 'exterior';

  @override
  String get offersfilter_errLoading => 'Problemas al cargar datos';

  @override
  String get offersfilter_sourceContactPhoneLabel => 'Teléfono de contacto';

  @override
  String get offersfilter_isVersionLabel => '¿Versiones?';

  @override
  String get offersfilter_isVersion_yes => 'Solo versiones';

  @override
  String get offersfilter_isVersion_not => 'Solo ofertas principales';

  @override
  String get offersfilter_customerTypeLabel => 'Tipo de propietario';

  @override
  String get offersfilter_customerLabel => 'Propietario';

  @override
  String get offersfilter_zoneLabel => 'Zona';

  @override
  String get offersfilter_cityLabel => 'Ciudad';

  @override
  String get offersfilter_cityEmptyText => 'Seleccione una ciudad';

  @override
  String get offersfilter_typeLabel => 'Tipo';

  @override
  String get offersfilter_operationLabel => 'Operación';

  @override
  String get offersfilter_m2Label => 'Superficie';

  @override
  String get offersfilter_minM2Label => 'Superficie mín';

  @override
  String get offersfilter_maxM2Label => 'Superficie máx';

  @override
  String get offersfilter_saleAmountLabel => 'Precio';

  @override
  String get offersfilter_rentAmountLabel => 'Alquiler';

  @override
  String get offersfilter_saleMinAmountLabel => 'Precio mín';

  @override
  String get offersfilter_saleMaxAmountLabel => 'Precio máx';

  @override
  String get offersfilter_rentMinAmountLabel => 'Alquiler mín';

  @override
  String get offersfilter_rentMaxAmountLabel => 'Alquiler máx';

  @override
  String get offersfilter_showNotMine =>
      'Mostrar oportunidades de otros agentes';

  @override
  String get cloudoffers_filter_favoritesOnlyLabel => 'Sólo favoritos';

  @override
  String get cloudoffers_filter_includeIndividualLabel => 'Particulares';

  @override
  String get cloudoffers_filter_includeProfessionalLabel => 'Profesionales';

  @override
  String get cloudoffers_filter_sourceContactPhoneLabel =>
      'Teléfono de contacto';

  @override
  String get cloudoffers_filter_publicationDateLabel => 'Publication date';

  @override
  String get cloudoffers_filter_publicationDate_manually =>
      'Indicar manualmente';

  @override
  String get cloudoffers_filter_publicationDate_last48h => 'Últimas 48h';

  @override
  String get cloudoffers_filter_publicationDate_thisWeek => 'Esta semana';

  @override
  String get cloudoffers_filter_publicationDate_lastWeek => 'Semana pasada';

  @override
  String get cloudoffers_filter_publicationDate_thisMonth => 'Este mes';

  @override
  String get cloudoffers_filter_publicationDate_lastMonth => 'Mes pasado';

  @override
  String get cloudoffers_filter_publicationDate_hint => 'Cualquiera';

  @override
  String get cloudoffers_filter_publicationDate_from => 'Desde';

  @override
  String get cloudoffers_filter_publicationDate_to => 'Hasta';

  @override
  String get cloudoffers_import_addNotExistsLabel => 'El anuncio ya no existe';

  @override
  String get cloudoffers_import_waitingForLastVersionLabel =>
      'Obteniendo versión actualizada del anuncio';

  @override
  String get cloudoffers_import_newPropertyOwnerLabel =>
      'Propietario del nuevo inmueble';

  @override
  String get cloudoffers_import_createOfferLabel => 'Crear oferta';

  @override
  String get cloudoffers_import_creatingOfferLabel => 'Creando oferta';

  @override
  String get cloudoffers_import_doneLabel => 'Oferta creada corréctamente';

  @override
  String get cloudoffers_import_seeDetailsLabel => 'Ver detalles';

  @override
  String get cloudoffers_import_err_importing =>
      'Problemas durante la importanción';

  @override
  String get cloudoffers_refresh_doneLabel => 'Datos del anuncio actualizados';

  @override
  String get cloudoffers_refresh_errLabel =>
      'Problems durante la actualización';

  @override
  String get cloudoffers_export_reportLabel => 'Qué quieres exportar';

  @override
  String get cloudoffers_export_formatLabel => 'En qué formato';

  @override
  String get cloudoffers_export_maxrowsLabel => 'Cuántas filas (como máximo)';

  @override
  String get cloudoffers_export_exportBtn => 'Exportar';

  @override
  String get cloudoffers_export_retrievingRows => 'Obteniendo filas';

  @override
  String cloudoffers_export_retrievedCount(int count) {
    return 'Total: $count';
  }

  @override
  String cloudoffers_export_generatingFile(int count) {
    return 'Generando fichero para $count filas';
  }

  @override
  String get cloudoffers_export_done_titleLabel => '¡¡ Hecho !!';

  @override
  String cloudoffers_export_done_totalrowsLabel(int count) {
    return 'Exportadas: $count filas';
  }

  @override
  String get contactsfilter_errLoading => 'Problemas al cargar datos';

  @override
  String get contactsfilter_searchLabel => '¿Texto de búsqueda';

  @override
  String get contactsfilter_isOfferCustomerLabel => '¿Es ofertante?';

  @override
  String get contactsfilter_isBankServicerLabel => '¿Es Bank Servicer?';

  @override
  String get contactsfilter_hasSiteLabel =>
      '¿Tiene site (tinder.topbroekes.io)?';

  @override
  String get contact_titleNew => 'Nuevo contacto';

  @override
  String get contact_title => 'Contacto';

  @override
  String get contact_errLoading =>
      'Problemas leyendo la información de el contacto';

  @override
  String get contact_errDoesnExist => 'El contacto no existe';

  @override
  String get contact_createBtn => 'Crear contacto';

  @override
  String get contact_saveBtn => 'Aceptar cambios';

  @override
  String get contact_nameLabel => 'Nombre';

  @override
  String get contact_lastnameLabel => 'Apellidos';

  @override
  String get contact_emailLabel => 'eMail';

  @override
  String get contact_mobileLabel => 'Teléfono (móvil)';

  @override
  String get contact_siteLabel => 'Sitio del contacto (tinder.topbrokers.io)';

  @override
  String get contact_notesLabel => 'Notas';

  @override
  String get contact_offersLabel => 'Ofertas';

  @override
  String get contact_addOfferBtn => 'nueva';

  @override
  String get contact_demandsLabel => 'Alertas';

  @override
  String get contact_addDemandBtn => 'nueva';

  @override
  String get contact_actionsLabel => 'Acciones';

  @override
  String get contact_addActionBtn => 'nueva';

  @override
  String get agent_titleNew => 'Nuevo agente';

  @override
  String get agent_title => 'Agente';

  @override
  String get agent_errLoading => 'Problemas leyendo la información del agente';

  @override
  String get agent_errDoesnExist => 'El agente no existe';

  @override
  String get agent_saveBtn => 'Aceptar cambios';

  @override
  String get agent_nameLabel => 'Nombre';

  @override
  String get agent_lastnameLabel => 'Apellidos';

  @override
  String get agent_emailLabel => 'eMail';

  @override
  String get agent_mobileLabel => 'Teléfono (móvil)';

  @override
  String get recoverpass_title => 'Recuperar contraseña';

  @override
  String get recoverpass_validationcodeHelper =>
      'Si no has recibido nada, es posible que la dirección indicada no exté asociada a ningún usuario o que el email esté en su carpeta de spam.';

  @override
  String get recoverpass_finalMessageText => 'Contraseña cambiada';

  @override
  String get demand_titleNew => 'Nueva alerta';

  @override
  String get demands_errLoading => 'Problemas obteniendo las alertas';

  @override
  String get demands_noDemands => 'No hay alertas';

  @override
  String get demands_addFirstDemand => 'Registra una alerta';

  @override
  String get demands_problems => 'Problemas obteniendo demandas';

  @override
  String get demands_notFound => 'No se han encontrado alertas';

  @override
  String get demands_register => 'Registra una alerta';

  @override
  String get opportunities_errLoading =>
      'Problemas obteniendo las oportunidades';

  @override
  String get opportunities_noOpportunities => 'No hay oportunidades';

  @override
  String get opportunities_noOffers => 'No hay ofertas';

  @override
  String get opportunities_noDemands => 'No hay alertas';

  @override
  String get opportunities_noOffersOrDemands => 'No hay ofertas ni alertas';

  @override
  String get opportunities_addFirstOffer => 'Registra una Oferta';

  @override
  String get opportunities_addFirstDemand => 'Registra una Demanda';

  @override
  String get matchings_errLoading => 'Problemas obteniendo cruces del servidor';

  @override
  String get matchings_noMatchings => 'No hay cruces';

  @override
  String get action_titleNew => 'Nueva acción';

  @override
  String get action_title => 'Acción';

  @override
  String get action_mainHeading => 'Datos principales';

  @override
  String get action_typeLabel => 'Tipo';

  @override
  String get action_whenLabel => 'Cuándo';

  @override
  String get action_doneLabel => 'Hecho';

  @override
  String get action_contactLabel => 'Con quién';

  @override
  String get action_offerLabel => 'Inmueble';

  @override
  String get action_notesLabel => 'Notas';

  @override
  String get actions_errLoading => 'Problemas obteniendo acciones del servidor';

  @override
  String get actions_noActions => 'No hay acciones';

  @override
  String get actions_noActionsYet => 'No se han encontrado acciones';

  @override
  String get actions_addFirstAction => 'Registra una acción';

  @override
  String get actions_filterTooltip => 'Filtro';

  @override
  String get actions_filter_todayLabel => 'Hoy';

  @override
  String get actions_filter_fromTodayLabel => 'Desde hoy';

  @override
  String get actions_filter_untilTodayLabel => 'Hasta hoy';

  @override
  String get actions_filter_doneLabel => 'Hechas';

  @override
  String get actions_filter_undoneLabel => 'Pendientes';

  @override
  String get actions_filter_doneAndUndoneLabel => 'Hechas y pendientes';

  @override
  String get serviceaction_titleNew => 'Superpoder';

  @override
  String get serviceaction_title => 'Superpoder';

  @override
  String get serviceaction_typeLabel => 'Tipo';

  @override
  String get serviceaction_whenLabel => 'Cuándo';

  @override
  String get serviceaction_doneLabel => 'Hecho';

  @override
  String get serviceaction_contactLabel => 'Contacto';

  @override
  String get serviceaction_offerLabel => 'Inmueble';

  @override
  String get serviceaction_notesLabel => 'Notas';

  @override
  String get offer_cloneDialog_title => 'Crear versión de oferta';

  @override
  String offer_cloneDialog_message(int offer_id) {
    return '¿Desea crear una versión de la oferta $offer_id ?';
  }

  @override
  String get offer_cloneDialog_yes => 'Sí, continuar';

  @override
  String get offer_cloneDialog_not => 'No, cancelar';

  @override
  String get offer_cloneDialog_type_message => 'Seleccione el tipo de versión';

  @override
  String get offer_cloneDialog_menu_option => 'Crear versión';

  @override
  String get offer_versionHeading => 'Datos de la versión';

  @override
  String get common_versionOf => 'Oferta origen';
}
