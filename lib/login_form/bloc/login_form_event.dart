part of 'login_form_bloc.dart';

abstract class LoginFormEvent extends Equatable {
  const LoginFormEvent();

  @override
  List<Object> get props => [];
}

class LoginFormOnLoad extends LoginFormEvent {
  LoginFormOnLoad() : super();
}

class LoginFormOnSignin extends LoginFormEvent {
  final String username;
  final String password;
  LoginFormOnSignin({required this.username, required this.password}) : super();
}
