part of 'login_form_bloc.dart';

int _counter = 0;
class LoginFormState extends Equatable {
  final _instanceId = ++_counter;
  @override
  List<Object> get props => [_instanceId];
}

class LoginFormInitialState extends LoginFormState {}

class LoginFormLoadedState extends LoginFormState {}

class LoginFormSuccessState extends LoginFormState {}

class LoginFormFailureState extends LoginFormState {
  final String error;

  LoginFormFailureState({required this.error});

  @override
  List<Object> get props => [_instanceId, error];
}
