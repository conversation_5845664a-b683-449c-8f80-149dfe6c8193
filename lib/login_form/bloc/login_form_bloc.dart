import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/global/session_bloc.dart';

part 'login_form_event.dart';
part 'login_form_state.dart';

///
/// Gestiona estados y eventos para el formulario de login [login_form]
/// Además de su trabajo interno con el formulario, notifica a [SessionBloc]
/// cuándo el usuario ha realizado un log-in para que la aplicación actúen en consecuencia
///

class LoginFormBloc extends Bloc<LoginFormEvent, LoginFormState> {
  final api = Deps.solve<ApiServices>();

  /// Bloque global al que se notificarán los eventos de usuario autenticado o no autenticado
  final SessionBloc _globalBloc;

  LoginFormBloc({required globalBloc})
      : this._globalBloc = globalBloc,
        super(LoginFormState());

  @override
  Stream<Transition<LoginFormEvent, LoginFormState>> transformEvents(
    Stream<LoginFormEvent> events,
    TransitionFunction<LoginFormEvent, LoginFormState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<LoginFormState> mapEventToState(LoginFormEvent event) async* {
    if (event is LoginFormOnLoad) {
      yield* _mapOnLoad(state, event);
    } else if (event is LoginFormOnSignin) {
      yield* _mapOnSignin(state, event);
    }
  }

  Stream<LoginFormState> _mapOnLoad(LoginFormState state, LoginFormOnLoad event) async* {
    yield LoginFormLoadedState();
  }

  Stream<LoginFormState> _mapOnSignin(LoginFormState state, LoginFormOnSignin event) async* {
    try {
      final token = await _authenticate(event.username, event.password);
      yield LoginFormSuccessState();
      _globalBloc.add(SessionOnLoggedIn(token: token));     
    } on ApiUnauthorized {
      yield LoginFormFailureState(error: "Usuario o Contraseña no son válidos");
    } on ApiException {
      yield LoginFormFailureState(error: "Autenticación fallida");
    } on Exception {
      yield LoginFormFailureState(error: "Problemas durante la autenticación. Inténtelo más tarde.");
    }
  }

  Future<String> _authenticate(String username, String password) async {
    return await api.createToken(username, password);
  }
}
