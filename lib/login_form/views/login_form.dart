import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/login_form/bloc/login_form_bloc.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class LoginForm extends StatefulWidget {
  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final GlobalKey<FormState> _key = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _emailController = TextEditingController();
  bool _autoValidate = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginFormBloc, LoginFormState>(
      listener: (context, state) {
        if (state is LoginFormFailureState) {
          context.showError(state.error);
        }
      },
      child: BlocBuilder<LoginFormBloc, LoginFormState>(
        builder: (context, state) {
          if (state is LoginFormInitialState) {
            return Center(
              child: CircularProgressIndicator(),
            );
          } else {
            final localizations = context.getAppLocalizationsOrThrow();
            return Form(
              key: _key,
              autovalidateMode: _autoValidate ? AutovalidateMode.onUserInteraction : AutovalidateMode.disabled,
              child: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.login_emailLabel,
                        ),
                        controller: _emailController,
                        validator: MyTextInputValidators.isEmail(allowEmpty: false),
                        keyboardType: TextInputType.emailAddress,
                        autocorrect: false,
                      ),
                      TextFormField(
                        decoration: InputDecoration(
                          labelText: localizations.login_passwordLabel,
                        ),
                        obscureText: true,
                        controller: _passwordController,
                        validator: MyTextInputValidators.notEmpty(),
                      ),
                      const SizedBox(
                        height: 42,
                      ),
                      ElevatedButton(
                        style: ButtonStyle(
                          padding: WidgetStateProperty.all<EdgeInsetsGeometry>(EdgeInsets.all(2)),
                        ),
                        child: Text(context.apploc.login_signinBtn),
                        onPressed: state is LoginFormInitialState
                            ? () {}
                            : () {
                                final formState = _key.currentState;
                                if (formState != null) {
                                  if (formState.validate())
                                    BlocProvider.of<LoginFormBloc>(context).add(LoginFormOnSignin(
                                      username: _emailController.text,
                                      password: _passwordController.text,
                                    ));
                                  else
                                    setState(() {
                                      _autoValidate = true;
                                    });
                                }
                              },
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      Row(children: [
                        Text(context.apploc.login_forgotPasswordLabel),
                        const SizedBox(width: 8),
                        TextButton(
                          child: Text(context.apploc.login_recoverPasswordBtn),
                          onPressed: () {
                            Navigator.pushNamed(context, AgentorRoutes.recoverPass);
                          },
                        ),
                      ]),
                      const SizedBox(
                        height: 12,
                      ),
                      Row(children: [
                        Text(context.apploc.login_noAccountLabel),
                        const SizedBox(width: 8),
                        TextButton(
                          child: Text(context.apploc.login_signUpBtn),
                          onPressed: () {
                            Navigator.pushNamed(context, AgentorRoutes.signUp);
                          },
                        ),
                      ]),
                    ],
                  ),
                ),
              ),
            );
          }
        },
      ),
    );
  }
}
