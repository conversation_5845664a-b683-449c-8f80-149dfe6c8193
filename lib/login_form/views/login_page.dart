import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/global/session_bloc.dart';
import 'package:topbrokers/login_form/bloc/login_form_bloc.dart';
import 'package:topbrokers/login_form/views/login_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart'; // Add this line

class LoginPage extends StatelessWidget {
  const LoginPage({Key ? key}) : super(key: key ?? AgentorKeys.loginPage);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<LoginFormBloc>(
      create: (context) => LoginFormBloc(globalBloc: BlocProvider.of<SessionBloc>(context)),
      child: Scaffold(
        appBar: AppBar(
          title: Image(
            image: AssetImage('images/topbrokers_io_white_174x22.png'),
            //fit: BoxFit.contain,
            alignment: FractionalOffset.centerLeft,
            height: 22,
            width: 174,
            //width: CONTENT_Width, //double.infinity,
            filterQuality: FilterQuality.high,
          ),
          centerTitle: true,
          actions: [],
        ),
        body: WidthLimiter(child: LoginForm()),
      ),
    );
  }
}
