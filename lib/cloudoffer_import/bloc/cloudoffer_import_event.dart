part of "cloudoffer_import_bloc.dart";

abstract class CloudofferImportEvent extends Equatable {
  const CloudofferImportEvent();

  @override
  List<Object> get props => [];
}

///
/// Evento inicial
///
class CloudofferImportOnLoadEvent extends CloudofferImportEvent {
  CloudofferImportOnLoadEvent() : super();

  @override
  List<Object> get props => [];
  @override
  String toString() => "${super.toString()}";
}

class CloudofferImportOnTickEvent extends CloudofferImportEvent {
  /// Ticks que quedan aún para cumplir con el número de ticks indicado
  final int remaining;
  CloudofferImportOnTickEvent({required this.remaining}) : super();
}

///
/// Empezar importación
///
class CloudofferImportOnCreateEvent extends CloudofferImportEvent {

  CloudofferImportOnCreateEvent() : super();
}

class CloudofferImportOnCreatedEvent extends CloudofferImportEvent {
  final OfferDto createdOffer;
  CloudofferImportOnCreatedEvent({required this.createdOffer}) : super();
}

///
/// Se ha producido un error procesando otro evento
///
class CloudofferImportOnFailureEvent extends CloudofferImportEvent {
  final String message;
  CloudofferImportOnFailureEvent({required this.message}) : super();
}
