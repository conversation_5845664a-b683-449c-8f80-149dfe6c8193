import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/cloudoffer_import/models/cloudoffer_import_formdata.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'cloudoffer_import_event.dart';
part 'cloudoffer_import_state.dart';

class CloudofferImportBloc extends Bloc<CloudofferImportEvent, CloudofferImportState> {
  final AppModelsNS appModels;

  // Subscripción que se crea al iniciar el stream de TickEvents y que se encarga de añadir los eventos a la cola del propio bloque;
  StreamSubscription<CloudofferImportOnTickEvent>? _ticksSubscription;

  CloudofferImportBloc({required this.appModels, required String cloudofferId}) : super(CloudofferImportInitState(cloudofferId: cloudofferId));

  factory CloudofferImportBloc.create({required BuildContext context, required String cloudofferId}) {
    return CloudofferImportBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      cloudofferId: cloudofferId,
    );
  }

  @override
  close() async {
    await _ticksSubscription?.cancel();
    super.close();
  }

  @override
  Stream<Transition<CloudofferImportEvent, CloudofferImportState>> transformEvents(
    Stream<CloudofferImportEvent> events,
    TransitionFunction<CloudofferImportEvent, CloudofferImportState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<CloudofferImportState> mapEventToState(CloudofferImportEvent event) async* {
    if (event is CloudofferImportOnLoadEvent)
      yield* _mapOnLoad(state, event);
    else if (state is CloudofferImportStepsState && event is CloudofferImportOnTickEvent)
      yield* _mapOnTick(state as CloudofferImportStepsState, event);
    else if (state is CloudofferImportStepsState && event is CloudofferImportOnCreateEvent) yield* _mapOnCreate(state as CloudofferImportStepsState, event);
  }

  Stream<CloudofferImportState> _mapOnLoad(CloudofferImportState state, CloudofferImportOnLoadEvent event) async* {
    yield CloudofferImportStepsState(step: CloudofferImportStep.step01WaitingLastVersion, cloudoffer_id: state.cloudofferId);
    // Generar OnTickEvent inmediatamente para cambiar al siguiente estado
    _startOnTickEventsGenerator(lapse: 1);
  }

  Stream<CloudofferImportState> _mapOnTick(CloudofferImportStepsState state, CloudofferImportOnTickEvent event) async* {
    if (state.step == CloudofferImportStep.step01WaitingLastVersion) {
      // De momento no hacemos nada especial: suponemos que tenemos una oferta en el cloud buena para ser importada
      await this._cancelOnTickEventsGenerator();
      // Leemos los detalles de la oferta y seguimos
      try {
        OfferDto? cloudOffer = null;
        bool resultIsDelayed = false;
        try {
          cloudOffer = await appModels.readOffer(state.cloudofferId, ensure_last_version: true);
        } on ApiAcceptedRequest {
          resultIsDelayed = true;
        }
        if (resultIsDelayed && event.remaining > 0) {
          this._startOnTickEventsGenerator(ticks: event.remaining);
          yield CloudofferImportStepsState.copyFrom(stepsState: state);
        } else if (resultIsDelayed) {
          yield CloudofferImportFailureState.fromStepsState(
            stepsState: state,
            error: "Tiempo de espera excedido\nInténtalo más tarde",
          );
        } else if (cloudOffer == null || cloudOffer.status.vn?.code.vn == OfferstatusCode.historic) {
          yield CloudofferImportNotExistsState(
            cloudofferId: state.cloudofferId,
          );
        } else {
          yield state.copyWith(
            formData: CloudoofferImportFormdata(contact: null),
            cloudOffer: cloudOffer,
            step: CloudofferImportStep.step02SelectCustomer,
          );
        }
      } on Exception {
        yield CloudofferImportFailureState.fromStepsState(
          stepsState: state,
          error: "Problemas obteniendo los detalles del anuncio",
        );
      }
    }
  }

  Stream<CloudofferImportState> _mapOnCreate(
    CloudofferImportStepsState state,
    CloudofferImportOnCreateEvent event,
  ) async* {
    try {
      if (state.cloudOffer == null) {
        throw Exception("Unexpected: state.cloudOffer must be a not null value at this point");
      } else if (state.formData == null) {
        throw Exception("Unexpected: state.formData must be a not null value at this point");
      } else {
        OfferDto offer = state.cloudOffer!;
        offer.customer = Some(state.formData!.contact);
        offer.status = Some(OfferstatusDto(code: Some(OfferstatusCode.draft)));
        final createdOffer = await appModels.createOffer(offer);
        yield state.copyWith(
          step: CloudofferImportStep.step04Done,
          createdOffer: createdOffer,
        );
      }
    } on Exception {
      yield CloudofferImportFailureState.fromStepsState(
        stepsState: state,
        error: "Problemas al añadir la oferta en tu cartera de oportunidades",
      );
    }
  }

  ///
  /// Inicia la generación regular de eventos CloudofferImportOnTickEvent  (generar N eventos espaciados en el tiempo)
  /// @param ticks número de eventos a generar
  /// @param lapse segundos entre eventos
  ///
  /// utiliza _cancelTicksStream para dejar de producir eventos antes de tiempo
  ///
  Future<void> _startOnTickEventsGenerator({int ticks = 40, int lapse = 3}) async {
    await this._ticksSubscription?.cancel();
    this._ticksSubscription = Stream.periodic(
      Duration(seconds: lapse),
      (x) => CloudofferImportOnTickEvent(remaining: ticks - x - 1),
    ).take(ticks).listen(
      (event) {
        this.add(event);
      },
    );
  }

  Future<void> _cancelOnTickEventsGenerator() async {
    await _ticksSubscription?.cancel();
    this._ticksSubscription = null;
  }
}
