part of 'cloudoffer_import_bloc.dart';

enum CloudofferImportStep {
  // Nota:
  //   Posibilidad 1: Api donde se da "entidad" a los checks de una oferta (semántica "añadir check", "consultar estado del check")
  //     POST cloud_offers/:offer_id/checks --> check_id.
  //     GET cloud_offers/:offer_id/checks/:check_id/status -> IN_PROCESS, FINISHED
  //   Posibilidad 2: Api orientada a "operaciones" (RPC) donde se encolan operaciones y se espera resultado
  //     POST agents/me/rpc_ops --> op_id
  //        BODY: {code: "refresh_cloud_offer", params: {id:<string>} }
  //     GET  agents/me/rpc_ops/:op_id -> IN_PROCESS, SUCCESS, ERROR
  //        BODY: {id:<string> /* op_id */, code: "refresh_cloud_offer", params: {id:<string>}, status: "IN_PROGRESS"|"SUCCESS"|"DONE", result: undefined|}
  //     Únicamente permitiríamos una operación RPC "IN_PROGRESS" por agente
  step01WaitingLastVersion,
  // Seleccionar el propietario o añadir uno nuevo (como sucede con  olicitar número de teléfono del propietario (si se desea indicar propietario)
  step02SelectCustomer,
  // Creando oferta: Se muestra un "ruca ruca"
  step03CreatingOffer,
  // Oferta creada:  Mostramos resumen operación y link para abrir la ficha
  step04Done,
  // Anuncio del cloud no existe
  step05NotExists
}

abstract class CloudofferImportState extends Equatable {
  /// Identificador de la oferta que deseamos importar.
  final String cloudofferId;

  CloudofferImportState({required this.cloudofferId}) : super();

  @override
  List<Object> get props => [];
}

/// Inicialización (cargar formulario, datos asociados...)
class CloudofferImportInitState extends CloudofferImportState {
  CloudofferImportInitState({required String cloudofferId}) : super(cloudofferId: cloudofferId);
}

class CloudofferImportStepsState extends CloudofferImportState {
  static int _id = 0;

  final CloudofferImportStep step;
  
  // Step2 data:
  //  Datos de la oferta del cloud.  Se obtiene en cuanto tenemos una respuesta RPC de que el refresco ha acabao OK
  final OfferDto? cloudOffer;

  final OfferDto? createdOffer;

  // Datos que se solicitan al usuario (Identificador del contacto)
  //   El formulario muestra un combo para seleccionar un contacto (es opcional).
  //     Por defecto se selecciona el contacto correspondiente al teléfono del anuncio (si lo tenemos) o bien vacío.
  //     El combo incorpora el símbolo "+" para añadir un nuevo contacto... el teléfono se "enviará" al formulario de creación en caso de apretar este botón.
  //   Tras llenar los datos se selecciona "continuar"
  final CloudoofferImportFormdata? formData;

  final _myid = ++_id;
  @override
  List<Object> get props => [_myid];

  CloudofferImportStepsState({
    required String cloudoffer_id,
    required this.step,
    this.cloudOffer,
    this.createdOffer,
    this.formData,
  }) : super(cloudofferId: cloudoffer_id);

  /// Clonar objeto por completo
  factory CloudofferImportStepsState.copyFrom({
    required CloudofferImportStepsState stepsState,
  }) =>
      CloudofferImportStepsState(
        cloudoffer_id: stepsState.cloudofferId,
        step: stepsState.step,
        cloudOffer: stepsState.cloudOffer,
        formData: stepsState.formData,
        createdOffer: stepsState.createdOffer,
      );

  /// Copiar a un nuevo objeto cambiando algunos valores
  CloudofferImportStepsState copyWith({
    CloudofferImportStep? step,
    CloudoofferImportFormdata? formData,
    OfferDto? cloudOffer,
    OfferDto? createdOffer,
  }) =>
      CloudofferImportStepsState(
        cloudoffer_id: this.cloudofferId,
        step: step ?? this.step,
        cloudOffer: cloudOffer ?? this.cloudOffer,
        formData: formData ?? this.formData,
        createdOffer: createdOffer ?? this.createdOffer,
      );
}

class CloudofferImportNotExistsState extends CloudofferImportState {
  CloudofferImportNotExistsState({required String cloudofferId}) : super(cloudofferId: cloudofferId);
}

class CloudofferImportFailureState extends CloudofferImportStepsState {
  final String error;
  CloudofferImportFailureState({
    required this.error,
    required String cloudoffer_id,
    required CloudofferImportStep step,
    String? rpc_id,
    OfferDto? cloudOffer,
    OfferDto? createdOffer,
    CloudoofferImportFormdata? formData,
  }) : super(
          step: step,
          cloudoffer_id: cloudoffer_id,
          cloudOffer: cloudOffer,
          createdOffer: createdOffer,
          formData: formData,
        );

  factory CloudofferImportFailureState.fromStepsState({
    required CloudofferImportStepsState stepsState,
    required String error,
  }) {
    return CloudofferImportFailureState(
      error: error,
      cloudoffer_id: stepsState.cloudofferId,
      step: stepsState.step,
      cloudOffer: stepsState.cloudOffer,
      createdOffer: stepsState.createdOffer,
      formData: stepsState.formData,
    );
  }

  @override
  List<Object> get props => [error, step, cloudofferId];
}
