import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/generated/app_localizations.dart';
import 'package:topbrokers/common/localizations_helper.dart';
import 'package:topbrokers/cloudoffer_import/bloc/cloudoffer_import_bloc.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/cloudoffers_export/reports/cloudoffers_export_reports.dart';
import 'package:topbrokers/common/widgets/select_contact_field.dart';

class CloudofferImportWizzard extends StatefulWidget {
  final String offerId;

  final void Function(String offerId) onEnded;
  final void Function() onCanceled;

  CloudofferImportWizzard({
    Key? key,
    required this.offerId,
    required this.onEnded,
    required this.onCanceled,
  }) : super(key: key ?? AgentorKeys.offerImportPage);

  @override
  _CloudofferImportWizzardState createState() => _CloudofferImportWizzardState();
}

class _CloudofferImportWizzardState extends State<CloudofferImportWizzard> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  late CloudofferImportBloc _formBloc;
  // Form data
  CloudoffersReportCode reportCode = CloudoffersReportCode.basicinfo;
  ReportFormatCode reportFormatCode = ReportFormatCode.csv;
  //
  _CloudofferImportWizzardState() : super();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //final localizations = context.getAppLocalizationsOrThrow();
    //Navigator.pop(context, OfferEditPageResult(id: state.offer.id, saved: true));

    return BlocProvider<CloudofferImportBloc>(
      create: (context) {
        _formBloc = CloudofferImportBloc.create(context: context, cloudofferId: widget.offerId)
          ..add(CloudofferImportOnLoadEvent());
        return _formBloc;
      },
      child: BlocConsumer<CloudofferImportBloc, CloudofferImportState>(
        listener: (context, state) {
          //assert(_formBloc != null);
        },
        builder: (context, state) {
          final textTheme = Theme.of(context).textTheme;
          final apploc = context.apploc;

          if (state is CloudofferImportFailureState) {
            return _unexpectedError(context, state, this._formBloc);
          } else if (state is CloudofferImportNotExistsState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(apploc.cloudoffers_import_addNotExistsLabel, style: textTheme.headlineSmall),
                  ),
                  Divider(height: 20.0, color: Colors.transparent),
                  TextButton.icon(
                    icon: Icon(Icons.close),
                    label: Text(apploc.common_Close),
                    onPressed: () {
                      widget.onCanceled();
                    },
                  ),
                ],
              ),
            );
          } else if (state is CloudofferImportStepsState) {
            switch (state.step) {
              case CloudofferImportStep.step01WaitingLastVersion:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(16),
                        child: Text(apploc.cloudoffers_import_waitingForLastVersionLabel, style: textTheme.headlineSmall),
                      ),
                      Divider(height: 20.0, color: Colors.transparent),
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                      Divider(height: 20.0, color: Colors.transparent),
                      TextButton.icon(
                        icon: Icon(Icons.close),
                        label: Text(apploc.common_Cancel),
                        onPressed: () {
                          widget.onCanceled();
                        },
                      ),
                    ],
                  ),
                );

              case CloudofferImportStep.step02SelectCustomer:
                return Center(
                  child: Padding(
                    padding: EdgeInsets.all(10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(apploc.cloudoffers_import_newPropertyOwnerLabel, style: textTheme.headlineSmall),
                        Padding(
                          padding: EdgeInsets.all(10),
                          child: SelectContactFieldEntry(
                            getValue: () => state.formData!.contact,
                            setValue: (ContactDto? contact) {
                              setState(() {
                                state.formData!.contact = contact;
                              });
                            },
                            isRequired: false,
                          ),
                        ),
                        Divider(height: 40.0, color: Colors.transparent),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton.icon(
                              icon: Icon(Icons.check),
                              label: Text(apploc.cloudoffers_import_createOfferLabel),
                              onPressed: () {
                                // Esto debe hacerse tras la selección de customer... como aún no está implementado, pasamos diréctamente a la creación
                                this._formBloc.add(CloudofferImportOnCreateEvent());
                              },
                            ),
                            TextButton.icon(
                              icon: Icon(Icons.close),
                              label: Text(apploc.common_Cancel),
                              onPressed: () {
                                widget.onCanceled();
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              case CloudofferImportStep.step03CreatingOffer:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(apploc.cloudoffers_import_creatingOfferLabel, style: textTheme.headlineSmall),
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                    ],
                  ),
                );
              case CloudofferImportStep.step04Done:
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(20),
                        child: Center(
                          child: Text(apploc.cloudoffers_import_doneLabel, style: textTheme.headlineSmall),
                        ),
                      ),
                      Divider(height: 40.0, color: Colors.transparent),
                      TextButton.icon(
                        icon: Icon(Icons.check),
                        label: Text(apploc.cloudoffers_import_seeDetailsLabel),
                        onPressed: () {
                          widget.onEnded(state.createdOffer!.id.v);
                        },
                      ),
                    ],
                  ),
                );
              default:
                throw Exception("Unexpected wizzard state");
            }
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _unexpectedError(BuildContext context, CloudofferImportFailureState state, CloudofferImportBloc _formBloc) {
    final apploc = context.apploc;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              apploc.cloudoffers_import_err_importing,
              style: textTheme.titleLarge,
            ),
          ),
          Divider(height: 20.0, color: Colors.transparent),
          Center(
            child: Text(
              state.error,
              style: textTheme.bodyMedium,
            ),
          ),
          Divider(height: 40.0, color: Colors.transparent),
          Padding(
            padding: EdgeInsets.all(5),
            child: TextButton.icon(
              icon: Icon(Icons.check),
              label: Text(apploc.common_Close),
              onPressed: () {
                widget.onCanceled();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _Step02Form extends StatefulWidget {
  final void Function() onCancel;
  final void Function({
    String? contactId,
  }) onContinue;

  const _Step02Form({Key? key, required this.onCancel, required this.onContinue}) : super(key: key);

  @override
  _Step02FormState createState() {
    return _Step02FormState();
  }
}

class _Step02FormState extends State<_Step02Form> {
  final _formKey = GlobalKey<FormState>();
  ContactDto? contact = null;

  @override
  Widget build(BuildContext context) {
    final apploc = context.apploc;
    // Build a Form widget using the _formKey created above.
    return Form(
      key: _formKey,
      child: Column(
        children: [
          GroupEntry(
            //label: "Datos del anuncio",
            isSubgroup: true,
            children: [
              SelectContactFieldEntry(
                getValue: () => contact,
                setValue: (ContactDto? selected) {
                  setState(() {
                    this.contact = selected;
                  });
                },
                isRequired: false,
              ),
            ],
          ),
          Divider(height: 40.0, color: Colors.transparent),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Padding(
                padding: EdgeInsets.all(0),
                child: TextButton.icon(
                  icon: Icon(Icons.check),
                  label: Text(apploc.common_Continue),
                  onPressed: () {
                    widget.onContinue(
                      contactId: contact?.id.vn,
                    );
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.all(0),
                child: TextButton.icon(
                  icon: Icon(Icons.close),
                  label: Text(apploc.common_Cancel),
                  onPressed: () {
                    widget.onCancel();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
