import 'package:flutter/material.dart';
import 'package:topbrokers/cloudoffer_import/widgets/widgets.dart';

Future<String?> importCloudofferDialog(
  context, {
  required String cloudOfferId,
}) {
  return showGeneralDialog<String?>(
      context: context,
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      pageBuilder: (
        BuildContext context,
        Animation animation,
        Animation secondaryAnimation,
      ) {
        return AlertDialog(
          insetPadding: EdgeInsets.all(5.0),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          contentPadding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 5),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            //crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CloudofferImportWizzard(
                offerId: cloudOfferId,
                onCanceled: () {
                  Navigator.of(context).pop(null);
                },
                onEnded: (newOfferId) {
                  Navigator.of(context).pop(newOfferId);
                },
              ),
            ],
          ),
          actions: [],
        );
      });
}
