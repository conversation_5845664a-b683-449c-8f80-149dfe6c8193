part of 'models_channel_bloc.dart';

int _lastId = 0;

abstract class ModelsChannelEvent extends Equatable {
  final _myId = ModelsChannelEvent._nextId();
  static int _nextId() {
    _lastId++;
    if (_lastId > 1000000) {
      _lastId = 1;
    }
    return _lastId;
  }

  @override
  List<Object> get props => [_myId];

  ModelsChannelState toState();
}

class ModelsChannelOnFetched<T> extends ModelsChannelEvent {
  final List<T> entities;
  ModelsChannelOnFetched({required this.entities}) : super();

  ModelsChannelFetchedState<T> toState() => ModelsChannelFetchedState<T>(entities: entities);
}

class ModelsChannelOnUpdated<T> extends ModelsChannelOnFetched<T> {
  ModelsChannelOnUpdated({required List<T> entities}) : super(entities: entities);

  @override
  ModelsChannelUpdatedState<T> toState() => ModelsChannelUpdatedState<T>(entities: entities);
}

class ModelsChannelOnCreated<T> extends ModelsChannelOnFetched<T> {
  ModelsChannelOnCreated({required List<T> entities}) : super(entities: entities);

  @override
  ModelsChannelCreatedState<T> toState() => ModelsChannelCreatedState<T>(entities: entities);
}

class ModelsChannelOnDeleted<T> extends ModelsChannelEvent {
  final Object entityId;
  ModelsChannelOnDeleted({required this.entityId}) : super();

  ModelsChannelDeletedState<T> toState() => ModelsChannelDeletedState<T>(entityId: entityId);
}

class ModelsChannelOnAddedOfferToMyFavourites extends ModelsChannelEvent {
  final String offerId;
  ModelsChannelOnAddedOfferToMyFavourites({required this.offerId}) : super();
  ModelsChannelOnAddedOfferToMyFavouritesState toState() =>
      ModelsChannelOnAddedOfferToMyFavouritesState(offerId: offerId);
}
class ModelsChannelOnRemovedOfferFromMyFavourites extends ModelsChannelEvent {
  final String offerId;
  ModelsChannelOnRemovedOfferFromMyFavourites({required this.offerId}) : super();
  ModelsChannelOnRemovedOfferFromMyFavouritesState toState() =>
      ModelsChannelOnRemovedOfferFromMyFavouritesState(offerId: offerId);
}

/// Eventos específicos para WorkgroupOffer
/// Se trata de informar tanto la oferta como el workgroup cuando se elimina
///
class ModelsChannelOnWorkgroupOfferDeleted extends ModelsChannelOnDeleted<WorkgroupOfferDto> {
  final String offerId;
  final String workgroupId;

  ModelsChannelOnWorkgroupOfferDeleted({required this.offerId, required this.workgroupId})
      : super(entityId: '$offerId,$workgroupId');

  @override
  ModelsChannelDeletedState<WorkgroupOfferDto> toState() =>
      ModelsChannelWorkgroupOfferDeletedState(offerId: this.offerId, workgroupId: this.workgroupId);
}

class ModelsChannelOnWorkgroupOfferCreated extends ModelsChannelEvent {
  final String offerId;
  final String workgroupId;

  ModelsChannelOnWorkgroupOfferCreated({required this.offerId, required this.workgroupId}) : super();

  @override
  ModelsChannelWorkgroupOfferCreatedState toState() =>
      ModelsChannelWorkgroupOfferCreatedState(offerId: this.offerId, workgroupId: this.workgroupId);
}

/// Eventos específicos para PropertyMedia
///
/// Un Propertymedia contiene el id del inmueble y del media, pero para que sea útil necesitamos que también
/// esté informada la oferta del inmueble
///

class ModelsChannelOnPropertymediaUpdated extends ModelsChannelOnUpdated<PropertymediaDto> {
  final String offerId;

  ModelsChannelOnPropertymediaUpdated({required this.offerId, required List<PropertymediaDto> entities})
      : super(entities: entities);

  @override
  ModelsChannelUpdatedState<PropertymediaDto> toState() =>
      ModelsChannelPropertymediaUpdatedState(offerId: offerId, entities: entities);
}

class ModelsChannelOnPropertymediaCreated extends ModelsChannelOnCreated<PropertymediaDto> {
  final String offerId;

  List<PropertymediaDto> get propertymedias => this.entities;

  ModelsChannelOnPropertymediaCreated({required this.offerId, required List<PropertymediaDto> propertymedias})
      : super(entities: propertymedias);

  @override
  ModelsChannelCreatedState<PropertymediaDto> toState() =>
      ModelsChannelPropertymediaCreatedState(offerId: offerId, entities: entities);
}

class ModelsChannelOnPropertymediaDeleted extends ModelsChannelOnDeleted<PropertymediaDto> {
  final String offerId;
  final String mediaKey;
  ModelsChannelOnPropertymediaDeleted({required this.offerId, required this.mediaKey}) : super(entityId: mediaKey);

  ModelsChannelDeletedState<PropertymediaDto> toState() =>
      ModelsChannelPropertymediaDeletedState(offerId: offerId, mediaKey: mediaKey);
}
