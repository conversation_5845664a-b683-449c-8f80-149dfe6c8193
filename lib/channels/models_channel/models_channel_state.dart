part of 'models_channel_bloc.dart';

int _lastStateId = 0;

abstract class ModelsChannelState extends Equatable {
  final _stateId = ++_lastStateId;
  @override
  List<Object> get props => [_stateId];
}

class ModelsChannelNone extends ModelsChannelState {}

class ModelsChannelFetchedState<T> extends ModelsChannelState {
  final List<T> entities;
  ModelsChannelFetchedState({required this.entities}) : super();
}

class ModelsChannelUpdatedState<T> extends ModelsChannelFetchedState<T> {
  ModelsChannelUpdatedState({required List<T> entities}) : super(entities: entities);
}

class ModelsChannelCreatedState<T> extends ModelsChannelFetchedState<T> {
  ModelsChannelCreatedState({required List<T> entities}) : super(entities: entities);
}

class ModelsChannelDeletedState<T> extends ModelsChannelState {
  final Object entityId;
  ModelsChannelDeletedState({required this.entityId}) : super();
}

// Special Events (debería resolverse de otra forma?)
class ModelsChannelPropertymediaUpdatedState extends ModelsChannelUpdatedState<PropertymediaDto> {
  final String offerId;
  ModelsChannelPropertymediaUpdatedState({required this.offerId, required List<PropertymediaDto> entities})
      : super(entities: entities);
}

class ModelsChannelPropertymediaCreatedState extends ModelsChannelCreatedState<PropertymediaDto> {
  final String offerId;
  ModelsChannelPropertymediaCreatedState({required this.offerId, required List<PropertymediaDto> entities})
      : super(entities: entities);
}

class ModelsChannelPropertymediaDeletedState extends ModelsChannelDeletedState<PropertymediaDto> {
  final String offerId;
  final String mediaKey;
  ModelsChannelPropertymediaDeletedState({required this.offerId, required this.mediaKey}) : super(entityId: mediaKey);
}
class ModelsChannelOnRemovedOfferFromMyFavouritesState extends ModelsChannelState {
  final String offerId;
  ModelsChannelOnRemovedOfferFromMyFavouritesState({required this.offerId}) : super(); 
}
class ModelsChannelOnAddedOfferToMyFavouritesState extends ModelsChannelState {
  final String offerId;
  ModelsChannelOnAddedOfferToMyFavouritesState({required this.offerId}) : super(); 
}

class ModelsChannelWorkgroupOfferDeletedState extends ModelsChannelDeletedState<WorkgroupOfferDto> {
  final String offerId;
  final String workgroupId;
  ModelsChannelWorkgroupOfferDeletedState({
    required this.offerId,
    required this.workgroupId,
  }) : super(entityId: '$offerId,$workgroupId');
}

class ModelsChannelWorkgroupOfferCreatedState extends ModelsChannelState {
  final String offerId;
  final String workgroupId;
  ModelsChannelWorkgroupOfferCreatedState({
    required this.offerId,
    required this.workgroupId,
  }) : super();
}
