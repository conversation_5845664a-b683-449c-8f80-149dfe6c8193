
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';

part 'models_channel_event.dart';
part 'models_channel_state.dart';

///
/// Gestiona un canal informativo sobre qué ofertas han sido creadas/modificadas/borradas
///
class ModelsChannelBloc extends Bloc<ModelsChannelEvent, ModelsChannelState> {
  ModelsChannelBloc() : super(ModelsChannelNone());

  @override
  Stream<Transition<ModelsChannelEvent, ModelsChannelState>> transformEvents(
    Stream<ModelsChannelEvent> events,
    TransitionFunction<ModelsChannelEvent, ModelsChannelState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ModelsChannelState> mapEventToState(ModelsChannelEvent event) async* {
    yield event.toState();
  }
}
