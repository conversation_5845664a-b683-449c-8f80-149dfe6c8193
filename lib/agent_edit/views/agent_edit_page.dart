import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/agent_edit/bloc/agent_edit_bloc.dart';
import 'package:topbrokers/agent_edit/models/agent_edit_page_result.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/common/scafold_helper.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';

typedef OnSaveAgentCallback = Function(AgentDto agent);

class AgentEditPage extends StatefulWidget {
  //final OnSaveAgentCallback onSave;
  final String agentId;
  //final AgentDto agent;

  AgentEditPage({Key? key, required this.agentId}) : super(key: key ?? AgentorKeys.editAgentPage);

  @override
  _AgentEditPageState createState() => _AgentEditPageState();
}

class _AgentEditPageState extends State<AgentEditPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return BlocProvider(
      create: (context) => AgentEditBloc.create(context)..add(AgentEditOnEditEvent(agentId: this.widget.agentId)),
      child: BlocConsumer<AgentEditBloc, AgentEditState>(
        listener: (context, state) {
          if (state is AgentEditSaved) {
            Navigator.pop(context, AgentEditPageResult(id: state.agent.id.vn, saved: true));
          }
        },
        builder: (context, state) {
          if (state is AgentEditLoaded) {
            return Scaffold(
              appBar: AppBar(
                title: Text(localizations?.agent_title ?? ""),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: BlocConsumer<AgentEditBloc, AgentEditState>(
                  listener: (context, state) {
                    if (state is AgentEditSaveFailure) {
                      context.showError(state.error);
                    } else if (state is AgentEditValidationFailure) {
                      context.showError(state.error);
                    }
                  },
                  builder: (context, state) {
                    // AgentformSaveFailure y AgentformValidationFailure
                    // extienden AgentformLoaded
                    return _buildForm(context, (state as AgentEditLoaded).agent);
                  },
                ),
              ),
              floatingActionButton: FloatingActionButton(
                key: AgentorKeys.saveAgent,
                tooltip: localizations?.agent_saveBtn,
                child: Icon(Icons.save_outlined, semanticLabel: localizations?.common_accept),
                onPressed: () async {
                  final currentState = _formKey.currentState;
                  if (currentState != null) {
                    if (currentState.validate()) {
                      currentState.save();
                      BlocProvider.of<AgentEditBloc>(context).add(AgentEditOnSaveEvent(agent: state.agent));
                    } else {
                      BlocProvider.of<AgentEditBloc>(context)
                          .add(AgentEditOnValidationErrorEvent(error: localizations?.common_formWithErrors ?? ""));
                    }
                  }
                },
              ),
            );
          } else if (state is AgentEditLoadFailure) {
            return Center(
              child: Text(state.error),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, AgentDto agent) {
    return Container(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            _agentEntries(context, agent).buildForm(key: _formKey, context: context),
            SizedBox.fromSize(size: Size.fromHeight(64)),
          ],
        ),
      ),
    );
  }

  List<FormEntry> _agentEntries(BuildContext context, AgentDto agent) {
    final localizations = AppLocalizations.of(context);
    return <FormEntry>[
      GroupEntry(children: [
        SimpleFieldEntry<String>(
          label: localizations?.agent_nameLabel,
          getValue: () => agent.firstName.vn,
          setValue: (String? v) {
            setState(() {
              agent.firstName = v==null ? None() : Some(v);
            });
          },
          isRequired: true,
        ),
        SimpleFieldEntry<String>(
          label: localizations?.agent_lastnameLabel,
          getValue: () => agent.lastName.vn,
          setValue: (String? v) {
            setState(() {
              agent.lastName = Some(v);
            });
          },
          isRequired: true,
        ),
        SimpleFieldEntry<String>(
          label: localizations?.agent_emailLabel,
          getValue: () => agent.email.vn,
          setValue: (String? v) {
            setState(() {
              agent.email = Some(v??"");
            });
          },
          isRequired: true,
          isEmail: true,
          readOnly: true,
        ),
        SimpleFieldEntry<String>(
          label: localizations?.agent_mobileLabel,
          getValue: () => agent.mobile.vn,
          setValue: (String? v) {
            setState(() {
              agent.mobile = Some(v);
            });
          },
          isRequired: true,
          isPhoneNumber: true,
        )
      ])
    ];
  }
}
