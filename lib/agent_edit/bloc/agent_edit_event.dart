part of 'agent_edit_bloc.dart';

abstract class AgentEditEvent extends Equatable {
  const AgentEditEvent();

  @override
  List<Object> get props => [];
}

class AgentEditOnEditEvent extends AgentEditEvent {
  final String agentId;
  AgentEditOnEditEvent({required this.agentId});
  @override
  List<Object> get props => [agentId];
}



///
/// El usuario quiere guardar los cambios
///
class AgentEditOnSaveEvent extends AgentEditEvent {
  final AgentDto agent;
  AgentEditOnSaveEvent({required this.agent}) : super();

  @override
  List<Object> get props => [agent];
}

class AgentEditOnValidationErrorEvent extends AgentEditEvent {
  final String error;
  AgentEditOnValidationErrorEvent({required this.error}) : super();

  @override
  List<Object> get props => [error];
}



///
/// Los cambios se han guardado corréctamente
///
class AgentEditOnSavedEvent extends AgentEditEvent {
}
