import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'agent_edit_event.dart';
part 'agent_edit_state.dart';

class AgentEditBloc extends Bloc<AgentEditEvent, AgentEditState> {
  final AppModelsNS appModels;

  AgentEditBloc({required this.appModels}) : super(AgentEditState());

  factory AgentEditBloc.create(BuildContext context) {
    return AgentEditBloc(appModels: Provider.of<AppModelsNS>(context, listen: false));
  }

  @override
  Stream<Transition<AgentEditEvent, AgentEditState>> transformEvents(
    Stream<AgentEditEvent> events,
    TransitionFunction<AgentEditEvent, AgentEditState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<AgentEditState> mapEventToState(AgentEditEvent event) async* {
    final state = this.state;
    if (event is AgentEditOnEditEvent) {
      yield* _mapOnEditEventToState(state, event);
    } else if (event is AgentEditOnSaveEvent && state is AgentEditLoaded) {
      yield* _mapOnSaveEventToState(state, event);
    } else if (event is AgentEditOnValidationErrorEvent && state is AgentEditLoaded) {
      yield* _mapOnValidationErrorEventToState(state, event);
    }
  }

  Stream<AgentEditState> _mapOnEditEventToState(AgentEditState state, AgentEditOnEditEvent event) async* {
    try {
      final agent = await _readAgent(event.agentId);
      if (agent == null)
        throw new Exception("Agento no existe");
      else
        yield AgentEditLoaded(agent: agent);
    } on Exception {
      yield AgentEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<AgentEditState> _mapOnSaveEventToState(AgentEditLoaded state, AgentEditOnSaveEvent event) async* {
    try {
      final updated = await _updateAgent(event.agent);
      if (updated == null)
        throw new Exception("Agente no existe");
      else
        yield AgentEditSaved(agent: updated);
    } on Exception {
      yield AgentEditSaveFailure(agent: state.agent, error: "Problemas guardando cambios");
    }
  }

  Stream<AgentEditState> _mapOnValidationErrorEventToState(
      AgentEditLoaded state, AgentEditOnValidationErrorEvent event) async* {
    yield AgentEditValidationFailure(offer: state.agent, error: event.error);
  }

  Future<AgentDto?> _readAgent(String id) async {
    return appModels.readAgent(id);
  }

  Future<AgentDto?> _updateAgent(AgentDto agent) async {
    assert(agent.id.vn != null);
    return appModels.updateAgent(agent.copyWith(email: None()));
  }
}
