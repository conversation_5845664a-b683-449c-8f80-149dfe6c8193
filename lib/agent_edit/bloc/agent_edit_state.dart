part of 'agent_edit_bloc.dart';

class AgentEditState extends Equatable {
  @override
  List<Object> get props => [];
}

class AgentEditLoading extends AgentEditState {}

class AgentEditLoaded extends AgentEditState {
  final AgentDto agent;
  AgentEditLoaded({required this.agent}) : super();

  @override
  List<Object> get props => [agent];
}

class AgentEditLoadFailure extends AgentEditState {
  final String error;

  AgentEditLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

class AgentEditSaved extends AgentEditState {
  final AgentDto agent;

  AgentEditSaved({required this.agent}) : super();

  @override
  List<Object> get props => [agent];
}

class AgentEditSaveFailure extends AgentEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  AgentEditSaveFailure({required agent, required this.error})
      : super(agent: agent);

  @override
  List<Object> get props => [lastError, agent, error];
}
class AgentEditValidationFailure extends AgentEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  AgentEditValidationFailure({required offer, required this.error})
      : super(agent: offer);

  @override
  List<Object> get props => [lastError, agent, error];
}