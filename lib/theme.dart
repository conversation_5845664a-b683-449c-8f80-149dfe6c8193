import 'package:flutter/material.dart';

class AgentorTheme {
  static ThemeData get theme {
    final themeData = ThemeData.light();
    final textTheme = themeData.textTheme;
    final primaryIconTheme = themeData.primaryIconTheme;
    final bodyText1 = textTheme.bodyLarge; //.copyWith(decorationColor: Colors.transparent);
    final backgroundColor = Colors.grey[100];
    final primaryColor = Colors.grey.shade900;
    final buttonColor = Color.fromRGBO(48, 193, 238, 255).withAlpha(255);
    return ThemeData.light().copyWith(
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor.withAlpha(200),
            padding: const EdgeInsets.fromLTRB(15, 10, 15, 10),
            textStyle: TextStyle(
              color: Colors.white,
            ),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: buttonColor, padding: const EdgeInsets.fromLTRB(2, 2, 2, 2),
            textStyle: TextStyle(color: primaryColor),
            minimumSize: Size.zero,
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          floatingLabelBehavior: FloatingLabelBehavior.always,
        ),
        scaffoldBackgroundColor: backgroundColor,
        appBarTheme: themeData.appBarTheme.copyWith(backgroundColor: Color.fromRGBO(48, 193, 238, 255).withAlpha(255)),
        primaryColor: primaryColor,
        primaryColorDark: primaryColor, //Colors.teal[800],
        primaryColorLight: primaryColor.withAlpha(220), // Color.fromRGBO(116, 181, 224, 255).withAlpha(255), //  Colors.teal[400],
        popupMenuTheme: themeData.popupMenuTheme.copyWith(color: Colors.white),
        bottomNavigationBarTheme: themeData.bottomNavigationBarTheme.copyWith(
          type: BottomNavigationBarType.fixed,
        ),
        textSelectionTheme: themeData.textSelectionTheme.copyWith(selectionColor: Colors.black),
        hintColor: Colors.black,
        // Usado por checkboxes y radiobuttons
        unselectedWidgetColor: Colors.grey[400],
        floatingActionButtonTheme: FloatingActionButtonThemeData(backgroundColor: primaryColor, foregroundColor: Colors.white),
        snackBarTheme: SnackBarThemeData(
          backgroundColor: themeData.dialogBackgroundColor,
          contentTextStyle: bodyText1,
          actionTextColor: Colors.cyan[300],
        ),
        textTheme: textTheme.copyWith(
          bodyLarge: bodyText1,
        ),
        primaryIconTheme: primaryIconTheme.copyWith(color: Colors.white, opacity: 255),
        cardTheme: themeData.cardTheme.copyWith(color: backgroundColor),
        colorScheme: ColorScheme.fromSwatch().copyWith(secondary: primaryColor), checkboxTheme: CheckboxThemeData(
 fillColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return primaryColor; }
 return null;
 }),
 ), radioTheme: RadioThemeData(
 fillColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return primaryColor; }
 return null;
 }),
 ), switchTheme: SwitchThemeData(
 thumbColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return primaryColor; }
 return null;
 }),
 trackColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
 if (states.contains(MaterialState.disabled)) { return null; }
 if (states.contains(MaterialState.selected)) { return primaryColor; }
 return null;
 }),
 ));
  }
}
