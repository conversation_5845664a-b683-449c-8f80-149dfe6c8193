import 'package:flutter/material.dart';

class AgentorTheme {
  static ThemeData get theme {
    final themeData = ThemeData.light();
    final textTheme = themeData.textTheme;
    final primaryIconTheme = themeData.primaryIconTheme;
    final bodyText1 = textTheme.bodyText1; //.copyWith(decorationColor: Colors.transparent);
    final backgroundColor = Colors.grey[100];
    final primaryColor = Colors.grey.shade900;
    final buttonColor = Color.fromRGBO(48, 193, 238, 255).withAlpha(255);
    return ThemeData.light().copyWith(
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            primary: primaryColor.withAlpha(200),
            padding: const EdgeInsets.fromLTRB(15, 10, 15, 10),
            textStyle: TextStyle(
              color: Colors.white,
            ),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            primary: buttonColor,
            padding: const EdgeInsets.fromLTRB(2, 2, 2, 2),
            textStyle: TextStyle(color: primaryColor),
            minimumSize: Size.zero,
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          floatingLabelBehavior: FloatingLabelBehavior.always,
        ),
        scaffoldBackgroundColor: backgroundColor,
        appBarTheme: themeData.appBarTheme.copyWith(backgroundColor: Color.fromRGBO(48, 193, 238, 255).withAlpha(255)),
        primaryColor: primaryColor,
        primaryColorDark: primaryColor, //Colors.teal[800],
        primaryColorLight: primaryColor.withAlpha(220), // Color.fromRGBO(116, 181, 224, 255).withAlpha(255), //  Colors.teal[400],
        popupMenuTheme: themeData.popupMenuTheme.copyWith(color: Colors.white),
        bottomNavigationBarTheme: themeData.bottomNavigationBarTheme.copyWith(
          type: BottomNavigationBarType.fixed,
        ),
        textSelectionTheme: themeData.textSelectionTheme.copyWith(selectionColor: Colors.black),
        hintColor: Colors.black,
        toggleableActiveColor: primaryColor,
        // Usado por checkboxes y radiobuttons
        unselectedWidgetColor: Colors.grey[400],
        selectedRowColor: Colors.grey[200],
        floatingActionButtonTheme: FloatingActionButtonThemeData(backgroundColor: primaryColor, foregroundColor: Colors.white),
        snackBarTheme: SnackBarThemeData(
          backgroundColor: themeData.dialogBackgroundColor,
          contentTextStyle: bodyText1,
          actionTextColor: Colors.cyan[300],
        ),
        textTheme: textTheme.copyWith(
          bodyText1: bodyText1,
        ),
        primaryIconTheme: primaryIconTheme.copyWith(color: Colors.white, opacity: 255),
        cardTheme: themeData.cardTheme.copyWith(color: backgroundColor),
        colorScheme: ColorScheme.fromSwatch().copyWith(secondary: primaryColor));
  }
}
