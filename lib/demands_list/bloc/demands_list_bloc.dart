import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/common/set_helper.dart';
import 'package:topbrokers/demands_list/models/demands_filter_option.dart';

part 'demands_list_event.dart';
part 'demands_list_state.dart';

class DemandsListBloc extends Bloc<DemandsListEvent, DemandsListState> {
  static const _PAGE_SIZE = 30;

  final AppModelsNS appModels;

  /// Cuando deseamos la oportunidades de un cliente
  final Optional<String> customerId;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;

  DemandsListBloc({
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
    List<DemandslistFilterOption>? filterOptions,
    DemandsListFilter filterX = const DemandsListFilter(),
    this.customerId = const None(),
  }) : super(
          DemandsListState.create(
            filterOptions: Set.from(filterOptions ?? DemandslistFilterOptions.notHistoric),
            filter: filterX,
          ),
        ) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelCreatedState<DemandDto> || state is ModelsChannelUpdatedState<DemandDto>) {
        this.add(DemandsListOnRefresh());
      }
    });
  }

  factory DemandsListBloc.create(
    BuildContext context, {
    Optional<String> customerId = const None(),
    List<DemandslistFilterOption> filterOptions = DemandslistFilterOptions.notHistoric,
  }) {
    return DemandsListBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      customerId: customerId,
      filterOptions: filterOptions,
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    await _modelsChannelSubscription.cancel();
    return super.close();
  }

  @override
  Stream<Transition<DemandsListEvent, DemandsListState>> transformEvents(
    Stream<DemandsListEvent> events,
    TransitionFunction<DemandsListEvent, DemandsListState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<DemandsListState> mapEventToState(DemandsListEvent event) async* {
    if (event is DemandsListOnFetch) {
      yield* _mapOnFetched(state, event);
    } else if (event is DemandsListOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is DemandsListOnDemandChanged) {
      yield* _mapOnDemandChanged(state, event);
    } else if (event is DemandsListOnItemSelected) {
      yield* _mapOnItemSelected(state, event);
    } else if (event is DemandsListOnFilterOptionSelected) {
      yield* _mapOnFilterOptionSelected(state, event);
    } else if (event is DemandsListOnFilterChanged) {
      yield* _mapOnFilterChanged(state, event);
    }
  }

  Stream<DemandsListState> _mapOnFilterOptionSelected(
      DemandsListState state, DemandsListOnFilterOptionSelected event) async* {
    try {
      yield state.copyWith(status: DemandsListStatus.initial);
      Set<DemandslistFilterOption> newFilterOptions = state.filterOptions.contains(event.filterOption)
          ? state.filterOptions.copyRemoving(event.filterOption)
          : state.filterOptions.copyAdding(event.filterOption);

      final demands = await _listDemands(filterOptions: newFilterOptions, filter: state.filter);
      yield state.copyWith(
        status: DemandsListStatus.success,
        filterOptions: newFilterOptions,
        demands: demands,
        hasReachedMax: demands.length != _PAGE_SIZE,
      );
    } on Exception catch (e) {
      print("$e");
      yield state.copyWith(status: DemandsListStatus.failure);
    }
  }

  Stream<DemandsListState> _mapOnFilterChanged(
    DemandsListState state,
    DemandsListOnFilterChanged event,
  ) async* {
    try {
      yield state.copyWith(status: DemandsListStatus.initial);
      final demands = await _listDemands(filterOptions: state.filterOptions, filter: event.filter);
      yield state.copyWith(
        status: DemandsListStatus.success,
        filter: event.filter,
        demands: demands,
        hasReachedMax: demands.length != _PAGE_SIZE,
      );
    } on Exception catch (e) {
      print("$e");
      yield state.copyWith(status: DemandsListStatus.failure);
    }
  }

  Stream<DemandsListState> _mapOnItemSelected(DemandsListState state, DemandsListOnItemSelected event) async* {
    try {
      // No tenemos en cuenta los datos de la nueva oferta:  recargamos la lista
      yield state.copyWith(selectedDemandId: event.demand.id.vn ?? "");
    } on Exception {
      yield state.copyWith(status: DemandsListStatus.failure);
    }
  }

  Stream<DemandsListState> _mapOnFetched(DemandsListState state, DemandsListOnFetch event) async* {
    if (state.hasReachedMax) yield state;
    try {
      if (state.status == DemandsListStatus.initial) {
        final demands = await _listDemands(filterOptions: state.filterOptions, filter: state.filter);
        yield state.copyWith(
          status: DemandsListStatus.success,
          demands: demands,
          hasReachedMax: demands.length != _PAGE_SIZE,
        );
      } else {
        // Este punto sirve tanto en estado success como failure si se reintentase
        final demands = await _listDemands(
          filterOptions: state.filterOptions,
          filter: state.filter,
          offset: state.demands.length,
        );
        yield demands.isEmpty
            ? state.copyWith(
                hasReachedMax: true,
              )
            : state.copyWith(
                status: DemandsListStatus.success,
                demands: List.of(state.demands)..addAll(demands),
                hasReachedMax: false,
              );
      }
    } on Exception {
      yield state.copyWith(status: DemandsListStatus.failure);
    }
  }

  Stream<DemandsListState> _mapOnRefresh(DemandsListState state, DemandsListOnRefresh event) async* {
    try {
      yield state.copyWith(status: DemandsListStatus.initial);
      final demands = await _listDemands(filterOptions: state.filterOptions, filter: state.filter);
      yield state.copyWith(
        status: DemandsListStatus.success,
        demands: demands,
        hasReachedMax: demands.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: DemandsListStatus.failure);
    }
  }

  ///
  /// Parcheamos la oportunidad asociada a la oferta sin necesidad de tener que recargar la lista entera
  /// Debido a que el orden de la lista puede cambiar, de momento no lo usamos (preferimos refrescar lista entera)
  Stream<DemandsListState> _mapOnDemandChanged(DemandsListState state, DemandsListOnDemandChanged event) async* {
    try {
      final demandId = event.demandId;
      final opportunityIndex = state.demands.indexWhere((o) => o.id.vn == demandId);
      if (opportunityIndex != -1) {
        final demand = await appModels.readDemand(demandId);
        if (demand != null) {
          yield state.copyWith(
            demands: state.demands.map((o) => o.id.vn == demandId ? demand : o).toList(),
          );
        }
      }
    } on Exception {
      yield state.copyWith(status: DemandsListStatus.failure);
    }
  }

  Future<List<DemandDto>> _listDemands({
    required Set<DemandslistFilterOption> filterOptions,
    required DemandsListFilter filter,
    int offset = 0,
  }) async {
    final srvFilter = filter.copyWith(
      customerId: customerId,
      statuses: {
        if (filterOptions.contains(DemandslistFilterOption.demand_active)) DemandstatusCode.active,
        if (filterOptions.contains(DemandslistFilterOption.demand_historic)) DemandstatusCode.historic,
      }      
    );

    final demands = await appModels.listDemands(
      filter: srvFilter,
      offset: offset,
      limit: _PAGE_SIZE,
    );
    return demands;
    
  }
}
