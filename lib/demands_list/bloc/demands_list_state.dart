part of 'demands_list_bloc.dart';

enum DemandsListStatus { initial, success, failure }

int _idGenerator = 0;

class DemandsListState extends Equatable {
  const DemandsListState({
    this.status = DemandsListStatus.initial,
    this.demands = const <DemandDto>[],
    this.hasReachedMax = false,
    required this.filterOptions,
    required this.filter,
    this.selectedDemandId = "",
    required this.id,
  });

  // Actual status of the screen
  final DemandsListStatus status;

  // *+*+* Nombre temporal hasta que se migre complétamente todo a un único filtro
  final DemandsListFilter filter;
  // Wich filter are we applying to the oportunities of kind "oofers"
  final Set<DemandslistFilterOption> filterOptions;
  // The list of actual fetched demands
  final List<DemandDto> demands;
  // There is no more demands to be fetched
  final bool hasReachedMax;
  // Identifier of the selected demand
  final String selectedDemandId;

  final int id;

  factory DemandsListState.create({
    required Set<DemandslistFilterOption> filterOptions,
    required DemandsListFilter filter,
  }) =>
      DemandsListState(
        filterOptions: filterOptions,
        filter: filter,
        id: ++_idGenerator,
      );

  DemandsListState copyWith({
    DemandsListStatus? status,
    Set<DemandslistFilterOption>? filterOptions,
    DemandsListFilter? filter,
    List<DemandDto>? demands,
    bool? hasReachedMax,
    String? selectedDemandId,
  }) =>
      DemandsListState(
        status: status ?? this.status,
        filterOptions: filterOptions ?? this.filterOptions,
        filter: filter ?? this.filter,
        demands: demands ?? this.demands,
        hasReachedMax: hasReachedMax ?? this.hasReachedMax,
        selectedDemandId: selectedDemandId ?? this.selectedDemandId,
        id: ++_idGenerator,
      );

  @override
  List<Object> get props => [id, status, demands, hasReachedMax, filterOptions, selectedDemandId];
  @override
  String toString() => "${super.toString()}$id";
}
