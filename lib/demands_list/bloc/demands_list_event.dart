part of 'demands_list_bloc.dart';

int _id = 0;

abstract class DemandsListEvent extends Equatable {
  const DemandsListEvent();

  @override
  List<Object> get props => [];
}

class DemandsListOnDemandChanged extends DemandsListEvent {
  final String demandId;

  const DemandsListOnDemandChanged({required this.demandId});
  @override
  List<Object> get props => [demandId];
}

class DemandsListOnRefresh extends DemandsListEvent {}

class DemandsListOnFetch extends DemandsListEvent {
  final _myid = ++_id;

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}

class DemandsListOnFilterOptionSelected extends DemandsListEvent {
  final DemandslistFilterOption filterOption;
  const DemandsListOnFilterOptionSelected(this.filterOption);
  @override
  List<Object> get props => [filterOption];

  @override
  String toString() => "DemandsListOnFilterOptionSelected { new filter: $filterOption }";
}

class DemandsListOnFilterChanged extends DemandsListEvent {
  final DemandsListFilter filter;
  const DemandsListOnFilterChanged(this.filter);
  @override
  List<Object> get props => [filter];

  @override
  String toString() => "DemandsListOnFilterChanged { filter: $filter }";
}

class DemandsListOnItemSelected extends DemandsListEvent {
  final DemandDto demand;
  const DemandsListOnItemSelected(this.demand);

  @override
  List<Object> get props => [demand];

  @override
  String toString() => "DemandsListOnItemSelected { opportunity: $demand  }";
}
