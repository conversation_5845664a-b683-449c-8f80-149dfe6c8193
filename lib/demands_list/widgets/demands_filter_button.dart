import 'package:topbrokers/demands_filter_dialog/demands_filter.dart';
import 'package:topbrokers/demands_list/demands_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

///
/// Botón que despliega diálogo de parámetros de filtrado de una lista de oportunidades.
/// Cuando se obtiene el nuevo filtro, se genera un evento DemandsListOnFilterChangedOnFilterChanged
///
class DemandsFilterButton extends StatelessWidget {
  final bool visible;

  DemandsFilterButton({
    Key? key,
    this.visible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemandsListBloc, DemandsListState>(
      builder: (context, state) => IconButton(
        icon: const Icon(Icons.search),
        tooltip: 'Filtrar',
        onPressed: () async {
          final newFilter = await shoyDemandsFilterDialog(context, filter: state.filter);
          if (newFilter != null) {
            BlocProvider.of<DemandsListBloc>(context).add(DemandsListOnFilterChanged(newFilter));
          }
        },
      ),
    );
  }
}
