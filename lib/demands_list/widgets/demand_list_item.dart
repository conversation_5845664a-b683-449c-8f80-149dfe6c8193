import 'package:topbrokers/demand_edit/widgets/demand_list_item_picture.dart';
import 'package:topbrokers/common/widgets/contact_link.dart';
import 'package:flutter/material.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

/*const _c_lang = "es";
const _c_locale = "es_ES";
const _c_lineheight = 1.0;*/
const isDense = true;

class DemandListItem extends StatelessWidget {
  const DemandListItem({
    Key? key,
    required this.demand,
    this.isSelected = false,
    this.onTap,
    this.showCustomer = true,
  }) : super(key: key);

  final DemandDto demand;
  final bool isSelected;
  final void Function()? onTap;
  final bool showCustomer;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final typePart = _buildPropertyType(context, theme, demand.property);
    final saleRentPart = _buildSaleRent(context, theme, demand);
    final cityPart = _buildCity(context, theme, demand.property.vn?.address.vn);
    final saleRentAmountPart = _buildSaleRentAmount(context, theme, demand);
    final customer = demand.customer.vn;

    return Card(
      //color: isSelected ? theme.primaryColorLight : theme.cardColor,
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal:8),
        leading: DemandListItemAvatar(
          demand: demand,
        ),
        title: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(children: _addSeparationWhiteSpace(typePart, saleRentPart)),
                  Row(children: saleRentAmountPart),
                ],
              ),
            ),
            if (cityPart != null)
              Expanded(
                child: cityPart,
              ),
          ],
        ),
        isThreeLine: false,
        subtitle: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: _buildPropertyAttributes2(context, theme, demand.property.vn?.attributes.vn),
                  ),
                ],
              ),
            ),
            if (showCustomer && customer?.id.vn != null)
              Expanded(
                child: ContactLink(
                  contact: customer as ContactDto,
                  isDense: true,
                ),
              ),
          ],
        ),
        dense: true,
        selected: isSelected,
        onTap: onTap,
      ),
    );
  }

  static List<Text> _buildSaleRent(BuildContext context, ThemeData theme, DemandDto demand) {
    final textTheme = theme.textTheme;
    const String C_SALE_TXT = "venta";
    const String C_RENT_TXT = "alquiler";

    final bool isSale = demand.sale.vn?.allowed.vn ?? false;
    final bool isRent = demand.rent.vn?.allowed.vn ?? false;

    final String txt = (isSale && isRent)
        ? '$C_SALE_TXT/$C_RENT_TXT'
        : (isSale)
            ? C_SALE_TXT
            : C_RENT_TXT;

    return <Text>[Text(txt, style: textTheme.bodyLarge)];
  }

  static List<Text> _buildSaleRentAmount(BuildContext context, ThemeData theme, DemandDto demand) {
    final txt = (() {
      final saleAmount = demand.sale.vn?.amount.vn;
      final rentAmount = demand.rent.vn?.amount.vn;
      if (saleAmount != null && rentAmount != null) {
        return '$saleAmount/$rentAmount €';
      } else if (saleAmount != null) {
        return '$saleAmount €';
      } else if (rentAmount != null) {
        return '$rentAmount €';
      } else {
        return null;
      }
    })();

    return txt != null ? [Text(txt)] : [];
  }

  static List<Text> _buildPropertyType(BuildContext context, ThemeData theme, Optional<PropertyDto> property) {
    final textTheme = theme.textTheme;
    final typeName = property.vn?.type.vn?.label.vn?.localized ?? "";
    return [Text('$typeName', style: textTheme.bodyLarge)];
  }

  static List<Widget> _buildPropertyAttributes2(BuildContext context, ThemeData theme, PropertyAttributesDto? attrs) {
    if (attrs == null) {
      return [];
    } else {
      final totalSurfaceM2 = attrs.totalSurfaceM2.vn;
      final totalBedroomsCount = attrs.totalBedroomsCount.vn;
      final isExterior = attrs.facadeCodes.vn?.contains("exterior");

      final textTheme = Theme.of(context).textTheme;

      final textStyle = textTheme.bodyMedium?.copyWith(
          fontSize: 13.0); //copyWith(height: (textTheme.bodyText2.height??1) * (isDense ? 0.6 : 1.0));//_c_lineheight);
      final txtStyle = textStyle;
      final sufixStyle = textStyle;
      return [
        new Flexible(
          child: RichText(
            softWrap: true,
            text: TextSpan(
              text: "",
              style: textStyle,
              children: addSeparation(
                [
                  if (totalSurfaceM2 != null)
                    [
                      TextSpan(text: "$totalSurfaceM2"), //, style: txtStyle),
                      TextSpan(text: "m²", style: sufixStyle),
                    ],
                  if (totalBedroomsCount != null && totalBedroomsCount > 0)
                    [
                      TextSpan(text: "$totalBedroomsCount", style: txtStyle),
                      TextSpan(text: " habitaciones", style: sufixStyle),
                    ],
                  if (isExterior != null && isExterior)
                    [
                      TextSpan(text: "exterior", style: sufixStyle),
                    ],
                ],
              ),
            ),
          ),
        ),
      ];
    }
  }

  static List<InlineSpan> addSeparation(List<Iterable<InlineSpan>> texts) => texts.fold(
      <InlineSpan>[],
      (allSpans, spans) => allSpans
        ..addAll([
          if (allSpans.length != 0 && spans.length != 0) TextSpan(text: ", "),
        ])
        ..addAll(spans));

  static Widget? _buildCity(BuildContext context, ThemeData theme, PropertyAddressDto? address) {
    if (address == null)
      return null;
    else {
      final textTheme = theme.textTheme;
      final cityLabel = address.city.vn?.label.vn?.localized ?? "";
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(cityLabel, style: textTheme.bodyLarge, overflow: TextOverflow.ellipsis),
          Text(
            address.composedStreetLine.vn ?? "", // provinceLabel,
            style: textTheme.bodyMedium,
            overflow: TextOverflow.ellipsis,
          )
        ],
      );
    }
  }

  static List<Text> _addSeparationWhiteSpace(List<Text>? a, List<Text>? b) {
    bool aIsWhite = a == null || a.length == 0 || a[0].data == null || a[0].data!.length == 0 || a[0].data == " ";
    bool bIsWhite = b == null || b.length == 0 || b[0].data == null || b[0].data!.length == 0 || b[0].data == " ";

    if (!aIsWhite && !bIsWhite) {
      return a + [Text(" ")] + b;
    } else {
      return (a ?? []) + (b ?? []);
    }
  }
}
