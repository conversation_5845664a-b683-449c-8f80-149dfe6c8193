import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/demands_list/demands_list.dart';


class DemandsOptionsButton extends StatelessWidget {
  final bool visible;

  DemandsOptionsButton({
    Key? key,
    this.visible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemandsListBloc, DemandsListState>(
      builder: (context, state) {
        final button = _Button(
          onSelected: (DemandslistFilterOption filterOption) {
            BlocProvider.of<DemandsListBloc>(context).add(DemandsListOnFilterOptionSelected(filterOption));
          },
          activeOptions: state.filterOptions,
        );
        return AnimatedOpacity(
          opacity: visible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 150),
          child: visible ? button : IgnorePointer(child: button),
        );
      },
    );
  }
}

class _<PERSON><PERSON> extends StatelessWidget {
  const _Button({
    Key? key,
    required this.onSelected,
    required this.activeOptions,
  }) : super(key: key);

  final PopupMenuItemSelected<DemandslistFilterOption> onSelected;
  final Set<DemandslistFilterOption> activeOptions;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<DemandslistFilterOption>(
      key: AgentorKeys.demandsFilterButton,

      tooltip: "Filter",
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<DemandslistFilterOption>(
          //key: ArchSampleKeys.activeFilter,
          value: DemandslistFilterOption.demand_active,
          child: _buildMenuItemWidget(
            "activas",
            activeOptions.contains(DemandslistFilterOption.demand_active),
          ),
        ),
        PopupMenuItem<DemandslistFilterOption>(
          //key: ArchSampleKeys.activeFilter,
          value: DemandslistFilterOption.demand_historic,
          child: _buildMenuItemWidget(
            "borradas",
            activeOptions.contains(DemandslistFilterOption.demand_historic),
          ),
        ),
      ],
      icon: Icon(Icons.filter_list),
    );
  }

  Widget _buildMenuItemWidget(String text, bool active) {
    return Row(children: [
      if (active)
        Icon(Icons.check_box_outlined, color: Colors.grey)
      else
        Icon(Icons.check_box_outline_blank, color: Colors.grey),
      Text(text)
    ]);
  }
}
