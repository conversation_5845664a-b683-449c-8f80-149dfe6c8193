import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/demands_list/demands_list.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmbeddedDemandsList extends StatefulWidget {
  final Optional<String> customerId;
  final Optional<bool> inlcudeHistoric;

  EmbeddedDemandsList({
    this.customerId = const None(),
    this.inlcudeHistoric = True,
  }) : super();

  @override
  _EmbeddedDemandsListState createState() => _EmbeddedDemandsListState();
}

class _EmbeddedDemandsListState extends State<EmbeddedDemandsList> {
  final _scrollController = ScrollController();
  late DemandsListBloc _demandsBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    return BlocProvider<DemandsListBloc>(
      create: (context) {
        _demandsBloc = DemandsListBloc.create(
          context,
          customerId: widget.customerId,
          filterOptions: widget.inlcudeHistoric.v ? DemandslistFilterOptions.all : DemandslistFilterOptions.notHistoric,
        )..add(DemandsListOnFetch());
        return _demandsBloc;
      },
      child: BlocConsumer<DemandsListBloc, DemandsListState>(
        listener: (context, state) {
          if (!state.hasReachedMax && _isBottom) {
            _demandsBloc.add(DemandsListOnFetch());
          }
        },
        builder: (context, state) {
          switch (state.status) {
            case DemandsListStatus.failure:
              return Center(
                child: Text(localization.demands_errLoading),
              );
            case DemandsListStatus.success:
              return ConstrainedBox(
                constraints: BoxConstraints(maxHeight: 160, minHeight: 0),
                child: state.demands.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              localization.demands_noDemands,
                              style: TextStyle(color: Colors.grey),
                            )
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: () async {
                          _demandsBloc.add(DemandsListOnRefresh());
                        },
                        child: ListView.builder(
                          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                          itemBuilder: (BuildContext context, int index) {
                            if (index >= state.demands.length) {
                              return BottomLoader();
                            } else {
                              return DemandListItem(
                                demand: state.demands[index],
                                isSelected: state.selectedDemandId == state.demands[index].id.vn,
                                showCustomer: widget.customerId.vn == null,
                                onTap: () {
                                  final opportunity = state.demands[index];
                                  Navigator.pushNamed(
                                    context,
                                    AgentorRoutes.showDemand,
                                    arguments: opportunity.id.v,
                                  );
                                },
                              );
                            }
                          },
                          itemCount: state.hasReachedMax ? state.demands.length : state.demands.length + 1,
                          controller: _scrollController,
                        ),
                      ),
              );

            default:
              return const Center(
                child: CircularProgressIndicator(),
              );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _demandsBloc.add(DemandsListOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
