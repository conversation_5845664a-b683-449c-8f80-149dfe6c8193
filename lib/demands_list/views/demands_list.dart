import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/demands_list/demands_list.dart';

import 'package:topbrokers/routes.dart';

class DemandsList extends StatefulWidget {
  @override
  _DemandsListState createState() => _DemandsListState();
}

class _DemandsListState extends State<DemandsList> {
  final _scrollController = ScrollController();
  late DemandsListBloc _opportunitesBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _opportunitesBloc = context.bloc<DemandsListBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final localization = context.getAppLocalizationsOrThrow();
    return BlocConsumer<DemandsListBloc, DemandsListState>(
      listener: (context, state) {
        if (!state.hasReachedMax && _isBottom) {
          _opportunitesBloc.add(DemandsListOnFetch());
        }
      },
      builder: (context, state) {
        switch (state.status) {
          case DemandsListStatus.failure:
            return Center(
              child: Text(localization.demands_problems),
            );
          case DemandsListStatus.success:
            return state.demands.isEmpty
                ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          localization.demands_notFound,
                          style: TextStyle(color: Colors.grey),
                        ),
                        Divider(height: 42),
                        ElevatedButton.icon(
                          icon: Icon(CustomAppIcons.demand, size: 16),
                          label: Text(localization.demands_register),
                          onPressed: () {
                            Navigator.pushNamed(context, AgentorRoutes.addDemand);
                          },
                        )
                      ],
                    ),
                  )
                : RefreshIndicator(
                    triggerMode: RefreshIndicatorTriggerMode.anywhere,
                    onRefresh: () async {
                      _opportunitesBloc.add(DemandsListOnRefresh());
                    },
                    child: ListView.builder(
                      key: AgentorKeys.demandsList,
                      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                      itemBuilder: (BuildContext context, int index) {
                        if (index >= state.demands.length) {
                          return BottomLoader();
                        } else {
                          return DemandListItem(
                            demand: state.demands[index],
                            isSelected: state.selectedDemandId == state.demands[index].id.vn,
                            onTap: () {
                              final opportunity = state.demands[index];
                              Navigator.pushNamed(
                                context,
                                AgentorRoutes.showDemand,
                                arguments: opportunity.id.v,
                              );
                            },
                          );
                        }
                      },
                      itemCount: state.hasReachedMax ? state.demands.length : state.demands.length + 1,
                      controller: _scrollController,
                    ),
                    //),
                  );
          default:
            return const Center(
              child: CircularProgressIndicator(),
            );
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _opportunitesBloc.add(DemandsListOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
