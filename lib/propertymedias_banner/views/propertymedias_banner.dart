import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/propertymedia/views/propertymedia_uploader.dart';
import 'package:topbrokers/propertymedias_banner/bloc/propertymedias_banner_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

class PropertymediasBanner extends StatefulWidget {
  // Picked file to be uploaded
  final String offerId;
  final bool readOnly;

  PropertymediasBanner({
    Key? key,
    required this.offerId,
    this.readOnly = true,
  }) : super(key: key);

  @override
  _PropertymediasBannerState createState() => _PropertymediasBannerState();
}

class _PropertymediasBannerState extends State<PropertymediasBanner> {
  final List<PickedFile> uploadingImages = [];
  final List<PropertymediaDto> propertymedias = [];

  @override
  @mustCallSuper
  void dispose() {
    uploadingImages.clear();
    super.dispose();
  }

  static const double _c_general_padding = 5;
  static const double _c_separation_between_thumbs = 12;
  static const double _c_thumb_card_padding = 12;
  static const double _c_separation_between_image_and_buttons = 5;
  static const double _c_image_height = 170;
  static const double _c_min_thumb_height =
      _c_general_padding + _c_thumb_card_padding + _c_image_height + _c_separation_between_image_and_buttons;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => PropertymediasBannerBloc.create(context, offerId: widget.offerId),
      child: BlocConsumer<PropertymediasBannerBloc, PropertymediasBannerState>(
        listener: (context, state) => {},
        buildWhen: (previous, current) => previous.instance != current.instance,
        builder: (context, state) {
          if (state is! PropertymediaBannerFetchedState)
            return Column(children: [
              ConstrainedBox(
                constraints: BoxConstraints(minHeight: _c_min_thumb_height),
                child: Center(child: CircularProgressIndicator()),
              )
            ]);
          else
            return Column(
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(minHeight: _c_min_thumb_height),
                  child: Container(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.all(_c_general_padding),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[]
                          ..addAll(
                            state.propertyMedias.where((pm) => pm.media.vn?.original.vn?.url.vn != null).map(
                                  (pm) => Card(
                                    margin: EdgeInsets.fromLTRB(0, 0, _c_separation_between_thumbs, 0),
                                    child: Container(
                                      padding: EdgeInsets.all(_c_thumb_card_padding),
                                      child: Column(
                                        children: [
                                          Container(
                                            margin:
                                                EdgeInsets.fromLTRB(0, 0, 0, _c_separation_between_image_and_buttons),
                                            child: Stack(
                                              clipBehavior: Clip.none,
                                              alignment: AlignmentDirectional.topEnd,
                                              children: [
                                                Image.network(
                                                  pm.media.v.thumbnail.vn?.url.vn ?? pm.media.v.original.v.url.v,
                                                  height: 170,
                                                  fit: BoxFit.fitHeight,
                                                ),
                                                if (!widget.readOnly)
                                                  Container(
                                                    alignment: Alignment.bottomRight,
                                                    height: 170,
                                                    padding: EdgeInsets.fromLTRB(5, 5, 5, 5),
                                                    child: IconButton(
                                                      icon: Stack(alignment: Alignment.center, children: [
                                                        Icon(Icons.delete,
                                                            color: Colors.white.withAlpha(200), size: 24),
                                                        Icon(Icons.delete, color: Colors.red, size: 20),
                                                      ]),
                                                      tooltip: "borrar",
                                                      //color: Colors.white,
                                                      onPressed: () => context.bloc<PropertymediasBannerBloc>().add(
                                                          PropertymediasBannerOnRemoveMedia(
                                                              mediaKey: pm.media.v.key.v)),
                                                    ),
                                                  ),
                                                if (!widget.readOnly)
                                                  Container(
                                                    alignment: Alignment.topLeft,
                                                    padding: EdgeInsets.fromLTRB(5, 0, 5, 5),
                                                    child: IconButton(
                                                      icon: Stack(alignment: Alignment.center, children: [
                                                        Icon(
                                                          Icons.star_outline,
                                                          color: Colors.black.withAlpha(128),
                                                          size: 24,
                                                        ),
                                                        (pm.isFavourite.vn ?? false)
                                                            ? Icon(Icons.star, color: Colors.white, size: 20)
                                                            : Icon(Icons.star,
                                                                color: Colors.white.withAlpha(128), size: 20)
                                                      ]),
                                                      tooltip:
                                                          (pm.isFavourite.vn ?? false) ? "no destacar" : "destacar",
                                                      onPressed: () {
                                                        if (pm.isFavourite.vn ?? false)
                                                          context.bloc<PropertymediasBannerBloc>().add(
                                                                PropertymediasBannerOnUnsetAsFavourite(
                                                                  mediaKey: pm.media.v.key.v,
                                                                ),
                                                              );
                                                        else
                                                          context.bloc<PropertymediasBannerBloc>().add(
                                                                PropertymediasBannerOnSetAsFavourite(
                                                                  mediaKey: pm.media.v.key.v,
                                                                ),
                                                              );
                                                      },
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                          )
                          ..addAll(
                            uploadingImages.map(
                              (uploadingImage) => Container(
                                padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                                width: 72,
                                child: PropertymediaUploader(
                                  offerId: widget.offerId,
                                  pickedImage: uploadingImage,
                                  onError: (error) {
                                    print("Error uploading image $error");
                                    setState(() {
                                      uploadingImages.remove(uploadingImage);
                                    });
                                  },
                                  onUploaded: (propertymedia) {
                                    setState(() {
                                      uploadingImages.remove(uploadingImage);
                                    });
                                    context.bloc<PropertymediasBannerBloc>().add(
                                          PropertymediasBannerOnMediaAdded(propertymedia: propertymedia),
                                        );
                                  },
                                ),
                              ),
                            ),
                          ),
                      ),
                    ),
                  ),
                ),
                if (!widget.readOnly)
                  TextButton.icon(
                    icon: Icon(Icons.add_photo_alternate),
                    label: Text("Añade una imagen"),
                    onPressed: () async {
                      //final pickedFile = await ImagePicker.pickImage(source: ImageSource.gallery);
                      PickedFile? pickedFile = await ImagePicker().getImage(source: ImageSource.gallery);
                      if (pickedFile != null) {
                        setState(() {
                          uploadingImages.add(pickedFile);
                        });
                        //context.bloc<OffersheetBloc>().add(OffersheetOnImagePicked(pickedImage: pickedFile));
                      } else {
                        print('No se ha seleccionado ninguna imagen');
                      }
                    },
                  ),
              ],
            );
        },
      ),
    );
  }
}
