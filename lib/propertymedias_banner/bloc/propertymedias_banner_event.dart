part of 'propertymedias_banner_bloc.dart';

abstract class PropertymediasBannerEvent extends Equatable {
  const PropertymediasBannerEvent();

  @override
  List<Object> get props => [];
}

class PropertymediasBannerOnFetch extends PropertymediasBannerEvent {
  PropertymediasBannerOnFetch() : super();
  @override
  List<Object> get props => [];
}

class PropertymediasBannerOnMediaAdded extends PropertymediasBannerEvent {
  final PropertymediaDto propertymedia;
  PropertymediasBannerOnMediaAdded({required this.propertymedia}) : super();

  @override
  List<Object> get props => [propertymedia.media.vn?.key.vn ?? ""];
}

class PropertymediasBannerOnRemoveMedia extends PropertymediasBannerEvent {
  final String mediaKey;
  PropertymediasBannerOnRemoveMedia({required this.mediaKey}) : super();

  @override
  List<Object> get props => [mediaKey];
}

class PropertymediasBannerOnSetAsFavourite extends PropertymediasBannerEvent {
  final String mediaKey;
  PropertymediasBannerOnSetAsFavourite({required this.mediaKey}) : super();

  @override
  List<Object> get props => [mediaKey];
}

class PropertymediasBannerOnUnsetAsFavourite extends PropertymediasBannerEvent {
  final String mediaKey;
  PropertymediasBannerOnUnsetAsFavourite({required this.mediaKey}) : super();

  @override
  List<Object> get props => [mediaKey];
}
