part of 'propertymedias_banner_bloc.dart';

int _instance_counter = 0;

class PropertymediasBannerState extends Equatable {
  final instance = ++_instance_counter;
  final String offerId;
  final bool readOnly;
  final List<PropertymediaDto> propertyMedias;

  PropertymediasBannerState({
    required this.offerId,
    this.readOnly = true,
    this.propertyMedias = const [],
  }) : super();

  @override
  List<Object> get props => [instance];
}

/*class PropertymediaBannerInitialState extends PropertymediasBannerState {
  
}*/
class PropertymediaBannerFetchingState extends PropertymediasBannerState {
  PropertymediaBannerFetchingState({
    required String offerId,
    required bool readOnly,
    List<PropertymediaDto> propertyMedias = const [],
  }) : super(offerId: offerId, readOnly: readOnly, propertyMedias: propertyMedias);

  factory PropertymediaBannerFetchingState.copyFrom(
    PropertymediasBannerState state, {
    String? offerId,
    bool? readOnly,
    List<PropertymediaDto>? propertyMedias,
  }) {
    return PropertymediaBannerFetchingState(
      offerId: offerId ?? state.offerId,
      readOnly: readOnly ?? state.readOnly,
      propertyMedias: propertyMedias ?? state.propertyMedias,
    );
  }
}
class PropertymediaBannerFetchedState extends PropertymediasBannerState {
  PropertymediaBannerFetchedState({
    required String offerId,
    required bool readOnly,
    List<PropertymediaDto> propertyMedias = const [],
  }) : super(offerId: offerId, readOnly: readOnly, propertyMedias: propertyMedias);

  factory PropertymediaBannerFetchedState.copyFrom(
    PropertymediasBannerState state, {
    String? offerId,
    bool? readOnly,
    List<PropertymediaDto>? propertyMedias,
  }) {
    return PropertymediaBannerFetchedState(
      offerId: offerId ?? state.offerId,
      readOnly: readOnly ?? state.readOnly,
      propertyMedias: propertyMedias ?? state.propertyMedias,
    );
  }
}

class PropertymediaBannerFailureState extends PropertymediasBannerState {
  final String error;

  PropertymediaBannerFailureState({
    required this.error,
    required String offerId,
    required bool readOnly,
    required List<PropertymediaDto> propertyMedias,
  }) : super(offerId: offerId, readOnly: readOnly, propertyMedias: propertyMedias);

  factory PropertymediaBannerFailureState.copyFrom(
    PropertymediasBannerState state, {
    required String error,
  }) {
    return PropertymediaBannerFailureState(
      offerId: state.offerId,
      readOnly: state.readOnly,
      propertyMedias: state.propertyMedias,
      error: error,
    );
  }
  @override
  List<Object> get props => [instance,offerId, propertyMedias, error];
}
