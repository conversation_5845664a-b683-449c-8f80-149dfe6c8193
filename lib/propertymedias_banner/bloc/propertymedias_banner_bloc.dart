import 'dart:async';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:rxdart/rxdart.dart';

part 'propertymedias_banner_event.dart';
part 'propertymedias_banner_state.dart';

///
/// Gestiona estados y eventos widget de subida de fichero
///

const _c_msgerror_fetching = "Problemas obteniendo las imágenes";

class PropertymediasBannerBloc extends Bloc<PropertymediasBannerEvent, PropertymediasBannerState> {
  final AppModelsNS appModels;
  
  late StreamSubscription<ModelsChannelState> _offersChannelSubscription;

  PropertymediasBannerBloc({
    required offerId,
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
    bool readOnly = false,
  }) : super(PropertymediasBannerState(offerId: offerId, readOnly:readOnly)) {
    this._offersChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelUpdatedState<OfferDto>) {
        if (state.entities.map((o) => o.id.vn).contains(this.state.offerId)) {
          this.add(PropertymediasBannerOnFetch());
        }
      }
    });
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _offersChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  factory PropertymediasBannerBloc.create(
    BuildContext context, {
    required String offerId,
  }) {
    return PropertymediasBannerBloc(
      offerId: offerId,
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
    )..add(PropertymediasBannerOnFetch());
  }

  @override
  Stream<Transition<PropertymediasBannerEvent, PropertymediasBannerState>> transformEvents(
    Stream<PropertymediasBannerEvent> events,
    TransitionFunction<PropertymediasBannerEvent, PropertymediasBannerState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<PropertymediasBannerState> mapEventToState(PropertymediasBannerEvent event) async* {
    if (event is PropertymediasBannerOnFetch) {
      yield* _mapOnFetch(state, event);
    } else if (event is PropertymediasBannerOnMediaAdded) {
      yield* _mapOnMediaAdded(state, event);
    } else if (event is PropertymediasBannerOnRemoveMedia) {
      yield* _mapOnRemoveMedia(state, event);
    } else if (event is PropertymediasBannerOnSetAsFavourite) {
      yield* _mapOnSetAsFavourite(state, event);
    } else if (event is PropertymediasBannerOnUnsetAsFavourite) {
      yield* _mapOnUnsetAsFavourite(state, event);
    }
  }

  Stream<PropertymediasBannerState> _mapOnFetch(
      PropertymediasBannerState state, PropertymediasBannerOnFetch event) async* {
    try {
      yield PropertymediaBannerFetchingState.copyFrom(state);
      await Future.delayed(Duration(milliseconds: 100));
      final propertymedias = await appModels.listOfferPropertymedias(state.offerId);
      yield PropertymediaBannerFetchedState.copyFrom(
        state,
        propertyMedias: propertymedias,
      );
    } on Exception {
      yield PropertymediaBannerFailureState.copyFrom(state, error: _c_msgerror_fetching);
    }
  }

  Stream<PropertymediasBannerState> _mapOnMediaAdded(
      PropertymediasBannerState state, PropertymediasBannerOnMediaAdded event) async* {
    yield PropertymediaBannerFetchedState.copyFrom(
      state,
      propertyMedias: []
        ..addAll(state.propertyMedias)
        ..add(event.propertymedia),
    );
  }

  Stream<PropertymediasBannerState> _mapOnRemoveMedia(
      PropertymediasBannerState state, PropertymediasBannerOnRemoveMedia event) async* {
    try {
      final offerId = state.offerId;
      final count = await appModels.removeOfferPropertymedia(offerId, event.mediaKey);
      if (count != 0) {
        yield PropertymediaBannerFetchedState.copyFrom(
          state,
          propertyMedias: state.propertyMedias.where((element) => element.media.v.key.v != event.mediaKey).toList(),
        );
      }
    } on Exception catch (e) {
      print("Error removing propertymedia $e");
      //yield PropertymediaBannerFailureState.copyFrom(state, error: _c_msgerror_fetching);
    }
  }

  Stream<PropertymediasBannerState> _mapOnSetAsFavourite(
      PropertymediasBannerState state, PropertymediasBannerOnSetAsFavourite event) async* {
    try {
      final offerId = state.offerId;
      //final updated =
      await appModels.updateOfferPropertymedia(
        offerId,
        event.mediaKey,
        PropertymediaDto.fromJson({"isFavourite": true}),
      );

      final propertymedias = await appModels.listOfferPropertymedias(offerId);

      yield PropertymediaBannerFetchedState.copyFrom(state, propertyMedias: propertymedias);
    } on Exception catch (e) {
      print("Error updating propertymedia $e");
    }
  }

  Stream<PropertymediasBannerState> _mapOnUnsetAsFavourite(
    PropertymediasBannerState state,
    PropertymediasBannerOnUnsetAsFavourite event,
  ) async* {
    try {
      final offerId = state.offerId;
      //final updated =
      await appModels.updateOfferPropertymedia(
        offerId,
        event.mediaKey,
        PropertymediaDto(isFavourite: Some(false)),
      );

      final propertymedias = await appModels.listOfferPropertymedias(offerId);
      yield PropertymediaBannerFetchedState.copyFrom(state, propertyMedias: propertymedias);
    } on Exception catch (e) {
      print("Error updating propertymedia $e");
    }
  }
}
