part of 'action_edit_bloc.dart';

class ActionEditState extends Equatable {
  @override
  List<Object> get props => [];
}

class ActionEditLoading extends ActionEditState {}

class ActionEditLoaded extends ActionEditState {
  final ActionDto action;
  final List<ActiontypeDto> actiontypes;
  final bool isReadOnly;

  ActionEditLoaded({required this.action, required this.actiontypes, required this.isReadOnly}) : super();

  @override
  List<Object> get props => [action, actiontypes.length, isReadOnly];
}

class ActionEditLoadFailure extends ActionEditState {
  final String error;

  ActionEditLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

class ActionEditSaved extends ActionEditLoaded {
  
  ActionEditSaved({required ActionDto action, required List<ActiontypeDto> actiontypes}) : super(action:action, actiontypes:actiontypes, isReadOnly: false);
  factory ActionEditSaved.fromPrevious(ActionEditLoaded status, {required ActionDto action}) {
    return ActionEditSaved(action: action, actiontypes: status.actiontypes);
  }
  
}

class ActionEditSaveFailure extends ActionEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  ActionEditSaveFailure({required action, required actiontypes,  required this.error})
      : super(action: action, actiontypes: actiontypes, isReadOnly: false);

  factory ActionEditSaveFailure.fromPrevious(ActionEditLoaded status, {required String error}) {
    return ActionEditSaveFailure(action: status.action, actiontypes: status.actiontypes, error: error);
  }
  @override
  List<Object> get props => [lastError, action, error];
}

class ActionEditValidationFailure extends ActionEditLoaded {
  final lastError = new DateTime.now();
  final String error;
  ActionEditValidationFailure({required action, required actiontypes, required this.error})
      : super(action: action, actiontypes: actiontypes, isReadOnly: true);

  factory ActionEditValidationFailure.fromPrevious(ActionEditLoaded status, {required String error}) {
    return ActionEditValidationFailure(action: status.action, actiontypes: status.actiontypes, error: error);
  }

  @override
  List<Object> get props => [lastError, action, error];
}
