import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:provider/provider.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:topbrokers/global/session_bloc.dart';

part 'action_edit_event.dart';
part 'action_edit_state.dart';

class ActionEditBloc extends Bloc<ActionEditEvent, ActionEditState> {
  final AppModelsNS appModels;
  final SessionBloc sessionBloc;

  ActionEditBloc({required this.appModels, required this.sessionBloc}) : super(ActionEditState());

  factory ActionEditBloc.create(BuildContext context) {

    return ActionEditBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      sessionBloc: BlocProvider.of<SessionBloc>(context, listen: false),
    );
  }

  @override
  Stream<Transition<ActionEditEvent, ActionEditState>> transformEvents(
    Stream<ActionEditEvent> events,
    TransitionFunction<ActionEditEvent, ActionEditState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<ActionEditState> mapEventToState(ActionEditEvent event) async* {
    final state = this.state;
    if (event is ActionEditOnEditEvent) {
      yield* _mapOnEditEventToState(state, event);
    } else if (event is ActionEditOnNewEvent) {
      yield* _mapOnNewEventToState(state, event);
    } else if (event is ActionEditOnSaveEvent && state is ActionEditLoaded) {
      yield* _mapOnSaveEventToState(state, event);
    } else if (event is ActionEditOnValidationErrorEvent && state is ActionEditLoaded) {
      yield* _mapOnValidationErrorEventToState(state, event);
    }
  }

  Stream<ActionEditState> _mapOnEditEventToState(ActionEditState state, ActionEditOnEditEvent event) async* {
    try {
      final action = await _readAction(event.actionId);
      final actiontypes = await _listActiontypes(forCrUp: true);
      if (action == null)
        yield ActionEditLoadFailure(error: "La acción no existe");
      else if (action.type is None || action.type.v.service is None)
        yield ActionEditLoadFailure(error: "Falta información asociada al tipo de la acción");
      else if (!_agentCanCrUpActionsOf(action.type.v))
        yield ActionEditLoaded(action: action, actiontypes: actiontypes, isReadOnly: true);
      else if (action.type.vn?.service.vn != null)
        yield ActionEditLoadFailure(error: "Esta acción es una solicitud de servicio y no puede ser editada");
      else
        yield ActionEditLoaded(action: action, actiontypes: actiontypes, isReadOnly: false);
    } on Exception {
      yield ActionEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<ActionEditState> _mapOnNewEventToState(ActionEditState state, ActionEditOnNewEvent event) async* {
    try {
      final action = _emptyAction();
      final contactId = event.contactId;
      final actiontypeId = event.actiontypeId;

      if (contactId != null) {
        final contact = await appModels.readContact(contactId);
        action.contact = Some(contact);
      }
      final offerId = event.offerId;
      if (offerId != null) {
        action.offer = Some(await appModels.readOffer(offerId));
      }
      final actiontypes = await _listActiontypes(actiontypeId: actiontypeId, forCrUp: true);
      yield ActionEditLoaded(action: action, actiontypes: actiontypes, isReadOnly: false);
    } on Exception {
      yield ActionEditLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<ActionEditState> _mapOnSaveEventToState(ActionEditLoaded state, ActionEditOnSaveEvent event) async* {
    try {
      if (event.action.id.vn == null) {
        final created = await _createAction(event.action);
        yield ActionEditSaved.fromPrevious(state, action: created);
        // Notificamos a quien le interese que la oferta ha cambiado.

      } else {
        final updated = await _updateAction(event.action);
        yield ActionEditSaved.fromPrevious(state, action: updated);
        // Notificamos a quien le interese que la oferta ha cambiado.

      }
    } on Exception {
      // catch (e) {
      final msg = event.action.id.vn == null ? "Problemas guardando la nueva acción" : "Problemas guardando cambios";
      yield ActionEditSaveFailure.fromPrevious(state, error: msg);
    }
  }

  Stream<ActionEditState> _mapOnValidationErrorEventToState(
      ActionEditLoaded state, ActionEditOnValidationErrorEvent event) async* {
    yield ActionEditValidationFailure.fromPrevious(state, error: event.error);
  }

  Future<ActionDto?> _readAction(String id) async {
    return appModels.readAction(id);
  }

  Future<ActionDto> _updateAction(ActionDto action) async {
    assert(action.id.vn != null);
    return appModels.updateAction(action);
  }

  Future<ActionDto> _createAction(ActionDto action) async {
    return appModels.createAction(action);
  }

  ActionDto _emptyAction() {
    return ActionDto.fromJson({"when": DateTime.now(), "done": false});
  }

  Future<List<ActiontypeDto>> _listActiontypes({String? actiontypeId, bool forCrUp = true}) async {
    if (actiontypeId != null) {
      final types = await appModels.listActiontypes(
        filter: ActiontypesListFilter(id: Some(actiontypeId)),
      ); //filter: ActiontypesListFilter(withService: False));
      return types.where((t) => forCrUp == (t.agentCanCreate.vn ?? true)).toList();
    } else {
      var types = await appModels.listActiontypes(
        filter: ActiontypesListFilter(withService: False),
      ); //filter: ActiontypesListFilter(withService: False));
      return types.where((t) => !forCrUp || _agentCanCrUpActionsOf(t)).toList();
    }
  }

  _agentCanCrUpActionsOf(ActiontypeDto actiontype) {
    if (sessionBloc.state.imIndividual) {
      return [
        "7", // "7"	"{""ca"": ""Reserva"", ""en"": ""Reservation"", ""es"": ""Reserva"", ""default"": ""Reservation""}"
        "8", // "8"	"{""ca"": ""Signar arres"", ""en"": ""Sign deposit"", ""es"": ""Firmar arras"", ""default"": ""Sign deposit""}"
        "9", // "9"	"{""ca"": ""Signar escriptures"", ""en"": ""Sign deeds"", ""es"": ""Firmar escrituras"", ""default"": ""Sign deeds""}"
        "2", // "2"	"{""ca"": ""Trucada"", ""en"": ""Phonen call"", ""es"": ""Llamada"", ""default"": ""Phone call""}"
        "10", // "10"	"{""ca"": ""eMail"", ""en"": ""eMail"", ""es"": ""eMail"", ""default"": ""eMail""}"
        "1", // "1"	"{""ca"": ""Visita"", ""en"": ""Visit"", ""es"": ""Visita"", ""default"": ""Visit""}"
        "11", // "11"	"{""ca"": ""Missatge"", ""en"": ""Messatge"", ""es"": ""Mensaje"", ""default"": ""Messatge""}"
      ].contains(actiontype.id.vn);
    } else {
      return actiontype.agentCanCreate != False;
    }
  }
}
