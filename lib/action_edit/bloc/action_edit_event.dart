part of 'action_edit_bloc.dart';

abstract class ActionEditEvent extends Equatable {
  const ActionEditEvent();

  @override
  List<Object> get props => [];
}

class ActionEditOnEditEvent extends ActionEditEvent {
  final String actionId;
  ActionEditOnEditEvent({required this.actionId});
  @override
  List<Object> get props => [actionId];
}

class ActionEditOnNewEvent extends ActionEditEvent {
  final String? actiontypeId;
  final String? contactId;
  final String? offerId;

  ActionEditOnNewEvent({this.contactId, this.offerId, this.actiontypeId}) : super();
  @override
  List<Object> get props => [contactId??"", offerId??"", actiontypeId??""];
}

///
/// El usuario quiere guardar los cambios
///
class ActionEditOnSaveEvent extends ActionEditEvent {
  final ActionDto action;
  ActionEditOnSaveEvent({required this.action}) : super();

  @override
  List<Object> get props => [action];
}

class ActionEditOnValidationErrorEvent extends ActionEditEvent {
  final String error;
  ActionEditOnValidationErrorEvent({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

///
/// Los cambios se han guardado corréctamente
///
class ActionEditOnSavedEvent extends ActionEditEvent {}
