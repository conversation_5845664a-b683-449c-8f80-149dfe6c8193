import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:form_builder/form_builder.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/action_edit/bloc/action_edit_bloc.dart';
import 'package:topbrokers/action_edit/models/action_edit_page_params.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/contact_long_link.dart';
import 'package:topbrokers/common/widgets/offer_long_link.dart';
import 'package:topbrokers/common/widgets/titled_card.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/contact_edit/models/contact_edit_page_result.dart';
import 'package:topbrokers/contacts_list/contacts.dart';
import 'package:topbrokers/offers_list/widgets/offer_list_item.dart';
import 'package:topbrokers/routes.dart';
import 'package:url_launcher/url_launcher_string.dart';

typedef OnSaveActionCallback = Function(ActionDto action);

const _c_lineheight = 1.5;

class ActionEditPage extends StatefulWidget {
  //final OnSaveActionCallback onSave;
  final bool isNew;
  final ActionEditPageParams params;
  //final ActionDto action;

  ActionEditPage({Key? key, required this.params})
      : this.isNew = params is NewActionEditParams,
        super(key: key ?? AgentorKeys.editActionPage);

  @override
  _ActionEditPageState createState() => _ActionEditPageState();
}

class _ActionEditPageState extends State<ActionEditPage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final api = Deps.solve<ApiServices>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        if (widget.isNew) {
          final params = this.widget.params as NewActionEditParams;
          return ActionEditBloc.create(context)
            ..add(ActionEditOnNewEvent(
                contactId: params.contactId, offerId: params.offerId, actiontypeId: params.actiontypeId));
        } else {
          final params = this.widget.params as ExistingActionEditParams;
          return ActionEditBloc.create(context)..add(ActionEditOnEditEvent(actionId: params.id));
        }
      },
      child: BlocConsumer<ActionEditBloc, ActionEditState>(listener: (context, state) {
        if (state is ActionEditSaved) {
          Navigator.pop(context);
        }
      }, builder: (context, state) {
        if (state is ActionEditLoaded) {
          return _buildPageScaffold(context, state);
        } else if (state is ActionEditLoadFailure) {
          return Center(child: Text(state.error));
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      }),
    );
  }

  Scaffold _buildPageScaffold(BuildContext context, ActionEditLoaded state) {
    final isReadOnly = state.isReadOnly; // !widget.isNew && action.type.vn?.service.vn != null;

    final localization = context.getAppLocalizationsOrThrow();
    return Scaffold(
        appBar: AppBar(
          title: Text(widget.isNew ? localization.action_titleNew : localization.action_title),
          centerTitle: true,
        ),
        body: WidthLimiter(
          child: BlocConsumer<ActionEditBloc, ActionEditState>(
            listener: (context, state) {
              if (state is ActionEditSaveFailure) {
                context.showError(state.error);
              } else if (state is ActionEditValidationFailure) {
                context.showError(state.error);
              }
            },
            builder: (context, state) {
              // ActionformSaveFailure y ActionformValidationFailure
              // extienden ActionformLoaded
              if (isReadOnly)
                return _buildSheet(
                  context,
                  (state as ActionEditLoaded).action,
                  actiontypes: state.actiontypes,
                );
              else
                return _buildForm(
                  context,
                  (state as ActionEditLoaded).action,
                  actiontypes: state.actiontypes,
                );
            },
          ),
        ),
        floatingActionButton: isReadOnly
            ? null
            : FloatingActionButton(
                key: AgentorKeys.saveAction,
                tooltip: widget.isNew ? localization.common_create : localization.common_acceptChanges,
                child: Icon(Icons.save_outlined, semanticLabel: context.apploc.common_Accept),
                onPressed: () async {
                  if (_formKey.currentState?.validate() ?? false) {
                    _formKey.currentState?.save();
                    BlocProvider.of<ActionEditBloc>(context).add(ActionEditOnSaveEvent(action: state.action));
                  } else {
                    BlocProvider.of<ActionEditBloc>(context)
                        .add(ActionEditOnValidationErrorEvent(error: localization.common_formWithErrors));
                  }
                },
              ));
  }

  Widget _buildForm(BuildContext context, ActionDto action, {required Iterable<ActiontypeDto> actiontypes}) {
    return new Container(
      child: new SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            _actionFormEntries(context, action, actiontypes: actiontypes).buildForm(key: _formKey, context: context),
            SizedBox.fromSize(size: Size.fromHeight(64))
          ],
        ),
      ),
    );
  }

  Widget _buildSheet(BuildContext context, ActionDto action, {required Iterable<ActiontypeDto> actiontypes}) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    final localizations = context.getAppLocalizationsOrThrow();
    return new Container(
      child: new SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            TitledCard(
              Text(context.apploc.action_mainHeading, style: textTheme.titleLarge),
              children: [
                RichText(
                  softWrap: true,
                  textAlign: TextAlign.left,
                  text: TextSpan(
                    text: "",
                    style: textStyle,
                    children: _addSeparation(
                      [
                        _buildCustomEntrySpan(
                          context: context,
                          labelText: localizations.action_typeLabel,
                          valueText: action.type.vn?.label.vn?.localized,
                        ),
                        if (action.when.vn != null)
                          _buildCustomEntrySpan(
                            context: context,
                            labelText: localizations.action_whenLabel,
                            valueText: action.when.vn?.toLocal().formatRelativeToNow() ?? "",
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            if (action.contact.vn != null)
              TitledCard(
                Text(localizations.action_contactLabel, style: textTheme.titleLarge),
                children: [
                  ContactLongLink(
                    contact: action.contact.v!,
                    textAlign: TextAlign.left,
                    isDense: false,
                  ),
                ],
              ),
            if (action.offer.vn != null)
              TitledCard(
                Text(localizations.action_offerLabel, style: textTheme.titleLarge),
                children: [
                  OfferLongLink(
                    offer: action.offer.v!,
                    textAlign: TextAlign.left,
                    isDense: false,
                  )
                ],
              ),
            if (action.description.vn != null)
              TitledCard(
                Text(localizations.action_notesLabel, style: textTheme.titleLarge),
                children: [
                  Text(
                    action.description.v!,
                    style: textTheme.bodyMedium,
                  ),
                ],
              )
          ],
        ),
      ),
    );
  }

  List<FormEntry> _actionFormEntries(BuildContext context, ActionDto action,
      {required Iterable<ActiontypeDto> actiontypes}) {
    final localizations = context.getAppLocalizationsOrThrow();
    final service = action.type.vn?.service.vn;
    // Si el tipo de acción es un servicio y la acción no es nueva, estamos en modo solo lectura
    final disclaimer = service?.disclaimer.vn;

    return <FormEntry>[
      GroupEntry(children: [
        SelectFieldEntry<String>(
          label: localizations.action_typeLabel,
          getValue: () => action.type.vn?.id.vn,
          setValue: (selectedId) {
            setState(() {
              final selectedType = actiontypes.firstWhere((atype) => atype.id.v == selectedId, orElse: null);
              action.type = Some(selectedType);
            });
          },
          isRequired: true,
          options: actiontypes.map((atype) {
            return SelectOption<String>(value: atype.id.v, label: atype.label.vn?.localized ?? "");
          }).toList(),
        ),
        if (disclaimer != null)
          TextEntry(
            label: disclaimer.title.vn?.localized ?? "",
            sufix: TextButton.icon(
              onPressed: () async {
                final title = disclaimer.title.vn?.localized ?? "";
                final body = disclaimer.detailHtml.vn?.localized ?? "";
                await _showDialog(context, title, body);
              },
              icon: Icon(Icons.open_in_new),
              label: Text(localizations.common_detailsLabel),
            ),
          ),
        if (service == null)
          SimpleFieldEntry<DateTime>(
            label: localizations.action_whenLabel,
            getValue: () => action.when.vn,
            setValue: (v) {
              setState(() {
                action.when = v == null ? None() : Some(v);
              });
            },
            isRequired: true,
          ),
        if (service == null)
          SimpleFieldEntry<bool>(
            label: localizations.action_doneLabel,
            getValue: () => action.done.vn,
            setValue: (v) {
              setState(() {
                action.done = Some(v ?? false);
              });
            },
          ),
        SearchFieldEntry<ContactDto>(
          label: localizations.action_contactLabel,
          getValue: () => action.contact.vn,
          setValue: (contact) {
            setState(() {
              action.contact = Some(contact);
            });
          },
          onSearch: (String search) => api.listContacts(filter: ContactsListFilter(search: Some(search))),
          itemBuilder: (context, contact, {bool isSelected = false}) => ContactListItem(contact: contact),
          valueToString: (contact) => contact != null ? contact.name.vn ?? "" : localizations.common_selectAContact,
          isRequired: action.type.vn?.contactIsRequired.vn ?? false,
          onAdd: () async {
            final result = await Navigator.pushNamed(context, AgentorRoutes.addContact);
            if (result is ContactEditPageResult && result.id != null) {
              try {
                return await api.getContact(result.id as String);
              } on Exception {
                print("Problemas obteniendo/asignando contacto");
              }
            }
            return null;
          },
        ),
        SearchFieldEntry<OfferDto>(
          label: localizations.action_offerLabel,
          getValue: () => action.offer.vn,
          setValue: (OfferDto? value) => setState(() => action.offer = Some(value)),
          onSearch: (String search) => api.listOffers(
              filter: OffersListFilter(
            search: search,
            includeMine: True,
            includeNotMineFavourites: True,
          )),
          itemBuilder: (context, offer, {bool isSelected = false}) => OfferListItem(offer: offer),
          valueToString: (offer) => offer != null ? offer.name.vn ?? "" : localizations.common_selectAnOffer,
          isRequired: action.type.vn?.offerIsRequired.vn ?? false,
        ),
        SimpleFieldEntry<String>(
          label: localizations.action_notesLabel,
          getValue: () => action.description.vn,
          setValue: (newValue) {
            setState(() {
              action.description = Some(newValue);
            });
          },
          isRequired: false,
          isMultiline: true,
        )
      ])
    ];
  }

  Future<void> _showDialog(BuildContext context, String title, String bodyHtml) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          //title: Text(title),
          content: HTML.toRichText(context, bodyHtml.replaceAll('<li>', " - ").replaceAll('</li>', "<br />"),
              linksCallback: (link) async {
            if (await canLaunchUrlString(link)) {
              await launchUrlString(link);
            }
          }),
          buttonPadding: EdgeInsets.fromLTRB(20, 0, 20, 0),
          actions: <Widget>[
            ElevatedButton(
              child: Text(context.apploc.common_close),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  List<InlineSpan> _addSeparation(List<Iterable<InlineSpan>?> texts) {
    return texts.fold(
      <InlineSpan>[],
      (allSpans, spans) {
        if (spans == null)
          return allSpans;
        else
          return allSpans
            ..addAll([
              if (allSpans.length != 0 && spans.length != 0) TextSpan(text: "\n"),
              if (spans.length != 0) TextSpan(text: "- "),
            ])
            ..addAll(spans);
      },
    );
  }

  Iterable<TextSpan> _buildCustomEntrySpan(
      {required BuildContext context, required String labelText, String? valueText}) {
    final textTheme = Theme.of(context).textTheme;
    final lbStyle = textTheme.bodyMedium;
    final txtStyle = textTheme.bodyLarge;
    final label = TextSpan(text: "$labelText: ".capitalize, style: lbStyle);
    if (valueText != null && valueText.length != 0) {
      return [label, TextSpan(text: "$valueText", style: txtStyle)];
    } else {
      return [];
    }
  }
}
