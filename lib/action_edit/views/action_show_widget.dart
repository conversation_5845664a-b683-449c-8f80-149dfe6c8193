import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/contact_long_link.dart';
import 'package:topbrokers/common/widgets/offer_long_link.dart';
import 'package:topbrokers/common/widgets/titled_card.dart';

typedef OnSaveActionCallback = Function(ActionDto action);

const _c_lineheight = 1.5;

class ActionShowWidget extends StatelessWidget {
  //final OnSaveActionCallback onSave;
  final ActionDto action;
  final List<ActiontypeDto> actiontypes;

  ActionShowWidget({Key? key, required this.action, required this.actiontypes}) : super(key:key);

  @override
  Widget build(BuildContext context) => _buildSheet(context, this.action, actiontypes: this.actiontypes);

  Widget _buildSheet(BuildContext context, ActionDto action, {required Iterable<ActiontypeDto> actiontypes}) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    final localizations = context.getAppLocalizationsOrThrow();
    return new Container(
      child: new SingleChildScrollView(
        padding: EdgeInsets.all(4),
        child: Column(
          children: [
            TitledCard(
              Text(context.apploc.action_mainHeading, style: textTheme.titleLarge),
              children: [
                RichText(
                  softWrap: true,
                  textAlign: TextAlign.left,
                  text: TextSpan(
                    text: "",
                    style: textStyle,
                    children: _addSeparation(
                      [
                        _buildCustomEntrySpan(
                          context: context,
                          labelText: localizations.serviceaction_typeLabel,
                          valueText: action.type.vn?.label.vn?.localized,
                        ),
                        if (action.when.vn != null)
                          _buildCustomEntrySpan(
                            context: context,
                            labelText: localizations.serviceaction_whenLabel,
                            valueText: action.when.vn?.toLocal().formatRelativeToNow() ?? "",
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            if (action.contact.vn != null)
              TitledCard(
                Text(localizations.serviceaction_contactLabel, style: textTheme.titleLarge),
                children: [
                  ContactLongLink(
                    contact: action.contact.v!,
                    textAlign: TextAlign.left,
                    isDense: false,
                  ),
                ],
              ),
            if (action.offer.vn != null)
              TitledCard(
                Text(localizations.serviceaction_offerLabel, style: textTheme.titleLarge),
                children: [
                  OfferLongLink(
                    offer: action.offer.v!,
                    textAlign: TextAlign.left,
                    isDense: false,
                  )
                ],
              ),
          ],
        ),
      ),
    );
  }

  List<InlineSpan> _addSeparation(List<Iterable<InlineSpan>?> texts) {
    return texts.fold(
      <InlineSpan>[],
      (allSpans, spans) {
        if (spans == null)
          return allSpans;
        else
          return allSpans
            ..addAll([
              if (allSpans.length != 0 && spans.length != 0) TextSpan(text: "\n"),
              if (spans.length != 0) TextSpan(text: "- "),
            ])
            ..addAll(spans);
      },
    );
  }

  Iterable<TextSpan> _buildCustomEntrySpan({
    required BuildContext context,
    required String labelText,
    String? valueText,
  }) {
    final textTheme = Theme.of(context).textTheme;
    final lbStyle = textTheme.bodyMedium;
    final txtStyle = textTheme.bodyLarge;
    final label = TextSpan(text: "$labelText: ".capitalize, style: lbStyle);
    if (valueText != null && valueText.length != 0) {
      return [label, TextSpan(text: "$valueText", style: txtStyle)];
    } else {
      return [];
    }
  }
}