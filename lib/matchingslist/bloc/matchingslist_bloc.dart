import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'matchingslist_event.dart';
part 'matchingslist_state.dart';

class MatchingslistBloc extends Bloc<MatchingslistEvent, MatchingslistState> {
  static const _PAGE_SIZE = 30;
  final AppModelsNS appModels;

  /// Filtro aplicado a los matchings conrolados por este bloque
  final MatchingsListFilter filter;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;

  MatchingslistBloc({
    required ModelsChannelBloc modelsChannel,
    required this.appModels,
    this.filter = const MatchingsListFilter(),
  }) : super(MatchingslistState()) {
    _modelsChannelSubscription = modelsChannel.stream.listen((state) {
      // Esto causará que revisemos si alguna acción de la lista usa esta oferta
      if (state is ModelsChannelUpdatedState<DemandDto>) {
        this.add(MatchingslistOnDemandsUpdated(demands: state.entities));
      } else if (state is ModelsChannelUpdatedState<OfferDto>) {
        this.add(MatchingslistOnOffersUpdated(offers: state.entities));
      } else if (state is ModelsChannelUpdatedState<MatchingDto>) {
        this.add(MatchingslistOnRefresh());
      } else if (state is ModelsChannelCreatedState<MatchingDto>) {
        this.add(MatchingslistOnRefresh());
      } else if (state is ModelsChannelDeletedState<MatchingDto>) {
        this.add(MatchingslistOnRefresh());
      }
    });
  }

  factory MatchingslistBloc.createForOffer(BuildContext context, String offerId) {
    return MatchingslistBloc(
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      filter: MatchingsListFilter(
        offerId: Some(offerId),
        includeOfferData: False,
        includeDemandData: True,
      ),
    );
  }
  factory MatchingslistBloc.createForDemand(BuildContext context, String demandId) {
    return MatchingslistBloc(
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      filter: MatchingsListFilter(
        demandId: Some(demandId),
        includeOfferData: True,
        includeDemandData: False,
      ),
    );
  }
  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _modelsChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<MatchingslistEvent, MatchingslistState>> transformEvents(
    Stream<MatchingslistEvent> events,
    TransitionFunction<MatchingslistEvent, MatchingslistState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<MatchingslistState> mapEventToState(MatchingslistEvent event) async* {
    if (event is MatchingslistOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is MatchingslistOnFetch) {
      yield* _mapOnFetch(state, event);
    } else if (event is MatchingslistOnOffersUpdated) {
      yield* _mapOnOffersUpdated(state, event);
    } else if (event is MatchingslistOnDemandsUpdated) {
      yield* _mapOnDemandsUpdated(state, event);
    }
  }

  Stream<MatchingslistState> _mapOnOffersUpdated(
    MatchingslistState state,
    MatchingslistOnOffersUpdated event,
  ) async* {
    try {
      final updatedIds = Set.from(event.offers.map((d) => d.id.v));
      // Un cambio en nuestra oferta implica que los matchings se habrán recalculado
      final isMyOffer = filter.offerId.vn != null && updatedIds.contains(filter.offerId.v);
      // Si la oferta está implicada en alguno de los matchings
      final isInMatchings = state.matchings.indexWhere((matching) => updatedIds.contains(matching.offer.v.id.v)) != -1;

      if (isMyOffer || isInMatchings) {
        final matchings = await _listMatchings();
        yield state.copyWith(
          status: MatchingslistStatus.success,
          matchings: matchings,
          hasReachedMax: matchings.length != _PAGE_SIZE,
        );
      }
    } on Exception {
      // NADA
    }
  }

  Stream<MatchingslistState> _mapOnDemandsUpdated(
    MatchingslistState state,
    MatchingslistOnDemandsUpdated event,
  ) async* {
    try {
      final updatedIds = Set.from(event.demands.map((d) => d.id.v));
      // Si la demanda es aquella de la que mostramos sus matchigs...
      final isMyDemand = this.filter.demandId.vn != null && updatedIds.contains(this.filter.demandId.v);
      // Si la demanda está implicada en alguno de los matchings
      final isInMatchings = state.matchings.indexWhere((matching) => updatedIds.contains(matching.demand.v.id.v)) != -1;

      if (isMyDemand || isInMatchings) {
        final matchings = await _listMatchings();
        yield state.copyWith(
          status: MatchingslistStatus.success,
          matchings: matchings,
          hasReachedMax: matchings.length != _PAGE_SIZE,
        );
      }
    } on Exception {
      // NADA
    }
  }

  Stream<MatchingslistState> _mapOnRefresh(MatchingslistState state, MatchingslistOnRefresh event) async* {
    try {
      final matchings = await _listMatchings();
      final myAgent = await appModels.readMyAgent();
      if (myAgent != null)
        yield state.copyWith(status: MatchingslistStatus.failure);
      else
        yield state.copyWith(
          status: MatchingslistStatus.success,
          matchings: matchings,
          hasReachedMax: matchings.length != _PAGE_SIZE,
          myAgent: myAgent,
        );
    } on Exception {
      yield state.copyWith(status: MatchingslistStatus.failure);
    }
  }

  Stream<MatchingslistState> _mapOnFetch(MatchingslistState state, MatchingslistOnFetch event) async* {
    if (state.hasReachedMax) {
      yield state;
    } else {
      try {
        if (state.status == MatchingslistStatus.initial) {
          final matchings = await _listMatchings();
          final myAgent = await appModels.readMyAgent();
          yield state.copyWith(
            status: MatchingslistStatus.success,
            matchings: matchings,
            hasReachedMax: matchings.length != _PAGE_SIZE,
            myAgent: myAgent,
          );
        } else {
          // Este punto sirve tanto en estado success como failure si se reintentase
          final matchings = await _listMatchings(offset: state.matchings.length);
          yield matchings.isEmpty
              ? state.copyWith(
                  hasReachedMax: true,
                )
              : state.copyWith(
                  status: MatchingslistStatus.success,
                  matchings: List.of(state.matchings)..addAll(matchings),
                  hasReachedMax: false,
                );
        }
      } on Exception {
        yield state.copyWith(status: MatchingslistStatus.failure);
      }
    }
  }

  Future<List<MatchingDto>> _listMatchings({int offset = 0}) {
    return appModels.listMatchings(
      filter: this.filter,
      offset: offset,
      limit: _PAGE_SIZE,
    );
  }

  static DateTime endOfToday() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 23, 59, 59, 999, 999);
  }

  static DateTime startOfToday() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }
}
