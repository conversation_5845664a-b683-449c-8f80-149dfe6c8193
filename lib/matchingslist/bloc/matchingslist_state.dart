part of 'matchingslist_bloc.dart';

enum MatchingslistStatus { initial, success, failure }

class MatchingslistState extends Equatable {
  MatchingslistState({
    this.status = MatchingslistStatus.initial,
    this.matchings = const <MatchingDto>[],
    this.hasReachedMax = false,
    this.myAgent,
  });

  // Actual status of the screen
  final MatchingslistStatus status;
  // The list of actual fetched matchings
  final List<MatchingDto> matchings;
  // There is no more matchings to be fetched
  final bool hasReachedMax;
  //
  final AgentDto? myAgent;

  MatchingslistState copyWith({
    MatchingslistStatus? status,
    List<MatchingDto>? matchings,
    bool? hasReachedMax,
    AgentDto? myAgent,
  }) =>
      MatchingslistState(
        status: status ?? this.status,
        matchings: matchings ?? this.matchings,
        hasReachedMax: hasReachedMax ?? this.hasReachedMax,
        myAgent: myAgent ?? this.myAgent,
      );

  factory MatchingslistState.create({
    MatchingslistStatus status = MatchingslistStatus.initial,
    List<MatchingDto> matchings = const <MatchingDto>[],
    bool hasReachedMax = false,
    required AgentDto myAgent,
  }) {
    return MatchingslistState(
      status: status,
      matchings: matchings,
      hasReachedMax: hasReachedMax,
      myAgent: myAgent,
    );
  }

  @override
  List<Object> get props => [status, hasReachedMax, matchings];
}

class MatchingslistLoadFailure extends MatchingslistState {}
