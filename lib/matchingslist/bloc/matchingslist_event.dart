part of 'matchingslist_bloc.dart';

abstract class MatchingslistEvent extends Equatable {
  const MatchingslistEvent();

  @override
  List<Object> get props => [];
}

class MatchingslistOnRefresh extends MatchingslistEvent {}

class MatchingslistOnOffersUpdated extends MatchingslistEvent {
  final List<OfferDto> offers;
  MatchingslistOnOffersUpdated({
    required this.offers,
  }) : super();
}

class MatchingslistOnDemandsUpdated extends MatchingslistEvent {
  final List<DemandDto> demands;
  MatchingslistOnDemandsUpdated({
    required this.demands,
  }) : super();
  @override
  List<Object> get props => [demands];
}

class MatchingslistOnFetch extends MatchingslistEvent {}
