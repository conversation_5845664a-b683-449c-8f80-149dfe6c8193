import 'package:topbrokers/matchingslist/bloc/matchingslist_bloc.dart';
import 'package:topbrokers/offers_list/widgets/offer_list_item.dart';
import 'package:flutter/material.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmbeddedDemandMatchingsList extends StatefulWidget {
  final String demandId;

  EmbeddedDemandMatchingsList({required this.demandId}) : super();

  @override
  _EmbeddedDemandMatchingsList createState() => _EmbeddedDemandMatchingsList();
}

class _EmbeddedDemandMatchingsList extends State<EmbeddedDemandMatchingsList> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return BlocProvider<MatchingslistBloc>(
      create: (context) => MatchingslistBloc.createForDemand(context, widget.demandId)..add(MatchingslistOnFetch()),
      child: BlocConsumer<MatchingslistBloc, MatchingslistState>(
        listener: (context, state) {},
        builder: (context, state) {
          switch (state.status) {
            case MatchingslistStatus.failure:
              return Center(child: Text(localizations.matchings_errLoading));
            case MatchingslistStatus.success:
              return state.matchings.isEmpty
                  ? Center(
                      child: Text(
                        localizations.matchings_noMatchings,
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ConstrainedBox(
                      constraints: BoxConstraints(maxHeight: 320, minHeight: 20),
                      child: Scrollbar(
                        child: ListView.builder(
                          //key: AgentorKeys.actionsList,
                          //physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                          itemBuilder: (BuildContext context, int index) {
                            if (index >= state.matchings.length)
                              return Center(
                                child: TextButton.icon(
                                  icon: Icon(Icons.expand_more_rounded),
                                  label: Text(localizations.common_see_more),
                                  onPressed: () async {
                                    BlocProvider.of<MatchingslistBloc>(context).add(MatchingslistOnFetch());
                                  },
                                ),
                              );
                            else
                              return OfferListItem(
                                offer: state.matchings[index].offer.v,
                                // Mostrar agente si el agente de la oferta no es el mío
                                showAgent: state.myAgent?.id.vn != state.matchings[index].offer.v.agent.vn?.id.vn,
                                onTap: () {
                                  final offerId = state.matchings[index].offer.v.id.v;
                                  Navigator.pushNamed(
                                    context,
                                    AgentorRoutes.showOffer,
                                    arguments: offerId,
                                  );
                                },
                              );
                          },
                          itemCount: state.hasReachedMax ? state.matchings.length : state.matchings.length + 1,
                        ),
                      ),
                    );

            default:
              return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
