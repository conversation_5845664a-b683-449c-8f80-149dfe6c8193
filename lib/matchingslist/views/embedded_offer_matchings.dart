import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/matchingslist/bloc/matchingslist_bloc.dart';
import 'package:topbrokers/demands_list/widgets/demand_list_item.dart';
import 'package:flutter/material.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmbeddedOfferMatchingsList extends StatefulWidget {
  final String offerId;

  EmbeddedOfferMatchingsList({required this.offerId}) : super();

  @override
  _EmbeddedOfferMatchingsList createState() => _EmbeddedOfferMatchingsList();
}

class _EmbeddedOfferMatchingsList extends State<EmbeddedOfferMatchingsList> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return BlocProvider<MatchingslistBloc>(
      create: (context) => MatchingslistBloc.createForOffer(context, widget.offerId)..add(MatchingslistOnFetch()),
      child: BlocConsumer<MatchingslistBloc, MatchingslistState>(
        listener: (context, state) {},
        buildWhen: (a,b)=>true,
        builder: (context, state) {
          switch (state.status) {
            case MatchingslistStatus.failure:
              return Center(
                child: Text(localizations.matchings_noMatchings),
              );
            case MatchingslistStatus.success:
              return state.matchings.isEmpty
                  ? Center(
                      child: Text(
                        localizations.matchings_noMatchings,
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: 320,
                        minHeight: 20,
                      ),
                      child: Scrollbar(
                        child: ListView.builder(
                          //key: AgentorKeys.actionsList,
                          //physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                          itemBuilder: (BuildContext context, int index) => (index >= state.matchings.length)
                              ? Center(
                                  child: TextButton.icon(
                                    icon: Icon(Icons.expand_more_rounded),
                                    label: Text(localizations.common_see_more),
                                    onPressed: () async {
                                      BlocProvider.of<MatchingslistBloc>(context).add(MatchingslistOnFetch());
                                    },
                                  ),
                                )
                              : DemandListItem(
                                  demand: state.matchings[index].demand.v,
                                  onTap: () {
                                    final demandId = state.matchings[index].demand.v.id.v;
                                    Navigator.pushNamed(
                                      context,
                                      AgentorRoutes.showDemand,
                                      arguments: demandId,
                                    );
                                  },
                                ),
                          itemCount: state.hasReachedMax ? state.matchings.length : state.matchings.length + 1,
                        ),
                      ),
                    );

            default:
              return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
