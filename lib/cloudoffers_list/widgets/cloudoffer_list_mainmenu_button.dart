import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:flutter/material.dart';

import 'package:topbrokers/cloudoffers_export/offers_filter.dart';
import 'package:topbrokers/cloudoffers_list/cloudoffers.dart';
import 'package:topbrokers/cloudoffers_filter_dialog/widgets/widgets.dart';

enum _OptionCode { export, filter }

class CloudofferListMainmenuButton extends StatelessWidget {
  final bool visible;

  CloudofferListMainmenuButton({
    required this.visible,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CloudoffersListBloc, CloudoffersListState>(builder: (context, state) {
      final button = _Button(
        onSelected: (_OptionCode value) async {
          switch (value) {
            case _OptionCode.export:
              await exportCloudoffersDialog(context, filter: state.filter, orderBy: state.orderBy);
              break;
            case _OptionCode.filter:
              final newFilter = await changeCloudoffersFilterDialog(context, filter: state.filter);
              if (newFilter != null) {
                BlocProvider.of<CloudoffersListBloc>(context).add(CloudoffersListOnFilterChanged(newFilter));
              }
              break;
            default:
              break;
          }
        },
      );
      return AnimatedOpacity(
        opacity: visible ? 1.0 : 0.0,
        duration: Duration(milliseconds: 150),
        child: visible ? button : IgnorePointer(child: button),
      );
    });
  }
}

class _Button extends StatelessWidget {
  const _Button({
    Key? key,
    required this.onSelected,
  }) : super(key: key);

  final PopupMenuItemSelected<_OptionCode> onSelected;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<_OptionCode>(
      padding: const EdgeInsets.all(0.0),
      key: AgentorKeys.actionsFilterButton,
      icon: Icon(
        Icons.more_vert,
      ),
      tooltip: "Menú",
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<_OptionCode>(
          value: _OptionCode.filter,
          child: Row(children: [
            Padding(
              padding: EdgeInsets.fromLTRB(0, 5, 10, 5),
              child: Icon(Icons.search, color: Colors.grey.shade700),
            ),
            Text("Filtrar"),
          ]),
        ),
        PopupMenuItem<_OptionCode>(
          value: _OptionCode.export,
          child: Row(children: [
            Padding(
              padding: EdgeInsets.fromLTRB(0, 5, 10, 5),
              child: Icon(Icons.download, color: Colors.grey.shade700),
            ),
            Text("Exportar"),
          ]),
        ),
      ],
    );
  }
}
