import 'package:intl/intl.dart';
import 'package:topbrokers/offer_edit/widgets/offer_list_item_picture.dart';
import 'package:topbrokers/common/widgets/contact_link.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:flutter/material.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

const isDense = true;

class CloudofferListItem extends StatelessWidget {
  const CloudofferListItem(
      {Key? key,
      required this.offer,
      this.isSelected = false,
      this.onTap,
      this.showCustomer = true,
      this.showStatus = true,
      this.showMatchings = true})
      : super(key: key);

  final OfferDto offer;
  final bool isSelected;
  final void Function()? onTap;
  final bool showCustomer;
  final bool showStatus;
  final bool showMatchings;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customer = offer.customer.vn;
    List<Text> typePart = _buildPropertyType(context, theme, offer.property);
    List<Text> saleRentPart = _buildSaleRent(context, theme, offer);
    final cityPart = _buildCity(context, theme, offer.property.vn?.address.vn);
    List<Widget> attributesPart = _buildPropertyAttributes(context, theme, offer.property.vn?.attributes.vn);
    List<Text> saleRentAmountPart = _buildSaleRentAmount(context, theme, offer);

    return Card(
      //color: isSelected ? theme.primaryColorLight : theme.cardColor,
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 8),
        leading: OfferListItemAvatar(
          offer: offer,
          showMatchings: showMatchings,
          showStatus: showStatus,
        ),
        title: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(children: _addSeparationWhiteSpace(typePart, saleRentPart)),
                  Row(children: saleRentAmountPart),
                ],
              ),
            ),
            if (cityPart != null)
              Expanded(
                child: cityPart,
              ),
          ],
        ),
        isThreeLine: false,
        subtitle: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: attributesPart,
                  ),
                ],
              ),
            ),
            if (showCustomer && customer != null && customer.id.vn != null)
              Expanded(
                child: ContactLink(
                  contact: customer,
                  isDense: true,
                ),
              ),
          ],
        ),
        dense: true,
        selected: isSelected,
        onTap: onTap,
      ),
    );
  }

  static List<Text> _buildSaleRent(BuildContext context, ThemeData theme, OfferDto offer) {
    final textTheme = theme.textTheme;

    final bool isSale = offer.sale.vn?.allowed.vn ?? false;
    final bool isRent = offer.rent.vn?.allowed.vn ?? false;

    final String txt = (isSale && isRent)
        ? '${context.apploc.common_sale}/${context.apploc.common_rent}'
        : (isSale)
            ? context.apploc.common_sale
            : context.apploc.common_rent;

    return <Text>[Text(txt, style: textTheme.bodyText1)];
  }

  static List<Text> _buildSaleRentAmount(BuildContext context, ThemeData theme, OfferDto offer) {
    final txt = (() {
      final formatter = NumberFormat.simpleCurrency(locale: Intl.getCurrentLocale(), name: "EUR", decimalDigits: 0);
      final saleAmount = offer.sale.vn?.amount.vn;
      final rentAmount = offer.rent.vn?.amount.vn;
      if (saleAmount != null && rentAmount != null) {
        return '${formatter.format(saleAmount)}/${formatter.format(rentAmount)}';
      } else if (saleAmount != null) {
        return '${formatter.format(saleAmount)}';
      } else if (rentAmount != null) {
        return '${formatter.format(rentAmount)}';
      } else {
        return null;
      }
    })();

    return txt != null ? [Text(txt)] : [];
  }

  static List<Text> _buildPropertyType(BuildContext context, ThemeData theme, Optional<PropertyDto> property) {
    final textTheme = theme.textTheme;
    final typeName = property.vn?.type.vn?.label.vn?.localized ?? "";
    return [Text('$typeName', style: textTheme.bodyText1)];
  }

  static List<Widget> _buildPropertyAttributes(
      BuildContext context, ThemeData theme, PropertyAttributesDto? attributes) {
    final apploc = context.apploc;
    final totalSurfaceM2 = attributes?.totalSurfaceM2.vn;
    final totalBedroomsCount = attributes?.totalBedroomsCount.vn;
    final isExterior = attributes?.facadeCodes.vn?.contains("exterior");

    final vdivider = VerticalDivider(color: Colors.black54, indent: 0, endIndent: 0, width: 6, thickness: 1);

    return [
      if (totalSurfaceM2 != null) Text(apploc.offers_item_surfaceM2(totalSurfaceM2.toInt())),
      if (totalBedroomsCount != null && totalBedroomsCount > 0)
        Text(apploc.offers_item_bethroomsCount(totalBedroomsCount)),
      if (isExterior != null && isExterior) Text(apploc.offers_item_exteriorTxt),
    ].fold(<Widget>[], (actual, widget) {
      if (actual.length != 0)
        return actual..addAll([vdivider, widget]);
      else
        return actual..add(widget);
    });
  }

  static Widget? _buildCity(BuildContext context, ThemeData theme, PropertyAddressDto? address) {
    if (address == null) {
      return null;
    } else {
      final textTheme = theme.textTheme;
      final cityLabel = address.city.vn?.label.vn?.localized ?? "";
      //final provinceLabel = city?.vn?.province?.vn?.label?.vn?.localized ?? "";
      return Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
        Text(cityLabel, style: textTheme.bodyText1, overflow: TextOverflow.ellipsis),
        Text(
          address.composedStreetLine.vn ?? "", // provinceLabel,
          style: textTheme.bodyText2,
          overflow: TextOverflow.ellipsis,
        )
      ]);
    }
  }

  static List<Text> _addSeparationWhiteSpace(List<Text> a, List<Text> b) {
    bool aIsWhite = a.length == 0 || a[0].data == null || a[0].data!.length == 0 || a[0].data == " ";
    bool bIsWhite = b.length == 0 || b[0].data == null || b[0].data!.length == 0 || b[0].data == " ";

    if (!aIsWhite && !bIsWhite)
      return a + [Text(" ")] + b;
    else
      return a + b;
  }
}
