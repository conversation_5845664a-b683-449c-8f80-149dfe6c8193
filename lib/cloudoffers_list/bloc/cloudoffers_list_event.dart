part of 'cloudoffers_list_bloc.dart';

int _id = 0;

enum CloudoffersListExportCode {
  phonenumbers,
  basicinfo
}
abstract class CloudoffersListEvent extends Equatable {
  const CloudoffersListEvent();

  @override
  List<Object> get props => [];
}

class CloudoffersListOnOfferChanged extends CloudoffersListEvent {
  final String offerId;

  const CloudoffersListOnOfferChanged({required this.offerId});
  @override
  List<Object> get props => [offerId];
}

class CloudoffersListOnRefresh extends CloudoffersListEvent {}


class CloudoffersListOnFetch extends CloudoffersListEvent {
  final _myid = ++_id;

  @override
  List<Object> get props => [_myid];
  @override
  String toString() => "${super.toString()}$_myid";
}

class CloudoffersListOnFilterChanged extends CloudoffersListEvent {
  final CloudoffersListFilter filter;
  const CloudoffersListOnFilterChanged(this.filter);
  @override
  List<Object> get props => [filter];

  @override
  String toString() => "CloudoffersListOnFilterChanged { new filter: $filter }";
}

class CloudoffersListOnOrderByChanged extends CloudoffersListEvent {
  final CloudoffersListOrderBy orderBy;
  const CloudoffersListOnOrderByChanged(this.orderBy);
  @override
  List<Object> get props => [orderBy];

  @override
  String toString() => "CloudoffersListOnOrderByChanged { new orderBy: $orderBy }";
}

class CloudoffersListOnExport extends CloudoffersListEvent {
  final CloudoffersListExportCode exportCode;
  const CloudoffersListOnExport(this.exportCode);
  @override
  List<Object> get props => [exportCode];

  @override
  String toString() => "CloudoffersListOnExport { exportCode: $exportCode }";
}

class CloudoffersListOnItemSelected extends CloudoffersListEvent {
  final OfferDto offer;
  const CloudoffersListOnItemSelected(this.offer);

  @override
  List<Object> get props => [offer];

  @override
  String toString() => "OfferSelected { offer: $offer }";
}
