import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';

part 'cloudoffers_list_event.dart';
part 'cloudoffers_list_state.dart';

class CloudoffersListBloc extends Bloc<CloudoffersListEvent, CloudoffersListState> {
  static const _PAGE_SIZE = 30;

  final AppModelsNS appModels;

  /// Cuando deseamos la oportunidades de un agente
  CloudoffersListFilter filter;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;

  CloudoffersListBloc({
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
    required this.filter,
  }) : super(CloudoffersListState.create(filter: filter)) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelCreatedState<OfferDto> || state is ModelsChannelUpdatedState<OfferDto>) {
        this.add(CloudoffersListOnRefresh());
      } else if (state is ModelsChannelPropertymediaCreatedState) {
        this.add(CloudoffersListOnOfferChanged(offerId: state.offerId));
      } else if (state is ModelsChannelPropertymediaUpdatedState) {
        this.add(CloudoffersListOnOfferChanged(offerId: state.offerId));
      } else if (state is ModelsChannelPropertymediaDeletedState) {
        this.add(CloudoffersListOnOfferChanged(offerId: state.offerId));
      }
    });
  }

  factory CloudoffersListBloc.create(
    BuildContext context, {
    CloudoffersListFilter filter = const CloudoffersListFilter(),
  }) {
    return CloudoffersListBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      filter: filter,
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    await _modelsChannelSubscription.cancel();
    return super.close();
  }

  @override
  Stream<Transition<CloudoffersListEvent, CloudoffersListState>> transformEvents(
    Stream<CloudoffersListEvent> events,
    TransitionFunction<CloudoffersListEvent, CloudoffersListState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<CloudoffersListState> mapEventToState(CloudoffersListEvent event) async* {
    if (event is CloudoffersListOnFetch) {
      yield* _mapOnFetch(state, event);
    } else if (event is CloudoffersListOnRefresh) {
      yield* _mapOnRefresh(state, event);
    } else if (event is CloudoffersListOnOfferChanged) {
      yield* _mapOnOfferChanged(state, event);
    } else if (event is CloudoffersListOnItemSelected) {
      yield* _mapOnItemSelected(state, event);
    } else if (event is CloudoffersListOnFilterChanged) {
      yield* _mapOnFilterChanged(state, event);
    } else if (event is CloudoffersListOnOrderByChanged) {
      yield* _mapOnOrderByChanged(state, event);
    }
  }

  Stream<CloudoffersListState> _mapOnFilterChanged(
      CloudoffersListState state, CloudoffersListOnFilterChanged event) async* {
    try {
      final newFilter = event.filter;
      yield state.copyWith(status: CloudoffersListStatus.initial);
      final offers = await _listOffers(filter: newFilter, orderBy: state.orderBy);
      yield state.copyWith(
        status: CloudoffersListStatus.success,
        filter: newFilter,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: CloudoffersListStatus.failure);
    }
  }

  Stream<CloudoffersListState> _mapOnOrderByChanged(
      CloudoffersListState state, CloudoffersListOnOrderByChanged event) async* {
    try {
      final newOrderBy = event.orderBy;
      yield state.copyWith(status: CloudoffersListStatus.initial);
      final offers = await _listOffers(filter: state.filter, orderBy: newOrderBy);
      yield state.copyWith(
        status: CloudoffersListStatus.success,
        orderBy: newOrderBy,
        offers: offers,
        hasReachedMax: offers.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: CloudoffersListStatus.failure);
    }
  }

  Stream<CloudoffersListState> _mapOnItemSelected(
      CloudoffersListState state, CloudoffersListOnItemSelected event) async* {
    try {
      // No tenemos en cuenta los datos de la nueva oferta:  recargamos la lista
      yield state.copyWith(selectedOfferId: event.offer.id.vn ?? "");
    } on Exception {
      yield state.copyWith(status: CloudoffersListStatus.failure);
    }
  }

  Stream<CloudoffersListState> _mapOnFetch(CloudoffersListState state, CloudoffersListOnFetch event) async* {
    if (state.hasReachedMax) yield state;
    try {
      if (state.status == CloudoffersListStatus.initial) {
        final offers = await _listOffers(filter: state.filter, orderBy: state.orderBy);
        yield state.copyWith(
            status: CloudoffersListStatus.success, offers: offers, hasReachedMax: offers.length != _PAGE_SIZE);
      } else {
        // Este punto sirve tanto en estado success como failure si se reintentase
        final offers = await _listOffers(filter: state.filter, orderBy: state.orderBy, offset: state.offers.length);
        yield offers.isEmpty
            ? state.copyWith(hasReachedMax: true)
            : state.copyWith(
                status: CloudoffersListStatus.success,
                offers: List.of(state.offers)..addAll(offers),
                hasReachedMax: false,
              );
      }
    } on Exception {
      yield state.copyWith(status: CloudoffersListStatus.failure);
    }
  }

  Stream<CloudoffersListState> _mapOnRefresh(CloudoffersListState state, CloudoffersListOnRefresh event) async* {
    try {
      yield state.copyWith(status: CloudoffersListStatus.initial);
      final offers = await _listOffers(filter: state.filter, orderBy: state.orderBy);
      yield state.copyWith(
          status: CloudoffersListStatus.success, offers: offers, hasReachedMax: offers.length != _PAGE_SIZE);
    } on Exception {
      yield state.copyWith(status: CloudoffersListStatus.failure);
    }
  }

  ///
  /// Parcheamos la oportunidad asociada a la oferta sin necesidad de tener que recargar la lista entera
  /// Debido a que el orden de la lista puede cambiar, de momento no lo usamos (preferimos refrescar lista entera)
  Stream<CloudoffersListState> _mapOnOfferChanged(
      CloudoffersListState state, CloudoffersListOnOfferChanged event) async* {
    try {
      final offerId = Some(event.offerId);
      final offerIndex = state.offers.indexWhere((o) => o.id == offerId);
      if (offerIndex != -1) {
        final offer = await appModels.readOffer(offerId.v);
        if (offer != null) {
          yield state.copyWith(
            offers: state.offers.map((o) => o.id == offerId ? offer : o).toList(),
          );
        }
      }
    } on Exception {
      yield state.copyWith(status: CloudoffersListStatus.failure);
    }
  }

  Future<List<OfferDto>> _listOffers({
    required CloudoffersListFilter filter,
    required CloudoffersListOrderBy orderBy,
    int offset = 0,
  }) {
    return appModels.listCloudOffers(filter: filter, offset: offset, orderBy: orderBy, limit: _PAGE_SIZE);
  }
}
