part of 'cloudoffers_list_bloc.dart';

enum CloudoffersListStatus { initial, success, failure }

int _idGenerator = 0;

class CloudoffersListState extends Equatable {
  const CloudoffersListState({
    this.status = CloudoffersListStatus.initial,
    this.offers = const <OfferDto>[],
    this.hasReachedMax = false,
    required this.filter,
    this.orderBy = CloudoffersListOrderBy.amount_desc,
    this.selectedOfferId,
    required this.id,
  });

  // Actual status of the screen
  final CloudoffersListStatus status;
  // Wich filter are we applying to the oportunities of kind "oofers"

  final CloudoffersListFilter filter;
  final CloudoffersListOrderBy orderBy;
  // The list of actual fetched offers
  final List<OfferDto> offers;
  // There is no more offers to be fetched
  final bool hasReachedMax;
  // Identifier of the selected Offer
  final String? selectedOfferId;

  final int id;

  factory CloudoffersListState.create({
    required CloudoffersListFilter filter,
    CloudoffersListOrderBy orderBy = CloudoffersListOrderBy.created_at_desc,
  }) {
    return CloudoffersListState(filter: filter, orderBy: orderBy, id: ++_idGenerator);
  }
  CloudoffersListState copyWith({
    CloudoffersListStatus? status,
    CloudoffersListFilter? filter,
    CloudoffersListOrderBy? orderBy,
    List<OfferDto>? offers,
    bool? hasReachedMax,
    String? selectedOfferId,
  }) {
    return CloudoffersListState(
      status: status ?? this.status,
      filter: filter ?? this.filter,
      orderBy: orderBy ?? this.orderBy,
      offers: offers ?? this.offers,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      selectedOfferId: selectedOfferId ?? this.selectedOfferId,
      id: ++_idGenerator,
    );
  }

  @override
  List<Object> get props => [
        id,
        status,
        offers.length,
        offers.length != 0 ? offers[offers.length - 1].id.vn ?? "" : "",
        offers,
        hasReachedMax,
        filter,
        orderBy,
        selectedOfferId ?? "",
      ];
  @override
  String toString() => "${super.toString()}$id";
}
