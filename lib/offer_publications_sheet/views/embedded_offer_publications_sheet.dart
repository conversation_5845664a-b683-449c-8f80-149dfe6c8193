import 'package:simple_html_css/simple_html_css.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/offer_publications_sheet/bloc/offerpublications_sheet_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmbeddedOfferpublicationsList extends StatefulWidget {
  final Optional<String> offerId;
  final bool? memberCanRead;

  EmbeddedOfferpublicationsList({this.offerId = const None(), this.memberCanRead}) : super();

  @override
  _EmbeddedActionsListState createState() => _EmbeddedActionsListState();
}

class _EmbeddedActionsListState extends State<EmbeddedOfferpublicationsList> {
  final _scrollController = ScrollController();
  late OfferPublicationsSheetBloc _widgetBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return _inBlockProvider(
      context,
      child: BlocConsumer<OfferPublicationsSheetBloc, OfferPublicationsSheetState>(
        listener: (context, state) {
          if (state.lastError is! None) {
            context.showError(state.lastError.v);
          }
        },
        builder: (context, state) {
          if (state.status == OfferPublicationsSheetStatus.failure)
            return ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: 180,
                minHeight: 20,
              ),
              child: Center(
                child: Text(localizations.actions_errLoading),
              ),
            );
          else if (state.status == OfferPublicationsSheetStatus.success) {
            final wgs = state.agentAffiliations
                .where((aaff) => aaff.workgroup.vn != null)
                .where((aaff) => widget.memberCanRead == null || widget.memberCanRead == aaff.can.vn?.read.vn)
                .map((aaff) => aaff.workgroup.v);
            return Column(
              children: wgs.map((wg) {
                final paymentinfo = wg.publicationEcomProduct.vn?.firstPayment.vn;
                final hasCost =
                    (paymentinfo?.amount.vn != null) || (wg.publicationEcomProduct.vn?.dailyamount.vn != null);

                final costs = [
                  <String>[
                    if (paymentinfo?.amount.vn != null) "${paymentinfo!.amount.v}€",
                    if ((paymentinfo?.days.vn ?? 1) > 1) "primeros ${paymentinfo!.days.v} días",
                  ].join(" "),
                  if (wg.publicationEcomProduct.vn?.dailyamount.vn != null)
                    "${wg.publicationEcomProduct.v?.dailyamount.v}€/dia",
                ].join(" + ");

                final found = state.workgroupIdsWhereIsPublished.contains(wg.id.v);
                return SwitchListTile(
                  title: Text(wg.name.vn ?? ""),
                  subtitle: costs != "" ? Text(costs) : null,
                  value: found,
                  dense: true,
                  onChanged: (bool value) {
                    final workgrouId = wg.id.v;

                    if (!hasCost) {
                      if (value)
                        _widgetBloc.add(OfferPublicationsSheetOnPublishEvent(workgroupId: workgrouId));
                      else
                        _widgetBloc.add(OfferPublicationsSheetOnUnpublishEvent(workgroupId: workgrouId));
                    } else {
                      if (value)
                        _acceptPublishing(wg, () {
                          _widgetBloc.add(OfferPublicationsSheetOnPublishEvent(workgroupId: workgrouId));
                        }, () {});
                      else
                        _acceptUnpublishing(wg, () {
                          _widgetBloc.add(OfferPublicationsSheetOnUnpublishEvent(workgroupId: workgrouId));
                        }, () {});
                    }
                  },
                );
              }).toList(growable: false),
            );
          } else
            return const Center(
              child: CircularProgressIndicator(),
            );
        },
      ),
    );
  }

  ///
  /// Investiga si ya existe OfferPublicationsSheetBloc en el contexto y lo usa.
  /// Si no existe, crea un bloque propio
  ///
  Widget _inBlockProvider(BuildContext context, {required Widget child}) {
    var bloc = _blocOrNull<OfferPublicationsSheetBloc>(context);
    if (bloc == null)
      return BlocProvider<OfferPublicationsSheetBloc>(
        create: (context) {
          _widgetBloc = OfferPublicationsSheetBloc.create(
            context,
            offerId: widget.offerId,
          );
          return _widgetBloc;
        },
        child: child,
      );
    else {
      _widgetBloc = bloc;
      return child;
    }
  }

  T? _blocOrNull<T extends BlocBase>(BuildContext context) {
    try {
      return BlocProvider.of<T>(context);
    } catch (_) {
      return null;
    }
  }

  void _acceptPublishing(WorkgroupDto wg, void Function() fYes, void Function() fNo) {
    final initialAmount = wg.publicationEcomProduct.vn?.firstPayment.vn?.amount.vn ?? 0;
    final initialDays = wg.publicationEcomProduct.vn?.firstPayment.vn?.days.vn ?? 1;
    final dailyAmount = wg.publicationEcomProduct.vn?.dailyamount.vn ?? 0;

    final initialPeriod = initialDays == 1 ? 'las primeras 24h' : '$initialDays días';
    final bodyHtml = """
<p>- Se aplicará un <b>cobro inicial</b> de <b>$initialAmount€</b></p>
<p>- Tras $initialPeriod, se aplicará un <b>cobro diario</b> de <b>$dailyAmount€</b>. Si carece de fondos suficientes, se dejará de publicar de forma automática.</p>
<h3>¿Desea continuar?</h3>
""";

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Publicación en ${wg.name.vn ?? ""}'),
          content: HTML.toRichText(context, bodyHtml),
          buttonPadding: EdgeInsets.fromLTRB(20, 0, 20, 0),
          actions: <Widget>[
            ElevatedButton(
              child: Text("Sí, continuar"),
              onPressed: () {
                fYes();
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: Text("No, cancelar"),
              onPressed: () {
                fNo();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _acceptUnpublishing(WorkgroupDto wg, void Function() fYes, void Function() fNo) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Dejar de publicar en ${wg.name.vn ?? ""}'),
          content: HTML.toRichText(context, """
<p>Esta operación deja de publicar el anuncio en el portal, <br/>
pero <b>no causa ningún tipo de devolución monetaria</b></p>
<h4>¿Desea continuar?</h4>
          """),
          buttonPadding: EdgeInsets.fromLTRB(20, 0, 20, 0),
          actions: <Widget>[
            ElevatedButton(
              child: Text("Sí, continuar"),
              onPressed: () {
                fYes();
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: Text("No, cancelar"),
              onPressed: () {
                fNo();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    //assert(_widgetBloc != null, "unitialized _obbersBlock when managing _onScroll");
    //if (_isBottom) _actionsBloc.add(OfferPublicationsSheetOnFetchEvent(offerId: ));
  }

  //bool get _isBottom {
  //  if (!_scrollController.hasClients) return false;
  //  final maxScroll = _scrollController.position.maxScrollExtent;
  //  final currentScroll = _scrollController.offset;
  //  return currentScroll >= (maxScroll * 0.9);
  //}
}
