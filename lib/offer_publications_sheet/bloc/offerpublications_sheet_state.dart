part of 'offerpublications_sheet_bloc.dart';

enum OfferPublicationsSheetStatus { initial, success, failure }
int _counter = 0;

class OfferPublicationsSheetState extends Equatable {
  final _instanceId = ++_counter;
  OfferPublicationsSheetState({
    this.status = OfferPublicationsSheetStatus.initial,
    this.offerId = const None(),
    this.agentAffiliations = const [],
    this.workgroupIdsWhereIsPublished = const {},
    this.lastError = const None(),
  });

  // Actual status of the screen
  final OfferPublicationsSheetStatus status;

  /// A qué oferta se refieren las publicaciones
  final Optional<String> offerId;

  /// Afiliaciones:   en qué grupos y con qué permisos está el agente
  final List<WorkgroupMemberDto> agentAffiliations;

  /// Publicaciones:  En cuales de los grupos en los que el agente está afiliado están publicadas las ofertas
  final Set<String> workgroupIdsWhereIsPublished;

  final Optional<String> lastError;

  OfferPublicationsSheetState copyWith({
    OfferPublicationsSheetStatus? status,
    Optional<String>? offerId,
    List<WorkgroupMemberDto>? agentAffiliations,
    Set<String>? workgroupIdsWhereIsPublished,
    Optional<String>? lastError,
  }) {
    return OfferPublicationsSheetState(
      status: status ?? this.status,
      offerId: offerId ?? this.offerId,
      agentAffiliations: agentAffiliations ?? this.agentAffiliations,
      workgroupIdsWhereIsPublished: workgroupIdsWhereIsPublished ?? this.workgroupIdsWhereIsPublished,
      lastError: lastError ?? this.lastError,
    );
  }

  @override
  List<Object> get props => [_instanceId];
  //List<Object> get props => [status, offerId, workgroupIdsWhereIsPublished?.length ?? 0, lastError];
}
