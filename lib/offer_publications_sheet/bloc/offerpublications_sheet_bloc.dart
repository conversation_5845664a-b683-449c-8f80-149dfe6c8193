import 'dart:async';
import 'dart:collection';

import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';


part 'offerpublications_sheet_event.dart';
part 'offerpublications_sheet_state.dart';

class OfferPublicationsSheetBloc extends Bloc<OfferPublicationsSheetEvent, OfferPublicationsSheetState> {
  final AppModelsNS appModels;

  late StreamSubscription<ModelsChannelState> _offersChannelSubscription;

  OfferPublicationsSheetBloc({
    required this.appModels,
    required ModelsChannelBloc modelsChannel,
  }) : super(OfferPublicationsSheetState()) {
    this._offersChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelCreatedState<WorkgroupOfferDto> && this.state.offerId is Some) {
        if (state.entities.map((o) => o.offer.vn?.id.vn).contains(this.state.offerId.v)) {
          this.add(OfferPublicationsSheetOnFetchEvent(offerId: this.state.offerId.v));
        }
      }
    });
  }

  ///
  /// Crea un nuevo bloque de control de hoja de publicaciones de oferta.
  /// Si se indica un identificador de oferta, se añade el primer evento de carga de datos asociados a dicha oferta.
  ///
  factory OfferPublicationsSheetBloc.create(BuildContext context, {Optional<String> offerId = const None()}) {
    final block = OfferPublicationsSheetBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
    );
    if (offerId is Some)
      return block..add(OfferPublicationsSheetOnFetchEvent(offerId: offerId.v));
    else
      return block;
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _offersChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<OfferPublicationsSheetEvent, OfferPublicationsSheetState>> transformEvents(
    Stream<OfferPublicationsSheetEvent> events,
    TransitionFunction<OfferPublicationsSheetEvent, OfferPublicationsSheetState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<OfferPublicationsSheetState> mapEventToState(OfferPublicationsSheetEvent event) async* {
    if (event is OfferPublicationsSheetOnFetchEvent) {
      yield* _mapOnFetchToState(state, event);
    } else if (event is OfferPublicationsSheetOnPublishEvent) {
      yield* _mapOnPublishEvent(state, event);
    } else if (event is OfferPublicationsSheetOnUnpublishEvent) {
      yield* _mapOnUnPublishEvent(state, event);
    }
  }

  Stream<OfferPublicationsSheetState> _mapOnFetchToState(
      OfferPublicationsSheetState state, OfferPublicationsSheetOnFetchEvent event) async* {
    try {
      final affiliations = await appModels.listAgentAffiliations(
        filter: AgentAffiliationsListFilter(
          includeCanPublish: True,
        ),
      );
      final publications = await appModels.listOfferPublications(event.offerId);

      yield state.copyWith(
        status: OfferPublicationsSheetStatus.success,
        offerId: Some(event.offerId),
        agentAffiliations: affiliations,
        workgroupIdsWhereIsPublished: LinkedHashSet.of(publications.map((pub) => pub.id.v)),
      );
    } on Exception catch (e) {
      print("Exception $e");
      yield state.copyWith(status: OfferPublicationsSheetStatus.failure);
    }
  }

  Stream<OfferPublicationsSheetState> _mapOnPublishEvent(
    OfferPublicationsSheetState state,
    OfferPublicationsSheetOnPublishEvent event,
  ) async* {
    try {
      final workgroup = await appModels.publishOffer(state.offerId.v, event.workgroupId);
      yield state.copyWith(
        workgroupIdsWhereIsPublished: state.workgroupIdsWhereIsPublished.union(LinkedHashSet.of([workgroup.id.v])),
        lastError: const None(),
      );
    } on ApiInsufficientFunds {
      //alertsBloc.add(AlertsProcessorOnPushEvent(message: AlertMessage(title: "", text: "No hay fondos suficients para realizar esta operación")));
      yield state.copyWith(lastError: Some("No hay fondos suficients para realizar esta operación"));
    } on Exception {
      yield state.copyWith(lastError: Some("Problemas publicando la oferta"));
    }
  }

  Stream<OfferPublicationsSheetState> _mapOnUnPublishEvent(
    OfferPublicationsSheetState state,
    OfferPublicationsSheetOnUnpublishEvent event,
  ) async* {
    try {
      final count = await appModels.unpublishOffer(state.offerId.v, event.workgroupId);
      if (count > 0) {
        yield state.copyWith(
          workgroupIdsWhereIsPublished:
              state.workgroupIdsWhereIsPublished.difference(LinkedHashSet.of([event.workgroupId])),
          lastError: const None(),
        );
      }
    } on Exception {
      yield state.copyWith(lastError: Some("Problemas publicando la oferta"));
    }
  }
}
