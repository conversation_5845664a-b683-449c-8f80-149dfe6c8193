part of 'offerpublications_sheet_bloc.dart';

abstract class OfferPublicationsSheetEvent extends Equatable {
  const OfferPublicationsSheetEvent();

  @override
  List<Object> get props => [];
}

class OfferPublicationsSheetOnFetchEvent extends OfferPublicationsSheetEvent {
  final String offerId;
  OfferPublicationsSheetOnFetchEvent({
    required this.offerId,
  });
}

///
/// Se ha recargado corréctamente la información
///
class OfferPublicationsSheetOnFetchedEvent extends OfferPublicationsSheetEvent {
  final String offerId;

  final List<WorkgroupMemberDto> affiliations;
  final List<WorkgroupOfferDto> publications;
  OfferPublicationsSheetOnFetchedEvent({
    required this.offerId,
    required this.affiliations,
    required this.publications,
  });
}

///
/// Se desea publicar oferta en un workgroup
///
class OfferPublicationsSheetOnPublishEvent extends OfferPublicationsSheetEvent {
  // Conqué afiliación se efectúa la publicación (en qué grupo)
  final String workgroupId;
  OfferPublicationsSheetOnPublishEvent({required this.workgroupId});
}

///
///  Se desea despublicar una oferta de un grupo
///
class OfferPublicationsSheetOnUnpublishEvent extends OfferPublicationsSheetEvent {
  final String workgroupId;
  OfferPublicationsSheetOnUnpublishEvent({required this.workgroupId});
}

class OfferPublicationsSheetOnRefreshEvent extends OfferPublicationsSheetEvent {}
