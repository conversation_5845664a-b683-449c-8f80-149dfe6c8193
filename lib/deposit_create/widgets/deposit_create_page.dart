import 'package:topbrokers/common/helpers.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:form_builder/form_builder.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/deposit_create/bloc/deposit_create_bloc.dart';
import 'package:topbrokers/deposit_create/models/deposit_amount_model.dart';
import 'package:topbrokers/deposit_create/models/deposit_model.dart';

typedef OnSaveContactCallback = Function(ContactDto contact);

class DepositCreatePage extends StatefulWidget {
  DepositCreatePage({Key? key}) : super(key: key ?? AgentorKeys.createDepositPage);

  @override
  _DepositCreatePageState createState() => _DepositCreatePageState();
}

class _DepositCreatePageState extends State<DepositCreatePage> {
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final localizations = context.getAppLocalizationsOrThrow();
    return BlocProvider(
      create: (context) => DepositCreateBloc.create(context)..add(DepositCreateOnNewEvent()),
      child: BlocConsumer<DepositCreateBloc, DepositCreateState>(
        listener: (context, state) {},
        builder: (context, state) {
          if (state is DepositCreateLoaded) {
            return Scaffold(
              appBar: AppBar(
                title: Text("Comprar crédito"),
                centerTitle: true,
              ),
              body: WidthLimiter(
                child: BlocConsumer<DepositCreateBloc, DepositCreateState>(
                  listener: (context, state) {
                    if (state is DepositCreatePurchasingError) {
                      context.showError(state.error);
                    }
                  },
                  builder: (context, state) {
                    final loadedState = state as DepositCreateLoaded;
                    return _buildForm(context, loadedState.deposit, loadedState.amounts);
                  },
                ),
              ),
            );
          } else if (state is DepositCreateLoadFailure) {
            return Center(
              child: Text(state.error),
            );
          } else if (state is DepositCreatePurchasingState) {
            return Center(
              child: Row(
                children: [const Text("Conecting with stripe"), CircularProgressIndicator()],
              ),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, DepositModel deposit, List<DepositAmountModel> amounts) {
    return Center(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(32),
        child: Column(children: [
          _buildEntries(context, deposit, amounts).buildForm(context: context, key: _formKey),
          SizedBox.fromSize(size: Size.fromHeight(64)),
          ElevatedButton.icon(
            icon: Icon(Icons.payment),
            onPressed: () {
              BlocProvider.of<DepositCreateBloc>(context).add(DepositCreateOnDoDeposit(deposit: deposit));
            },
            label: Text("Pagar con Stripe"),
            style: ElevatedButton.styleFrom(
              elevation: 2,
              minimumSize: Size(350, 50),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
            ),
          ),
        ]),
      ),
    );
  }

  List<FormEntry> _buildEntries(BuildContext context, DepositModel deposit, List<DepositAmountModel> amounts) {
    return <FormEntry>[
      SelectFieldEntry<String>(
        label: "¿Cuánto dinero deseas añadir a tu cuenta?",
        getValue: () => deposit.amount.id,
        setValue: (String? v) {
          setState(() {
            deposit.amount = amounts.firstWhere((a) => a.id == v, orElse: () => amounts[0]);
          });
        },
        isRequired: true,
        options: amounts.map((amount) => SelectOption<String>(value: amount.id, label: amount.label)).toList(),
      ),
    ];
  }
}
