import 'dart:html';

import 'package:agentor_deps/agentor_deps.dart';

import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/deposit_create/models/deposit_amount_model.dart';
import 'package:topbrokers/deposit_create/models/deposit_model.dart';
import 'package:topbrokers/stripe_checkout/stripe_js.dart';

part 'deposit_create_event.dart';
part 'deposit_create_state.dart';

class DepositCreateBloc extends Bloc<DepositCreateEvent, DepositCreateState> {
  final AppModelsNS appModels;

  DepositCreateBloc({required this.appModels}) : super(DepositCreateState());

  factory DepositCreateBloc.create(BuildContext context) {
    return DepositCreateBloc(appModels: Provider.of<AppModelsNS>(context, listen: false));
  }

  @override
  Stream<Transition<DepositCreateEvent, DepositCreateState>> transformEvents(
    Stream<DepositCreateEvent> events,
    TransitionFunction<DepositCreateEvent, DepositCreateState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<DepositCreateState> mapEventToState(DepositCreateEvent event) async* {
    final state = this.state;
    if (event is DepositCreateOnNewEvent) {
      yield* _mapOnNewEventToState(state, event);
    } else if (state is DepositCreateLoaded) {
      if (event is DepositCreateOnDoDeposit) {
        yield* _mapOnDoDepositToState(state, event);
      }
    }
  }

  Stream<DepositCreateState> _mapOnNewEventToState(DepositCreateState state, DepositCreateOnNewEvent event) async* {
    try {
      final amounts = <DepositAmountModel>[
        DepositAmountModel(id: "eur_10", label: "10€"),
        DepositAmountModel(id: "eur_20", label: "20€"),
        DepositAmountModel(id: "eur_30", label: "30€"),
        DepositAmountModel(id: "eur_40", label: "40€"),
        DepositAmountModel(id: "eur_50", label: "50€"),
        DepositAmountModel(id: "eur_60", label: "60€"),
        DepositAmountModel(id: "eur_70", label: "70€"),
        DepositAmountModel(id: "eur_80", label: "80€"),
        DepositAmountModel(id: "eur_90", label: "90€"),
      ];
      yield DepositCreateLoaded(amounts: amounts, deposit: DepositModel(amount: amounts[0]));
    } on Exception {
      yield DepositCreateLoadFailure(error: "Problemas obteniendo datos");
    }
  }

  Stream<DepositCreateState> _mapOnDoDepositToState(DepositCreateLoaded state, DepositCreateOnDoDeposit event) async* {
    try {
      await _purchaseCredit(event.deposit);
    } on Exception {
      yield DepositCreatePurchasingError.fromLoadedState(state, error: "Problems initializing stripe operation");
    }
  }

  Future<void> _purchaseCredit(DepositModel deposit) async {
    Location currentLocation = window.location;
    final baseUrl = currentLocation.href.split("#")[0].split("?")[0];
    //print(currentLocation.href); // 'http://www.example.com:80/'
    //print(baseUrl);
    final stripeConf = await Deps.solve<ApiServices>().getStripeConf();
    //print(stripeConf);
    final checkoutSession = await Deps.solve<ApiServices>()
        .postStripeCheckoutsession(deposit.amount.id, '$baseUrl#/successcheckout', '$baseUrl#/cancelcheckout');

    Stripe(stripeConf.publicKey).redirectToCheckout(CheckoutOptions(sessionId: checkoutSession.id.v));
  }
}
