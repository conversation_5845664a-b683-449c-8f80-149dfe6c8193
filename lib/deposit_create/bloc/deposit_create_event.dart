part of 'deposit_create_bloc.dart';

abstract class DepositCreateEvent extends Equatable {
  const DepositCreateEvent();

  @override
  List<Object> get props => [];
}

class DepositCreateOnNewEvent extends DepositCreateEvent {}

///
/// The deposit needs to be executed.
/// It will cause application to navigate to Stripe
/// 
///
class DepositCreateOnDoDeposit extends DepositCreateEvent {
  final DepositModel deposit;
  const DepositCreateOnDoDeposit({required this.deposit}) : super();
}
