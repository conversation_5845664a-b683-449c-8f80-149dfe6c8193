part of 'deposit_create_bloc.dart';

class DepositCreateState extends Equatable {
  @override
  List<Object> get props => [];
}

class DepositCreateLoading extends DepositCreateState {}

///
/// Los datos para la creación del depósito han sido cargados (y pueden modificarse en el formulario)
///
class DepositCreateLoaded extends DepositCreateState {
  final List<DepositAmountModel> amounts;
  final DepositModel deposit;

  DepositCreateLoaded({
    required this.amounts,
    required this.deposit,
  }) : super();

  @override
  List<Object> get props => [deposit, amounts];
}

///
/// Problemas durante la carga inicial de datos
///
///
class DepositCreateLoadFailure extends DepositCreateState {
  final String error;

  DepositCreateLoadFailure({required this.error}) : super();

  @override
  List<Object> get props => [error];
}

///
/// Se ha iniciado el proceso de compra de crédito (se está redirigiendo a stripe)
/// Este estado, en realidad, no llega a generarse nunca (porque el pago con stripe implica navegar fuera de la aplicación)
///
class DepositCreatePurchasingState extends DepositCreateLoaded {
  DepositCreatePurchasingState({required List<DepositAmountModel> amounts, required DepositModel deposit})
      : super(amounts: amounts, deposit: deposit);
  factory DepositCreatePurchasingState.fromLoadedState(DepositCreateLoaded srcState) {
    return DepositCreatePurchasingState(amounts: srcState.amounts, deposit: srcState.deposit);
  }
}

///
/// Se ha producido un error mientras se prepara el salto a stripe (Ej: ha fallado la preparación de la sesión de stripe porque el "precio" que se quiere usar no existe)
///
class DepositCreatePurchasingError extends DepositCreateLoaded {
  // Generamos un valor numérico distinto cada 5 segundos... si dos errores se producen muy proximos solo el primero vale.
  final lastError = (new DateTime.now().millisecondsSinceEpoch / 5000).floor();
  final String error;

  DepositCreatePurchasingError(
      {required this.error, required List<DepositAmountModel> amounts, required DepositModel deposit})
      : super(amounts: amounts, deposit: deposit) {
    print(this.lastError);
  }
  factory DepositCreatePurchasingError.fromLoadedState(DepositCreateLoaded srcState, {required String error}) {
    return DepositCreatePurchasingError(amounts: srcState.amounts, deposit: srcState.deposit, error: error);
  }

  @override
  List<Object> get props => [lastError, error];
}
