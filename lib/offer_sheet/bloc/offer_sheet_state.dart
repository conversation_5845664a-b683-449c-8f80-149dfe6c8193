part of 'offer_sheet_bloc.dart';

enum OfferSheetStatus { initial, success, failure }

class OfferSheetState extends Equatable {
  const OfferSheetState({
    this.status = OfferSheetStatus.initial,
    this.imIndividual = false,
    this.definibleFields = const {},
    this.offerPropertyMedias = const [],
    this.offer = const None(),
    this.isFavourite = false,
    this.isMine = false,
    this.isVersion = false,
    this.isCloud = false,
  });
  // Soy un particular?
  final bool imIndividual;
  // Actual status of the screen
  final OfferSheetStatus status;
  // Wich filter are we applying to the oportunities of kind "offers"
  final Optional<OfferDto> offer;

  final bool isFavourite;
  final bool isMine;
  final bool isCloud;
  final bool isVersion;

  final List<PropertymediaDto> offerPropertyMedias;

  final Map<String, FieldDefDto> definibleFields;

  OfferSheetState copyWith({
    OfferSheetStatus? status,
    bool? imIndividual,
    Optional<OfferDto>? offer,
    bool? isFavourite,
    bool? isMine,
    bool? isCloud,
    bool? isVersion,
    Map<String, FieldDefDto>? definibleFields,
    List<PropertymediaDto>? offerPropertyMedias,
  }) {
    return OfferSheetState(
      status: status ?? this.status,
      imIndividual: imIndividual ?? this.imIndividual,
      isMine: isMine ?? this.isMine,
      isCloud: isCloud ?? this.isCloud,
      isVersion: isVersion ?? this.isVersion,
      isFavourite: isFavourite ?? this.isFavourite,
      offer: offer ?? this.offer,
      definibleFields: definibleFields ?? this.definibleFields,
      offerPropertyMedias: offerPropertyMedias ?? this.offerPropertyMedias,
    );
  }

  @override
  List<Object> get props => [status, offer, isFavourite, offerPropertyMedias.length, isVersion];
}
