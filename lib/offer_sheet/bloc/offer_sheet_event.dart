part of 'offer_sheet_bloc.dart';

abstract class OfferSheetEvent extends Equatable {
  const OfferSheetEvent();

  @override
  List<Object> get props => [];
}

class OfferSheetOnFetchEvent extends OfferSheetEvent {
  final String offerId;
  OfferSheetOnFetchEvent({required this.offerId});
}
class OfferSheetOnSetUnsetFavourite extends OfferSheetEvent {
  OfferSheetOnSetUnsetFavourite();
}
class OfferSheetOnFetchedEvent extends OfferSheetEvent {
  final OfferDto offer;
  OfferSheetOnFetchedEvent({required this.offer});
}

class OfferSheetOnPropertymediaCreated extends OfferSheetEvent {
  final PropertymediaDto propertymedia;
  OfferSheetOnPropertymediaCreated({required this.propertymedia});
  @override
  List<Object> get props => [propertymedia];
}


class OfferSheetOnRefreshEvent extends OfferSheetEvent {}
