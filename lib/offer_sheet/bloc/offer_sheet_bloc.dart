import 'dart:async';

import 'package:agentor_repositoryns/exception_utils.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:topbrokers/global/session_bloc.dart';

part 'offer_sheet_event.dart';
part 'offer_sheet_state.dart';

class OfferSheetBloc extends Bloc<OfferSheetEvent, OfferSheetState> {
  final AppModelsNS appModels;
  final SessionBloc globalBloc;
  late StreamSubscription<ModelsChannelState> _offersChannelSubscription;

  OfferSheetBloc({
    required this.appModels,
    required this.globalBloc,
    required ModelsChannelBloc modelsChannel,
  }) : super(OfferSheetState()) {
    
    this._offersChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelUpdatedState<OfferDto> && this.state.offer is Some) {
        final updatedIds = state.entities.map((o)=>o.id);

        if (updatedIds.contains(this.state.offer.v.id) || updatedIds.contains(this.state.offer.v.version.v?.of.v.id)) {
          // Si una de las ofertas cambiadas es la nuestra o es aquella de la que somos versión, recargamos!!! (ej: refrescar por un paso a histórico)
          this.add(OfferSheetOnFetchEvent(offerId: this.state.offer.v.id.v));
        }
      }
    });
  }

  factory OfferSheetBloc.fromContext(BuildContext context) {
    return OfferSheetBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      globalBloc: BlocProvider.of<SessionBloc>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _offersChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<OfferSheetEvent, OfferSheetState>> transformEvents(
    Stream<OfferSheetEvent> events,
    TransitionFunction<OfferSheetEvent, OfferSheetState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<OfferSheetState> mapEventToState(OfferSheetEvent event) async* {
    if (event is OfferSheetOnFetchEvent) {
      yield* _mapOnFetchToState(state, event);
    } else if (event is OfferSheetOnPropertymediaCreated) {
      yield* _mapOnPropertymediaCreated(state, event);
    } else if (event is OfferSheetOnSetUnsetFavourite) {
      yield* _mapOnSetUnsetFavourite(state, event);
    }
  }

  Stream<OfferSheetState> _mapOnFetchToState(OfferSheetState state, OfferSheetOnFetchEvent event) async* {
    try {
      final myAgent = _myAgent();
      final offer = (await appModels.readOffer(event.offerId)) ?? doThrow<OfferDto>("Unknown offer ${event.offerId}");
      final isFavourite = (await appModels.isOfferInMyFavourites(event.offerId));
      final offerPropertyMedias = await appModels.listOfferPropertymedias(event.offerId);
      final definibleFields = await _readDefinibleFields();
      yield state.copyWith(
        status: OfferSheetStatus.success,
        imIndividual: myAgent?.isIndividual == True,
        offer: Some(offer),
        isFavourite: isFavourite,
        isMine: offer.agent.vn?.id == myAgent?.id,
        isCloud: offer.agent.vn?.isInternal == True,
        isVersion: offer.version.vn?.type.vn?.code == null ? false : true,
        definibleFields: definibleFields,
        offerPropertyMedias: offerPropertyMedias,
      );
    } on Exception catch (e) {
      print("Exception $e");
      yield state.copyWith(status: OfferSheetStatus.failure);
    }
  }

  Stream<OfferSheetState> _mapOnPropertymediaCreated(
    OfferSheetState state,
    OfferSheetOnPropertymediaCreated event,
  ) async* {
    try {
      yield state.copyWith(offerPropertyMedias: [event.propertymedia]..addAll(state.offerPropertyMedias));
    } on Exception {
      yield state.copyWith(status: OfferSheetStatus.failure);
    }
  }

  Stream<OfferSheetState> _mapOnSetUnsetFavourite(
    OfferSheetState state,
    OfferSheetOnSetUnsetFavourite event,
  ) async* {
    try {
      if (state.offer.vn?.id.v != null) {
        if (!state.isFavourite)
          await appModels.addOfferToMyFavourites(state.offer.v.id.v);
        else
          await appModels.removeOfferFromMyFavourites(state.offer.v.id.v);
      }
      yield state.copyWith(isFavourite: !state.isFavourite);
    } on Exception {
      yield state.copyWith(status: OfferSheetStatus.failure);
    }
  }

  Future<Map<String, FieldDefDto>> _readDefinibleFields() async {
    final result = await appModels.listOfferFielddefs();
    return Map.fromEntries(result.map((fieldDef) => MapEntry(fieldDef.code, fieldDef)));
  }

  AgentDto? _myAgent() => globalBloc.state.agent.vn;
}
