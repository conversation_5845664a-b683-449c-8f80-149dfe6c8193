import 'package:agentor_deps/agentor_deps.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:topbrokers/action_edit/models/action_edit_page_params.dart';
import 'package:topbrokers/actionslist/views/embedded_actionslist.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/app_constants.dart';
import 'package:topbrokers/cloudoffer_import/widgets/cloudoffer_import_dialog.dart';
import 'package:topbrokers/cloudoffer_refresh/widgets/widgets.dart';
import 'package:topbrokers/common/common_wrappers.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/titled_card.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/matchingslist/views/embedded_offer_matchings.dart';
import 'package:topbrokers/offer_edit/models/offer_edit_page_params.dart';
import 'package:topbrokers/offer_publications_sheet/views/embedded_offer_publications_sheet.dart';
import 'package:topbrokers/offer_sheet/bloc/offer_sheet_bloc.dart';
import 'package:topbrokers/propertymedias_banner/views/propertymedias_banner.dart';
import 'package:topbrokers/routes.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:topbrokers/offer_clone/widgets/widgets.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../common/widgets/offer_long_link.dart';
import '../../offers_list/widgets/offer_list_item.dart';

//const _c_card_padding = const EdgeInsets.fromLTRB(15, 15, 15, 15);
//const _c_card_margin = const EdgeInsets.fromLTRB(5, 5, 5, 5);

const _c_lang = "es";
const _c_lineheight = 1.5;
const _c_locale = "es_ES";

class OfferSheetPage extends StatefulWidget {
  final String offerId;

  OfferSheetPage({Key? key, required this.offerId}) : super(key: key ?? AgentorKeys.newOfferPage);

  @override
  _OfferSheetPageState createState() => _OfferSheetPageState();
}

class _OfferSheetPageState extends State<OfferSheetPage> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Cada una de las imágenes seleccionadas que están siendo subidas
  final List<PickedFile> uploadingImages = [];

  List<InlineSpan> addSeparation(List<Iterable<InlineSpan>?> texts) {
    return texts.fold(
      <InlineSpan>[],
      (allSpans, spans) {
        if (spans == null)
          return allSpans;
        else
          return allSpans
            ..addAll([
              if (allSpans.length != 0 && spans.length != 0) TextSpan(text: "\n"),
              if (spans.length != 0) TextSpan(text: "- "),
            ])
            ..addAll(spans);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    //final localizations = AppLocalizations.of(context);
    return BlocProvider(
      create: (BuildContext context) {
        return OfferSheetBloc.fromContext(context)
          ..add(
            OfferSheetOnFetchEvent(offerId: this.widget.offerId),
          );
      },
      child: BlocConsumer<OfferSheetBloc, OfferSheetState>(
        listener: (context, state) {},
        buildWhen: (previous, actual) => true,
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: Text("Oferta #${widget.offerId}"),
              centerTitle: true,
              actions: _buildActions(context, state),
            ),
            body: WidthLimiter(
              child: _buildBody(context),
            ),
          );
        },
      ),
    );
  }

  @override
  @mustCallSuper
  void dispose() {
    uploadingImages.clear();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  List<Widget> _buildActions(BuildContext context, OfferSheetState state) {
    return [
      if (state.isMine)
        IconButton(
          icon: const Icon(Icons.edit),
          tooltip: 'Edit',
          onPressed: () {
            Navigator.pushNamed(
              context,
              AgentorRoutes.editOffer,
              arguments: ExistingOfferEditParams(id: widget.offerId),
            );
          },
        ),
      if (state.isMine)
        _buildOfferMenu(context, state)
      else if (state.isCloud && !state.imIndividual)
        _buildCloudMenu(context)
    ];
  }

  List<Widget> _buildAddressCard(BuildContext context, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    final addr = offer.property.vn?.address.vn;
    final zone = offer.property.vn?.zone.vn;
    if (addr == null) {
      return [];
    } else {
      return <Widget>[
        if (addr.composedStreetLine.vn != null) Text(addr.composedStreetLine.v, style: textStyle),
        if (addr.detail.vn != null) Text(addr.detail.v!, style: textStyle),
        if (addr.city.vn != null && addr.postcode.vn != null)
          Text("${addr.postcode.v} ${addr.city.v.label.v.getByLang(_c_lang)}", style: textStyle),
        if (addr.city.vn != null && addr.postcode.vn == null)
          Text("${addr.city.v.label.v.getByLang(_c_lang)}", style: textStyle),
        if (addr.city.vn?.province.vn?.label.vn != null)
          Text(addr.city.v.province.v.label.v.getByLang(_c_lang), style: textStyle),
        if (Deps.solve<AppConstants>().zonesEnabled && zone != null)
          _buildRichText(
            context,
            fieldsDefinition[FielddefsSrv.c_prop_zone]!.buildCustomEntrySpan(
              context,
              getStringValue: () => zone.name.vn,
            ),
          ),
      ];
    }
  }

  List<Widget> _buildAgentCard(BuildContext context, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final agent = offer.agent.v;
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    final iconSize = (Theme.of(context).iconTheme.size ?? 24.0) * 0.8;
    return <Widget>[
      Flexible(
        child: Row(children: [
          Expanded(
            child: RichText(
              softWrap: true,
              text: TextSpan(
                text: agent.name.vn ?? "",
                style: textStyle,
                children: [],
              ),
            ),
          ),
        ]),
      ),
      if (agent.email.vn != null)
        TextButton.icon(
          icon: Icon(Icons.email, size: iconSize),
          label: Text(agent.email.v),
          onPressed: () async {
            final url = "mailto:${agent.email.v}";
            if (await canLaunchUrlString(url)) {
              await launchUrlString(url);
            }
          },
        ),
      if (agent.mobile.vn != null)
        Row(
          children: [
            TextButton.icon(
              icon: Icon(Icons.smartphone, size: iconSize),
              label: Text(agent.mobile.v!),
              onPressed: () async {
                final url = "tel:${agent.mobile.v!}";
                if (await canLaunchUrlString(url)) {
                  await launchUrlString(url);
                }
              },
            ),
          ],
        ),
    ];
  }

  List<Widget> _buildAttributesCard(BuildContext context, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final attrs = offer.property.vn?.attributes.vn;
    if (attrs == null) {
      return [];
    } else {
      return [
        _buildRichText(
          context,
          addSeparation(
            [
              fieldsDefinition[FielddefsSrv.c_prop_attrs_status]
                  ?.buildEntrySpans(context, getValue: () => attrs.statusCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_totalSurface]
                  ?.buildEntrySpans(context, getValue: () => attrs.totalSurfaceM2.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_usefulSurface]
                  ?.buildEntrySpans(context, getValue: () => attrs.usefulSurfaceM2.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_solarSurface]
                  ?.buildEntrySpans(context, getValue: () => attrs.solarSurfaceM2.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_constructionYear]
                  ?.buildEntrySpans(context, getValue: () => attrs.constructionYear.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_conservationStatus]
                  ?.buildEntrySpans(context, getValue: () => attrs.conservationStatusCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_individualBedroomsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.individualBedroomsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_doubleBedroomsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.doubleBedroomsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_suiteBedroomsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.suiteBedroomsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_totalBedroomsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.totalBedroomsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_bathroomsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.bathroomsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_toiletsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.toiletsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_builtInCabinetsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.builtInCabinetsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_buddleHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.buddleHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_dinningRoomHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.dinningRoomHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_storageRoomHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.storageRoomHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_balconyHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.balconyHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_terraceHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.terraceHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_externalJoinery]
                  ?.buildEntrySpans(context, getValue: () => attrs.externalJoineryCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_ground]
                  ?.buildEntrySpans(context, getValue: () => attrs.groundCodes.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_waterSupplyHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.waterSupplyHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_powerSupplyHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.powerSupplyHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_gasSupplyHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.gasSupplyHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_airConditioning]
                  ?.buildEntrySpans(context, getValue: () => attrs.airConditioningCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_heating]
                  ?.buildEntrySpans(context, getValue: () => attrs.heatingCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_fireplaceHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.fireplaceHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_intercomHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.intercomHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_reinforcedDoorHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.reinforcedDoorHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_alarmSystemHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.alarmSystemHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_elevatorHas]
                  ?.buildEntrySpans(context, getValue: () => attrs.elevatorHas.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_handicappedAccessibleIs]
                  ?.buildEntrySpans(context, getValue: () => attrs.handicappedAccessibleIs.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_furnishedIs]
                  ?.buildEntrySpans(context, getValue: () => attrs.furnishedIs.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_garden]
                  ?.buildEntrySpans(context, getValue: () => attrs.gardenCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_outsideArea]
                  ?.buildEntrySpans(context, getValue: () => attrs.outsideAreaCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_swimmingPool]
                  ?.buildEntrySpans(context, getValue: () => attrs.swimmingPoolCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_parkingPlacesCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.parkingPlacesCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_optionalParkingIs]
                  ?.buildEntrySpans(context, getValue: () => attrs.optionalParkingIs.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_facade]
                  ?.buildEntrySpans(context, getValue: () => attrs.facadeCodes.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_orientation]
                  ?.buildEntrySpans(context, getValue: () => attrs.orientationCodes.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_sunnyIs]
                  ?.buildEntrySpans(context, getValue: () => attrs.sunnyIs.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_communityFeesAmount]
                  ?.buildEntrySpans(context, getValue: () => attrs.communityFeesAmount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_neighborsPerFloorCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.neighborsPerFloorCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_buildingFloorsCount]
                  ?.buildEntrySpans(context, getValue: () => attrs.buildingFloorsCount.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_energyCertificate]
                  ?.buildEntrySpans(context, getValue: () => attrs.energyCertificateCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_consumptionLevel]
                  ?.buildEntrySpans(context, getValue: () => attrs.consumptionLevelCode.vn),
              fieldsDefinition[FielddefsSrv.c_prop_attrs_emissionLevel]
                  ?.buildEntrySpans(context, getValue: () => attrs.emissionLevelCode.vn),
            ],
          ),
        ),
      ];
    }
  }

  Widget _buildBody(BuildContext context) {
    return BlocConsumer<OfferSheetBloc, OfferSheetState>(
      listener: (context, state) {},
      buildWhen: (previous, actual) => true,
      builder: (context, state) {
        final localizations = context.getAppLocalizationsOrThrow();
        switch (state.status) {
          case OfferSheetStatus.failure:
            return Center(child: Text(localizations.offer_errLoading));
          case OfferSheetStatus.success:
            if (state.offer is None)
              return Center(
                child: Text(
                  localizations.offer_errDoesnExist,
                ),
              );
            else
              return RefreshIndicator(
                onRefresh: () async {
                  context.bloc<OfferSheetBloc>().add(OfferSheetOnFetchEvent(offerId: widget.offerId));
                },
                child: Container(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(5, 5, 5, 5),
                    child: _buildMainAttributesCard(
                      context: context,
                      offer: state.offer.v,
                      imIndividual: state.imIndividual,
                      isFavourite: state.isFavourite,
                      isMine: state.isMine,
                      isCloud: state.isCloud,
                      fieldsDefinitions: state.definibleFields,
                    ),
                  ),
                ),
              );

          default:
            return const Center(
              child: CircularProgressIndicator(),
            );
        }
      },
    );
  }

  PopupMenuButton<String> _buildCloudMenu(BuildContext context) {
    return PopupMenuButton<String>(
      padding: const EdgeInsets.all(0.0),
      icon: Icon(
        Icons.more_vert,
      ),
      tooltip: "Menú",
      onSelected: (String value) async {
        switch (value) {
          case "import":
            final createdOfferId = await importCloudofferDialog(context, cloudOfferId: widget.offerId);
            if (createdOfferId != null) {
              // Cerramos la hoja de la oferta cloud
              Navigator.of(context).pop(null);
              // Abrimos la hoja de la oferta recién creada
              Navigator.pushNamed(
                context,
                AgentorRoutes.showOffer,
                arguments: createdOfferId,
              );
            }
            break;
          case "refresh":
            await refreshCloudofferDialog(context, cloudOfferId: widget.offerId);
            break;

          default:
            break;
        }
      },
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<String>(
          value: "refresh",
          child: Row(children: [
            Padding(
              padding: EdgeInsets.fromLTRB(0, 5, 10, 5),
              child: Icon(Icons.refresh_rounded, color: Colors.grey.shade700),
            ),
            Text("Actualizar"),
          ]),
        ),
        PopupMenuItem<String>(
          value: "import",
          child: Row(children: [
            Padding(
              padding: EdgeInsets.fromLTRB(0, 5, 10, 5),
              child: Icon(Icons.cloud_download, color: Colors.grey.shade700),
            ),
            Text("Importar a mi cartera"),
          ]),
        ),
      ],
    );
  }

  PopupMenuButton<String> _buildOfferMenu(BuildContext context, OfferSheetState state) {
    final localization = context.getAppLocalizationsOrThrow();
    return PopupMenuButton<String>(
      padding: const EdgeInsets.all(0.0),
      icon: Icon(
        Icons.more_vert,
      ),
      tooltip: "Menú",
      onSelected: (String value) async {
        switch (value) {
          case "create_version":
            final createdOfferId = await offerCloneDialog(context, cloudOfferId: widget.offerId);
            if (createdOfferId != null) {
              // Cerramos la hoja de la oferta cloud
              Navigator.of(context).pop(null);
              // Abrimos la hoja de la oferta recién creada
              Navigator.pushNamed(
                context,
                AgentorRoutes.showOffer,
                arguments: createdOfferId,
              );
            }
            break;
          case "refresh":
            await refreshCloudofferDialog(context, cloudOfferId: widget.offerId);
            break;

          default:
            break;
        }
      },
      itemBuilder: (BuildContext context) => [
        if (!state.imIndividual)
          PopupMenuItem<String>(
            value: "refresh",
            child: Row(children: [
              Padding(
                padding: EdgeInsets.fromLTRB(0, 5, 10, 5),
                child: Icon(Icons.refresh_rounded, color: Colors.grey.shade700),
              ),
              Text("Actualizar"),
            ]),
          ),
        if (!state.imIndividual)
          PopupMenuItem<String>(
            value: (state.isVersion) ? "disabled_create_version" : "create_version",
            child: Row(children: [
              Padding(
                padding: EdgeInsets.fromLTRB(0, 5, 10, 5),
                child: Icon(Icons.copy, color: Colors.grey.shade700),
              ),
              state.isVersion
                  ? Text(
                      localization.offer_cloneDialog_menu_option,
                      style: TextStyle(decoration: TextDecoration.lineThrough),
                    )
                  : Text(localization.offer_cloneDialog_menu_option),
            ]),
          ),
      ],
    );
  }

  List<Widget> _buildCustomerCard(BuildContext context, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition,
      {bool isMine = false}) {
    final customer = offer.customer.v;
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    final iconSize = (Theme.of(context).iconTheme.size ?? 24.0) * 0.8;
    return <Widget>[
      new Flexible(
        child: Row(children: [
          Expanded(
            child: RichText(
              softWrap: true,
              text: TextSpan(
                text: customer?.name.v,
                style: textStyle,
                children: [],
              ),
            ),
          ),
          if (isMine)
            TextButton.icon(
              icon: Icon(Icons.open_in_new, size: iconSize),
              label: Text(""),
              onPressed: () {
                if (customer?.id.vn != null) {
                  Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: customer!.id.v);
                }
              },
            )
        ]),
      ),
      if (customer?.email.vn != null)
        TextButton.icon(
          icon: Icon(Icons.email, size: iconSize),
          label: Text(customer!.email.v!),
          onPressed: () async {
            final url = "mailto:${customer.email.v}";
            if (await canLaunch(url)) {
              await launch(url);
            }
          },
        ),
      if (customer?.mobile.vn != null)
        Row(
          children: [
            TextButton.icon(
              icon: Icon(Icons.smartphone, size: iconSize),
              label: Text(customer!.mobile.v!),
              onPressed: () async {
                final url = "tel:${customer.mobile.v!}";
                if (await canLaunch(url)) {
                  await launch(url);
                }
              },
            ),
          ],
        ),
    ];
  }

  List<Widget> _buildHeadingCard(
      BuildContext context, bool isFavourite, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final localization = context.getAppLocalizationsOrThrow();
    final textTheme = Theme.of(context).textTheme;
    final boldStyle = textTheme.bodyLarge?.copyWith(height: _c_lineheight);
    final sale = offer.sale.vn?.allowed == True ? offer.sale.v : null;
    final rent = offer.rent.vn?.allowed == True ? offer.rent.v : null;
    final version = offer.version.vn?.type.vn?.code != null ? offer.version.v : null;
    final currencyISO = offer.currency.vn?.code.vn ?? "EUR";

    return <Widget>[
      _buildRichText(
        context,
        [
          TextSpan(text: "Ref.: "),
          TextSpan(text: "#${offer.reference.vn ?? ""}"),
        ],
      ),
      _buildRichText(
        context,
        [
          if (offer.property.vn?.type.vn?.label is Some)
            TextSpan(text: "${offer.property.v.type.v.label.v.localized.capitalize} ", style: boldStyle),
          if (offer.property.vn?.subtype.vn?.label is Some)
            TextSpan(text: "(${offer.property.v.subtype.v!.label.v.localized.capitalize}) ", style: boldStyle),
          if (sale != null) TextSpan(text: "${localization.common_forSale} "),
          if (sale?.amount.vn != null && sale!.amount.v! > 0)
            TextSpan(
                text: "${sale.amount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)} "),
          if (sale?.marketAmount.vn != null && sale!.marketAmount.v! > 0)
            TextSpan(
              text:
                  "(${localization.common_marketPrice} ${sale.marketAmount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)}) ",
            ),
          if (rent != null && sale != null)
            TextSpan(text: "${localization.common_and} ${localization.common_forRent} "),
          if (rent != null && sale == null) TextSpan(text: "${localization.common_forRent} "),
          if (rent != null && rent.amount.vn != null && rent.amount.v! > 0)
            TextSpan(
              text: "${rent.amount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)} ",
            ),
          if (rent != null && rent.marketAmount.vn != null && rent.marketAmount.v! > 0)
            TextSpan(
              text:
                  "(${localization.common_marketPrice} ${rent.marketAmount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)}) ",
            ),
        ],
      ),
      if (offer.status.vn?.code.vn != null)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_status]!.buildEntrySpans(
            context,
            getValue: () => offer.status.vn?.code.vn?.enumToString(),
          ),
        ),
      if (offer.urgency.vn != null)
        Row(
          children: [
            RichText(
              softWrap: true,
              text: TextSpan(
                text: '${fieldsDefinition[FielddefsSrv.c_offer_urgency]?.label?.localized.capitalize ?? ""}: ',
                style: boldStyle,
              ),
            ),
            for (var i = 0; i < 4; i++)
              if (offer.urgency.v != null && i <= offer.urgency.v!)
                Icon(Icons.star_rounded, size: 12)
              else
                Icon(Icons.star_border_rounded, size: 12),
          ],
        ),
    ];
  }

  List<Widget> _buildInternalCard(BuildContext context, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final mandate = offer.mandate.vn;
    final saleFee = offer.sale.vn?.allowed == True ? offer.sale.vn?.fee.vn : null;

    return <Widget>[
      if (saleFee?.type.vn?.code.vn == SalefeetypeCode.fixed)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_sale_fee_fixedValue]!.buildEntrySpans(
            context,
            getValue: () => saleFee?.value.vn,
          ),
        ),
      if (saleFee?.type.vn?.code.vn == SalefeetypeCode.percent)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_sale_fee_percentValue]!.buildEntrySpans(
            context,
            getValue: () => saleFee?.value.vn,
          ),
        ),
      if (mandate?.type.vn?.code.vn != null)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_mandate_type]!.buildEntrySpans(
            context,
            getValue: () => mandate?.type.v.code.v.enumToString(),
          ),
        ),
      if (mandate?.start.vn != null)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_mandate_start]!.buildEntrySpans(
            context,
            getValue: () => mandate?.start.v,
          ),
        ),
      if (mandate?.end.vn != null)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_mandate_end]!.buildEntrySpans(
            context,
            getValue: () => mandate?.end.v,
          ),
        ),
      if (offer.notes.vn != null)
        _buildRichText(
          context,
          fieldsDefinition[FielddefsSrv.c_offer_notes]!.buildEntrySpans(
            context,
            getValue: () => offer.notes.v,
          ),
        ),
    ];
  }

  Widget _buildMainAttributesCard({
    required BuildContext context,
    required OfferDto offer,
    required bool imIndividual,
    required bool isMine,
    required bool isCloud,
    required bool isFavourite,
    required Map<String, FieldDefDto> fieldsDefinitions,
  }) {
    final localizations = context.getAppLocalizationsOrThrow();
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,

      children: [
        if (!isMine)
          SwitchListTile.adaptive(
            title: Text(localizations.offer_favouriteLabel),
            value: isFavourite,
            onChanged: (bool? v) {
              if (v != null) context.bloc<OfferSheetBloc>().add(OfferSheetOnSetUnsetFavourite());
            },
          ),
      ]..addAll([
          if (offer.version.vn?.type.vn?.code.vn != null)
            TitledCard(
              Text(localizations.offer_versionHeading, style: textTheme.titleLarge),
              children: _buildVersionCard(context, isFavourite, offer, fieldsDefinitions),
            ),
          TitledCard(
            Text(localizations.offer_mainHeading, style: textTheme.titleLarge),
            children: _buildHeadingCard(context, isFavourite, offer, fieldsDefinitions),
          ),

          if (offer.source.vn?.pageUrl.vn != null)
            TitledCard(
              Text(localizations.offer_srcadHeading, style: textTheme.titleLarge),
              children: _buildSourceCard(context, isFavourite, offer, fieldsDefinitions),
            ),
          if (offer.customer.vn != null)
            TitledCard(
              Text(localizations.offer_ownerHeading, style: textTheme.titleLarge),
              children: _buildCustomerCard(context, offer, fieldsDefinitions, isMine: isMine),
            ),
          TitledCard(
            Text(localizations.offer_picturesHeading, style: textTheme.titleLarge),
            children: _buildPicturesCard(context, offer, !isMine),
          ),
          TitledCard(
            Text(localizations.offer_addressHeading, style: textTheme.titleLarge),
            children: _buildAddressCard(context, offer, fieldsDefinitions),
          ),
          TitledCard(
            Text(localizations.offer_featuresHeading, style: textTheme.titleLarge),
            children: _buildAttributesCard(context, offer, fieldsDefinitions),
          ),
          if (offer.description.vn != null)
            TitledCard(
              Text(localizations.offer_descriptionHeading, style: textTheme.titleLarge),
              children: offer.description.vn
                      ?.split("\n")
                      .map((String line) => _buildRichText(context, [TextSpan(text: line)]))
                      .toList() ??
                  [],
            ),

          if (isMine)
            _buildInternalCard(context, offer, fieldsDefinitions).withValue((items) {
              if (items.length != 0)
                return TitledCard(
                  Text(localizations.offer_internalsHeading, style: textTheme.titleLarge),
                  children: items,
                );
              else
                return null;
            }),
          if (!isMine && offer.agent.vn != null && !(offer.agent.vn?.isInternal.vn ?? true))
            TitledCard(
              Text(localizations.offer_agentHeading, style: textTheme.titleLarge),
              children: _buildAgentCard(context, offer, fieldsDefinitions),
            ),
          if (isMine)
            TitledCard(
              Text(localizations.offer_publicationsHeading, style: textTheme.titleLarge),
              children: [
                if (offer.status.vn?.code.vn == OfferstatusCode.commercialization)
                  // memberCanRead:false -> Cuando el miembro puede publicar pero no leer se trata de un portal (se publica)
                  EmbeddedOfferpublicationsList(offerId: offer.id, memberCanRead: false)
                else
                  _buildRichText(context, [HTML.toTextSpan(context, localizations.offer_cantPublishDisclaimer)]),
              ],
            ),

          if (!imIndividual && isMine && offer.status.vn?.code.vn == OfferstatusCode.commercialization)
            TitledCard(
              Text(localizations.offer_sharingsHeading, style: textTheme.titleLarge),
              children: [
                // memberCanRead:true -> Cuando el miembro puede publicar y leer se trata de un grupo de trabajo normal (se comparte)
                EmbeddedOfferpublicationsList(offerId: offer.id, memberCanRead: true)
              ],
            ),
          TitledCard(
            Text(localizations.offer_actionsHeading, style: textTheme.titleLarge),
            children: [
              EmbeddedActionsList(offerId: offer.id.vn),
            ],
            barActions: [
              TextButton.icon(
                icon: Icon(Icons.add),
                label: Text(localizations.common_add),
                onPressed: () {
                  assert(offer.id.vn != null);
                  Navigator.pushNamed(
                    context,
                    AgentorRoutes.addAction,
                    arguments: NewActionEditParams(offerId: offer.id.v),
                  );
                },
              )
            ],
          ),
          //if (isMine)
          TitledCard(Text(localizations.offer_matchingsHeading, style: textTheme.titleLarge), children: [
            EmbeddedOfferMatchingsList(offerId: offer.id.v),
          ]),
        ].where((TitledCard? w) => w != null).map((TitledCard? w) => w as TitledCard)), //.toList(),
    );
  }

  List<Widget> _buildPicturesCard(BuildContext context, OfferDto model, bool readOnly) =>
      [PropertymediasBanner(offerId: widget.offerId, readOnly: readOnly)];

  Flexible _buildRichText(context, Iterable<InlineSpan> lines) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    return Flexible(
      child: RichText(
        softWrap: true,
        textAlign: TextAlign.left,
        text: TextSpan(
          text: "",
          style: textStyle,
          children: lines.toList(),
        ),
      ),
    );
  }

  List<Widget> _buildSourceCard(
      BuildContext context, bool isFavourite, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final localization = context.getAppLocalizationsOrThrow();
    final textTheme = Theme.of(context).textTheme;
    final boldStyle = textTheme.bodyLarge?.copyWith(height: _c_lineheight);
    final iconSize = (Theme.of(context).iconTheme.size ?? 24.0) * 0.8;
    return <Widget>[
      if (offer.source.vn?.pageUrl.vn != null)
        TextButton(
          child: Row(
            children: [Icon(Icons.open_in_browser), Text(localization.offer_srcad_webpageLabel.capitalize)],
          ),
          onPressed: () async {
            final url = offer.source.vn?.pageUrl.vn;

            if (url != null && await canLaunchUrlString(url)) {
              await launchUrlString(url);
            }
          },
        ),
      if (offer.source.vn?.reference.vn != null)
        _buildRichText(
          context,
          [
            TextSpan(text: '${localization.offer_srcad_referenceLabel.capitalize}: '),
            TextSpan(
              text: offer.source.vn?.reference.vn ?? "",
              //style: boldStyle?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      if (offer.source.v.updated.vn?.at.vn != null)
        _buildRichText(
          context,
          [
            TextSpan(text: '${localization.offer_srcad_updatedAtLabel.capitalize}: '),
            TextSpan(
              text: offer.source.v.updated.v!.at.v.toLocal().toString(),
              style: boldStyle?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      if (offer.source.vn?.announcedByAnIndividual.vn ?? false)
        _buildRichText(
          context,
          [
            TextSpan(text: '${localization.offer_srcad_advertiserLabel.capitalize}: '),
            TextSpan(
              text: localization.offer_srcad_individualLabel.capitalize,
              style: boldStyle?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      if (offer.source.vn?.contact.vn?.name.vn != null)
        _buildRichText(
          context,
          [
            TextSpan(text: '${localization.offer_srcad_nameLabel.capitalize}: '),
            TextSpan(
              text: offer.source.vn?.contact.vn?.name.v,
              style: boldStyle?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      if (offer.source.vn?.contact.vn?.phone.vn != null)
        Row(children: [
          TextButton.icon(
            icon: Icon(Icons.smartphone, size: iconSize),
            label: Text(offer.source.v.contact.v!.phone.v!),
            onPressed: () async {
              final url = "tel:${offer.source.v.contact.v!.phone.v!}";
              if (await canLaunchUrlString(url)) {
                await launchUrlString(url);
              }
            },
          ),
        ]),
    ];
  }

  List<Widget> _buildVersionCard(
      BuildContext context, bool isFavourite, OfferDto offer, Map<String, FieldDefDto> fieldsDefinition) {
    final localization = context.getAppLocalizationsOrThrow();
    final textTheme = Theme.of(context).textTheme;
    final boldStyle = textTheme.bodyLarge?.copyWith(height: _c_lineheight);
    final textStyle = textTheme.bodyMedium?.copyWith(height: _c_lineheight);
    final iconSize = (Theme.of(context).iconTheme.size ?? 24.0) * 0.8;
    final sale = offer.sale.vn?.allowed == True ? offer.sale.v : null;
    final version = offer.version.vn?.type.vn?.code != null ? offer.version.v : null;
    final currencyISO = offer.currency.vn?.code.vn ?? "EUR";

    return <Widget>[
      Container(
        padding: EdgeInsets.only(bottom: 4.0, left: 4.0, right: 4.0),
        color: Color.fromARGB(255, 189, 207, 205),
        child: Column(children: [
          OfferLongLink(
            offer: version!.of.v,
            textAlign: TextAlign.left,
            isDense: false,
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                '${version.type.v.label.vn?.localized ?? version.type.v.code.vn}',
                style: TextStyle(fontWeight: FontWeight.bold),
              )),
              Text(
                '${sale?.monthlyPayment.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)}',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          SizedBox(
            height: 10,
          ),
          Row(children: [
            Expanded(
              child: Text(
                offer.version.v!.disclaimer.vn!,
              ),
            ),
          ])
        ]),
      ),
    ];
  }
}
