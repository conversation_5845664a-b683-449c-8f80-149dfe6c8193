import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:agentor_repositoryns/models/ecom_purchase_dto.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:topbrokers/common/widgets/offer_long_link.dart';

class PurchaseListItem extends StatelessWidget {
  const PurchaseListItem({
    Key? key,
    required this.purchase,
    this.isSelected = false,
    this.onTap,
  }) : super(key: key);

  final EcomPurchaseDto purchase;
  final bool isSelected;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: Icon(_getPurchaseIcon(purchase)),
        title: Row(children: _titlePart(purchase)),
        isThreeLine: true,
        subtitle: Column(children: [
          Row(children: _moneyPart(purchase)),
          if (purchase.service.vn?.offer.vn != null)
            OfferLongLink(
              offer: purchase.service.vn!.offer.vn!,
              textAlign: TextAlign.left,
            ),
          Row(children: _servicePart(purchase)),
        ]),
        dense: true,
        onTap: onTap,
      ),
    );
  }

  IconData _getPurchaseIcon(EcomPurchaseDto purchase) {
    switch (purchase.service.vn?.type.vn?.code ?? "") {
      case "publication":
        return Icons.public;
      default:
        return Icons.filter_none;
    }
  }

  List<Widget> _moneyPart(EcomPurchaseDto purchase) {
    final firstPayment = purchase.firstPayment.vn;
    final firstDays = firstPayment?.days.vn;
    final firstAmount = firstPayment?.amount.vn;
    final dailyAmount = purchase.dailyamount.vn;
    final totalPayed = purchase.totals.vn?.payed.vn;

    if (purchase.dailyamount.vn == null) {
      return [
        Expanded(child: Text("$totalPayed €")),
        if (totalPayed != firstAmount) Text("$firstAmount €"),
      ];
    } else {
      return [
        Expanded(child: Text("$totalPayed €")),
        Text("$firstAmount € (primeros ${firstDays ?? 0} días) + ${dailyAmount ?? 0} €/día"),
      ];
    }
  }

  List<Widget> _titlePart(EcomPurchaseDto purchase) {
    final date = purchase.date.vn; //?.toLocal().toString() ?? "";
    final dateStr = date != null ? DateFormat.yMMMd(Intl.systemLocale).format(date) : "";
    final productName = purchase.product.vn?.name.vn?.getByLang(Intl.systemLocale) ?? "";

    return [
      Expanded(child: Text(productName)),
      Text(dateStr),
    ];
  }

  List<Widget> _servicePart(EcomPurchaseDto purchase) {
    final date = purchase.service.vn?.enddate.vn?.toLocal();
    final enddateStr = date != null ? DateFormat.yMMMd(Intl.systemLocale).format(date) : "";

    return [
      Expanded(child: Text("")),
      if (enddateStr != "") Text("Finalizado $enddateStr"),
    ];
  }
}
