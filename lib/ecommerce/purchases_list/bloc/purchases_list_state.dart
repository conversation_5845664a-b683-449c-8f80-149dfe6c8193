part of 'purchases_list_bloc.dart';

enum PurchasesStatus { initial, success, failure }

class PurchasesState extends Equatable {
  const PurchasesState({
    this.status = PurchasesStatus.initial,
    this.purchases = const <EcomPurchaseDto>[],
    this.hasReachedMax = false,
    this.filter = const EcomPurchasesListFilter(),
  });

  // Actual status of the screen
  final PurchasesStatus status;
  // The list of actual fetched opportunities
  final List<EcomPurchaseDto> purchases;
  // There is no more opportunities to be fetched
  final bool hasReachedMax;
  //
  final EcomPurchasesListFilter filter;

  PurchasesState copyWith({
    PurchasesStatus? status,
    List<EcomPurchaseDto>? purchases,
    bool? hasReachedMax,
    EcomPurchasesListFilter? filter,
  }) {
    return PurchasesState(
      status: status ?? this.status,
      purchases: purchases ?? this.purchases,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      filter: filter ?? this.filter,
    );
  }

  @override
  List<Object> get props => [status, purchases];
}

class ContactsLoadSuccessLoadFailure extends PurchasesState {}
