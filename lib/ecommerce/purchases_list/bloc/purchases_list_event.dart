part of 'purchases_list_bloc.dart';

abstract class PurchaseslistEvent extends Equatable {
  const PurchaseslistEvent();

  @override
  List<Object> get props => [];
}

class PurchaseslistOnRefresh extends PurchaseslistEvent {}

class PurchaseslistOnFetch extends Purchaseslist<PERSON>vent {}

class PurchaseslistOnFilterChanged extends PurchaseslistEvent {
  final EcomPurchasesListFilter filter;
  const PurchaseslistOnFilterChanged(this.filter);
  @override
  List<Object> get props => [filter];
  @override
  String toString() => "PurchaseslistOnFilterChanged { new filter: $filter }";
}

class PurchaseslistOnContactUpdated extends PurchaseslistEvent {
  final EcomPurchaseDto contact;

  const PurchaseslistOnContactUpdated(this.contact);

  @override
  List<Object> get props => [contact];

  @override
  String toString() => "ContactUpdated { $contact }";
}
