import 'dart:async';

import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:agentor_repositoryns/models/ecom_purchase_dto.dart';
import 'package:agentor_repositoryns/services/ecom_purchases_srv.dart';
import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:provider/provider.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rxdart/rxdart.dart';

part 'purchases_list_event.dart';
part 'purchases_list_state.dart';

class PurchasesBloc extends Bloc<PurchaseslistEvent, PurchasesState> {
  static const _PAGE_SIZE = 30;

  late StreamSubscription<ModelsChannelState> _modelsChannelSubscription;
  AppModelsNS appModels;

  PurchasesBloc({
    required ModelsChannelBloc modelsChannel,
    required this.appModels,
    EcomPurchasesListFilter filter = const EcomPurchasesListFilter(),
  }) : super(PurchasesState(filter: filter)) {
    this._modelsChannelSubscription = modelsChannel.stream.listen((state) {
      if (state is ModelsChannelCreatedState<EcomPurchaseDto> || state is ModelsChannelUpdatedState<EcomPurchaseDto>) {
        this.add(PurchaseslistOnRefresh());
      } else {
        // NADA
      }
    });
  }

  factory PurchasesBloc.create(
    BuildContext context, {
    EcomPurchasesListFilter filter = const EcomPurchasesListFilter(),
  }) {
    return PurchasesBloc(
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      filter: filter,
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _modelsChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<PurchaseslistEvent, PurchasesState>> transformEvents(
    Stream<PurchaseslistEvent> events,
    TransitionFunction<PurchaseslistEvent, PurchasesState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 10)),
      transitionFn,
    );
  }

  @override
  Stream<PurchasesState> mapEventToState(PurchaseslistEvent event) async* {
    if (event is PurchaseslistOnRefresh) {
      yield* _mapOnRefreshToState(state, event);
    } else if (event is PurchaseslistOnFilterChanged) {
      yield* _mapOnFilterChangedToState(state, event);
    } else if (event is PurchaseslistOnFetch) {
      yield* _mapOnFetchToState(state, event);
    }
  }

  Stream<PurchasesState> _mapOnRefreshToState(PurchasesState state, PurchaseslistOnRefresh event) async* {
    try {
      final purchases = await _listPurchases(filter: state.filter, offset: 0);
      yield state.copyWith(
        status: PurchasesStatus.success,
        purchases: purchases,
        hasReachedMax: purchases.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: PurchasesStatus.failure);
    }
  }

  Stream<PurchasesState> _mapOnFilterChangedToState(PurchasesState state, PurchaseslistOnFilterChanged event) async* {
    try {
      final contacts = await _listPurchases(filter: event.filter, offset: 0);
      yield state.copyWith(
        status: PurchasesStatus.success,
        filter: event.filter,
        purchases: contacts,
        hasReachedMax: contacts.length != _PAGE_SIZE,
      );
    } on Exception {
      yield state.copyWith(status: PurchasesStatus.failure);
    }
  }

  Stream<PurchasesState> _mapOnFetchToState(PurchasesState state, PurchaseslistOnFetch event) async* {
    if (state.hasReachedMax)
      yield state;
    else
      try {
        if (state.status == PurchasesStatus.initial) {
          final purchases = await _listPurchases(filter: state.filter, offset: 0);
          yield state.copyWith(
            status: PurchasesStatus.success,
            purchases: purchases,
            hasReachedMax: purchases.length != _PAGE_SIZE,
          );
        } else {
          // Este punto sirve tanto en estado success como failure si se reintentase
          final purchases = await _listPurchases(filter: state.filter, offset: state.purchases.length);
          yield purchases.isEmpty
              ? state.copyWith(hasReachedMax: true)
              : state.copyWith(
                  status: PurchasesStatus.success,
                  purchases: List.of(state.purchases)..addAll(purchases),
                  hasReachedMax:  purchases.length != _PAGE_SIZE,
                );
        }
      } on Exception {
        yield state.copyWith(status: PurchasesStatus.failure);
      }
  }

  Future<List<EcomPurchaseDto>> _listPurchases({
    EcomPurchasesListFilter filter = const EcomPurchasesListFilter(),
    int offset = 0,
  }) {
    return appModels.listPurchases(
      filter: filter,
      offset: offset,
      limit: _PAGE_SIZE,
    );
  }
}
