import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:topbrokers/common/widgets/bottom_loader.dart';
import 'package:topbrokers/ecommerce/purchases_list/bloc/purchases_list_bloc.dart';
import 'package:topbrokers/ecommerce/purchases_list/widgets/purchases_list_item.dart';

class PurchaseslistWidget extends StatefulWidget {
  @override
  _PurchaseslistWidgetState createState() => _PurchaseslistWidgetState();
}

class _PurchaseslistWidgetState extends State<PurchaseslistWidget> {
  final _scrollController = ScrollController();
  late PurchasesBloc _purchasesBloc;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _purchasesBloc = context.bloc<PurchasesBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PurchasesBloc, PurchasesState>(
      listener: (context, state) {
        if (!state.hasReachedMax && _isBottom) {
          _purchasesBloc.add(PurchaseslistOnFetch());
        }
      },
      builder: (context, state) {
        switch (state.status) {
          case PurchasesStatus.failure:
            return const Center(child: Text('Problemas obteniendo contactos del servidor'));
          case PurchasesStatus.success:
            return state.purchases.isEmpty
                ? Center(
                    child: Column(mainAxisSize: MainAxisSize.min, children: [
                    Text('Aún no has realizado ninguna compra', style: TextStyle(color: Colors.grey)),
                    Divider(height: 42),
                    ElevatedButton.icon(
                      icon: Icon(
                        Icons.sell,
                        size: 16,
                      ),
                      label: Text('Ir al catálogo de productos'),
                      onPressed: () {
                        //Navigator.pushNamed(context, AgentorRoutes.addContact);
                      },
                    )
                  ]))
                : RefreshIndicator(
                    onRefresh: () async {
                      _purchasesBloc.add(PurchaseslistOnRefresh());
                    },
                    child: ListView.builder(
                      key: AgentorKeys.purchaseslist,
                      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                      itemBuilder: (BuildContext context, int index) {
                        if (index >= state.purchases.length) {
                          return BottomLoader();
                        } else {
                          return PurchaseListItem(
                            purchase: state.purchases[index],
                            onTap: () {
                              //final purchaseId = state.purchases[index].id.v;
                              //Navigator.pushNamed(context, AgentorRoutes.showContact, arguments: purchaseId);
                            },
                          );
                        }
                      },
                      itemCount: state.hasReachedMax ? state.purchases.length : state.purchases.length + 1,
                      controller: _scrollController,
                    ),
                  );

          default:
            return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) _purchasesBloc.add(PurchaseslistOnFetch());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
