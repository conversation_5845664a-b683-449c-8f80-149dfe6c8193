import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/ecommerce/purchases_list/bloc/purchases_list_bloc.dart';
import 'package:topbrokers/ecommerce/purchases_list/views/views.dart';


class PurchasesPage extends StatefulWidget {
  PurchasesPage({Key? key}) : super(key: key ?? AgentorKeys.purchasesPage);

  @override
  _PurchasesPageState createState() => _PurchasesPageState();
}

class _PurchasesPageState extends State<PurchasesPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  @mustCallSuper
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(
          title: Text("Compras"),
          centerTitle: true,
          actions: [],
        ),
        body: WidthLimiter(
          child: BlocProvider(
            create: (BuildContext context) => PurchasesBloc.create(context)..add(PurchaseslistOnRefresh()),
            child: PurchaseslistWidget(),
          ),
        ),
      );
}
