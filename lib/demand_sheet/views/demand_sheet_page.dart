import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:topbrokers/agentor_keys.dart';
import 'package:topbrokers/common/widgets/titled_card.dart';
import 'package:topbrokers/common/widgets/width_limiter.dart';
import 'package:topbrokers/demand_edit/models/demand_edit_page_params.dart';
import 'package:topbrokers/matchingslist/views/embedded_demand_matchings.dart';
import 'package:topbrokers/demand_sheet/bloc/demand_sheet_bloc.dart';
import 'package:topbrokers/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:topbrokers/common/helpers.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher_string.dart';

const _c_lang = "es";
const _c_locale = "es_ES";
const _c_lineheight = 1.5;

class DemandSheetPage extends StatefulWidget {
  final String demandId;

  DemandSheetPage({Key? key, required this.demandId}) : super(key: key ?? AgentorKeys.newDemandPage);

  @override
  _DemandSheetPageState createState() => _DemandSheetPageState();
}

class _DemandSheetPageState extends State<DemandSheetPage> {
  //static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Cada una de las imágenes seleccionadas que están siendo subidas
  final List<PickedFile> uploadingImages = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  @mustCallSuper
  void dispose() {
    uploadingImages.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(
          title: Text("Alerta"),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              tooltip: 'Editar',
              onPressed: () {
                Navigator.pushNamed(context, AgentorRoutes.editDemand,
                    arguments: ExistingDemandEditParams(id: widget.demandId));
              },
            ),
          ],
        ),
        body: WidthLimiter(
          child: BlocProvider(
            create: (BuildContext context) =>
                DemandSheetBloc.create(context)..add(DemandSheetOnFetchEvent(demandId: this.widget.demandId)),
            child: _buildBody(context),
          ),
        ),
      );

  Widget _buildBody(BuildContext context) => BlocConsumer<DemandSheetBloc, DemandSheetState>(
        listener: (context, state) {},
        buildWhen: (previous, actual) => true,
        builder: (context, state) {
          switch (state.status) {
            case DemandSheetStatus.failure:
              return const Center(child: Text('Problemas leyendo la información de la alerta'));
            case DemandSheetStatus.success:
              final demand = state.demand;
              if (demand == null)
                return const Center(
                  child: Text('La alerta no existe'),
                );
              else
                return RefreshIndicator(
                  onRefresh: () async {
                    context.bloc<DemandSheetBloc>().add(DemandSheetOnFetchEvent(demandId: widget.demandId));
                  },
                  child: Container(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.fromLTRB(5, 5, 5, 5),
                      child: _buildMainAttributesCard(
                        context,
                        demand,
                        state.definibleFields,
                      ),
                    ),
                  ),
                );

            default:
              return const Center(
                child: CircularProgressIndicator(),
              );
          }
        },
      );

  Widget _buildMainAttributesCard(BuildContext context, DemandDto demand, Map<String, FieldDefDto> fieldsDefinitions) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitledCard(
          Text("Operación", style: textTheme.headline6),
          children: _buildHeadingCard(context, demand, fieldsDefinitions),
        ),
        if (demand.customer.vn != null)
          TitledCard(
            Text("Cliente", style: textTheme.headline6),
            children: _buildCustomerCard(context, demand, fieldsDefinitions),
          ),
        TitledCard(
          Text("Ubicación", style: textTheme.headline6),
          children: _buildAddressCard(context, demand, fieldsDefinitions),
        ),
        TitledCard(
          Text("Características", style: textTheme.headline6),
          children: _buildAttributesCard(context, demand, fieldsDefinitions),
        ),
        TitledCard(Text("Cruces (ofertas)", style: textTheme.headline6), children: [
          EmbeddedDemandMatchingsList(demandId: demand.id.v),
        ]),
      ],
    );
  }

  List<Widget> _buildHeadingCard(BuildContext context, DemandDto demand, Map<String, FieldDefDto> fieldsDefinition) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);
    final boldStyle = textTheme.bodyText1?.copyWith(height: _c_lineheight);
    final sale = demand.sale.vn?.allowed.vn != null ? demand.sale.v : null;
    final rent = demand.rent.vn?.allowed.vn != null ? demand.rent.v : null;
    final currencyISO = demand.currency.vn?.code.vn ?? "EUR";
    return <Widget>[
      Flexible(
        child: RichText(
          softWrap: true,
          text: TextSpan(
            text: "",
            style: textStyle,
            children: [
              if (demand.property.vn?.type.vn?.label.vn != null)
                TextSpan(
                  text: "${demand.property.v.type.v.label.v.localized.capitalize} ",
                  style: boldStyle,
                ),
              if (demand.property.vn?.subtype.vn?.label.vn != null)
                TextSpan(text: "(${demand.property.v.subtype.v!.label.v.localized.capitalize}) ", style: boldStyle),
              if (sale != null) TextSpan(text: "en venta "),
              if (sale != null && (sale.amount.vn ?? 0) > 0)
                TextSpan(
                  text:
                      "por ${sale.amount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)} ",
                ),
              if ((sale?.marketAmount.vn ?? 0) > 0)
                TextSpan(
                  text:
                      "(precio mercado ${sale!.marketAmount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)}) ",
                ),
              if (rent != null && sale != null) TextSpan(text: "y en alquiler "),
              if (rent != null && sale == null) TextSpan(text: "en alquiler "),
              if ((rent?.amount.vn ?? 0) > 0)
                TextSpan(
                    text:
                        "por ${rent!.amount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)} "),
              if ((rent?.marketAmount.vn ?? 0) > 0)
                TextSpan(
                  text:
                      "(precio mercado ${rent!.marketAmount.v!.asSimpleCurrency(locale: _c_locale, name: currencyISO, decimalDigits: 0)}) ",
                ),
            ],
          ),
        ),
      ),
      if (demand.status.vn?.code.vn != null)
        Flexible(
          child: RichText(
            softWrap: true,
            text: TextSpan(
              text: "",
              style: textStyle,
              children: fieldsDefinition[FielddefsSrv.c_demand_status]!
                  .buildEntrySpans(context, getValue: () => demand.status.vn?.code.vn?.enumToString())
                  .toList(),
            ),
          ),
        ),
    ];
  }

  List<Widget> _buildCustomerCard(BuildContext context, DemandDto demand, Map<String, FieldDefDto> fieldsDefinition) {
    final customer = demand.customer.v;
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);

    return <Widget>[
      new Flexible(
        child: RichText(
          softWrap: true,
          text: TextSpan(
            text: customer?.name.vd(""),
            style: textStyle,
            children: [],
          ),
        ),
      ),
      if (customer?.email.vn != null)
        TextButton.icon(
          icon: Icon(Icons.email),
          label: Text(customer!.email.v!),
          onPressed: () async {
            final url = "mailto:${customer.email.v!}";
            if (await canLaunchUrlString(url)) {
              await launchUrlString(url);
            }
          },
        ),
      if (customer?.mobile.vn != null)
        TextButton.icon(
          icon: Icon(Icons.smartphone),
          label: Text(customer!.mobile.v!),
          onPressed: () async {
            final url = "tel:${customer.mobile.v!}";
            if (await canLaunchUrlString(url)) {
              await launchUrlString(url);
            }
          },
        ),
    ];
  }

  List<Widget> _buildAddressCard(BuildContext context, DemandDto demand, Map<String, FieldDefDto> fieldsDefinition) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);
    final addr = demand.property.vn?.address.vn;
    if (addr == null) {
      return [];
    } else {
      return <Widget>[
        if (addr.composedStreetLine.vn != null) Text(addr.composedStreetLine.v, style: textStyle),
        if (addr.detail.vn != null) Text(addr.detail.v as String, style: textStyle),
        if (addr.city.vn != null && addr.postcode.vn != null)
          Text(
            "${addr.postcode.v} ${addr.city.v.label.v.getByLang(_c_lang)}",
            style: textStyle,
          ),
        if (addr.city.vn?.label.vn != null)
          Text(
            "${addr.city.v.label.v.getByLang(_c_lang)}",
            style: textStyle,
          ),
        if (addr.city.vn?.province.vn?.label.vn != null)
          Text(
            addr.city.v.province.v.label.v.getByLang(_c_lang),
            style: textStyle,
          ),
        if (demand.property.vn?.zone.vn != null)
          _buildRichText(
            context,
            fieldsDefinition[FielddefsSrv.c_prop_zone]!
                .buildCustomEntrySpan(
                  context,
                  getStringValue: () => demand.property.vn?.zone.vn?.name.vn ?? "",
                )
                .toList(),
          ),
      ];
    }
  }

  List<Widget> _buildAttributesCard(BuildContext context, DemandDto demand, Map<String, FieldDefDto> fieldsDefinition) {
    final attrs = demand.property.vn?.attributes.vn;
    if (attrs == null) {
      return [];
    } else {
      final textTheme = Theme.of(context).textTheme;
      final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);
      return [
        new Flexible(
          child: RichText(
            softWrap: true,
            text: TextSpan(
              text: "",
              style: textStyle,
              children: addSeparation(
                [
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_status]
                      ?.buildEntrySpans(context, getValue: () => attrs.statusCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_totalSurface]
                      ?.buildEntrySpans(context, getValue: () => attrs.totalSurfaceM2.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_usefulSurface]
                      ?.buildEntrySpans(context, getValue: () => attrs.usefulSurfaceM2.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_solarSurface]
                      ?.buildEntrySpans(context, getValue: () => attrs.solarSurfaceM2.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_constructionYear]
                      ?.buildEntrySpans(context, getValue: () => attrs.constructionYear.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_conservationStatus]
                      ?.buildEntrySpans(context, getValue: () => attrs.conservationStatusCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_individualBedroomsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.individualBedroomsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_doubleBedroomsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.doubleBedroomsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_suiteBedroomsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.suiteBedroomsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_totalBedroomsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.totalBedroomsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_bathroomsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.bathroomsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_toiletsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.toiletsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_builtInCabinetsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.builtInCabinetsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_buddleHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.buddleHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_dinningRoomHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.dinningRoomHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_storageRoomHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.storageRoomHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_balconyHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.balconyHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_terraceHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.terraceHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_externalJoinery]
                      ?.buildEntrySpans(context, getValue: () => attrs.externalJoineryCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_ground]
                      ?.buildEntrySpans(context, getValue: () => attrs.groundCodes.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_waterSupplyHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.waterSupplyHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_powerSupplyHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.powerSupplyHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_gasSupplyHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.gasSupplyHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_airConditioning]
                      ?.buildEntrySpans(context, getValue: () => attrs.airConditioningCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_heating]
                      ?.buildEntrySpans(context, getValue: () => attrs.heatingCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_fireplaceHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.fireplaceHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_intercomHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.intercomHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_reinforcedDoorHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.reinforcedDoorHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_alarmSystemHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.alarmSystemHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_elevatorHas]
                      ?.buildEntrySpans(context, getValue: () => attrs.elevatorHas.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_handicappedAccessibleIs]
                      ?.buildEntrySpans(context, getValue: () => attrs.handicappedAccessibleIs.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_furnishedIs]
                      ?.buildEntrySpans(context, getValue: () => attrs.furnishedIs.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_garden]
                      ?.buildEntrySpans(context, getValue: () => attrs.gardenCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_outsideArea]
                      ?.buildEntrySpans(context, getValue: () => attrs.outsideAreaCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_swimmingPool]
                      ?.buildEntrySpans(context, getValue: () => attrs.swimmingPoolCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_parkingPlacesCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.parkingPlacesCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_optionalParkingIs]
                      ?.buildEntrySpans(context, getValue: () => attrs.optionalParkingIs.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_facade]
                      ?.buildEntrySpans(context, getValue: () => attrs.facadeCodes.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_orientation]
                      ?.buildEntrySpans(context, getValue: () => attrs.orientationCodes.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_sunnyIs]
                      ?.buildEntrySpans(context, getValue: () => attrs.sunnyIs.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_communityFeesAmount]
                      ?.buildEntrySpans(context, getValue: () => attrs.communityFeesAmount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_neighborsPerFloorCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.neighborsPerFloorCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_buildingFloorsCount]
                      ?.buildEntrySpans(context, getValue: () => attrs.buildingFloorsCount.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_energyCertificate]
                      ?.buildEntrySpans(context, getValue: () => attrs.energyCertificateCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_consumptionLevel]
                      ?.buildEntrySpans(context, getValue: () => attrs.consumptionLevelCode.vn),
                  fieldsDefinition[FielddefsSrv.c_prop_attrs_emissionLevel]
                      ?.buildEntrySpans(context, getValue: () => attrs.emissionLevelCode.vn),
                ],
              ),
            ),
          ),
        ),
      ];
    }
  }

  List<InlineSpan> addSeparation(List<Iterable<InlineSpan>?> texts) {
    return texts.fold(
      <InlineSpan>[],
      (allSpans, spans) {
        if (spans == null)
          return allSpans;
        else
          return allSpans
            ..addAll([
              if (allSpans.length != 0 && spans.length != 0) TextSpan(text: "\n"),
              if (spans.length != 0) TextSpan(text: "- "),
            ])
            ..addAll(spans);
      },
    );
  }

  Flexible _buildRichText(context, List<TextSpan> lines) {
    final textTheme = Theme.of(context).textTheme;
    final textStyle = textTheme.bodyText2?.copyWith(height: _c_lineheight);
    return new Flexible(
      child: RichText(
        softWrap: true,
        text: TextSpan(
          text: "",
          style: textStyle,
          children: lines,
        ),
      ),
    );
  }
}
