import 'dart:async';

import 'package:topbrokers/channels/models_channel/models_channel_bloc.dart';
import 'package:topbrokers/common/app_models_ns.dart';
import 'package:equatable/equatable.dart';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

part 'demand_sheet_event.dart';
part 'demand_sheet_state.dart';

class DemandSheetBloc extends Bloc<DemandSheetEvent, DemandSheetState> {
  final AppModelsNS appModels;

  late StreamSubscription<ModelsChannelState> _demandsChannelSubscription;

  DemandSheetBloc({required this.appModels, required ModelsChannelBloc modelsChannel}) : super(DemandSheetState()) {
    this._demandsChannelSubscription = modelsChannel.stream.listen((ModelsChannelState state) {
      if (state is ModelsChannelUpdatedState<DemandDto>) {
        final demandId = this.state.demand?.id.vn;
        if (demandId != null && state.entities.indexWhere((d) => demandId == d.id.vn) != -1) {
          this.add(DemandSheetOnFetchEvent(demandId: demandId));
        }
      }
    });
  }

  factory DemandSheetBloc.create(BuildContext context) {
    return DemandSheetBloc(
      appModels: Provider.of<AppModelsNS>(context, listen: false),
      modelsChannel: BlocProvider.of<ModelsChannelBloc>(context),
    );
  }

  @override
  @mustCallSuper
  Future<void> close() async {
    try {
      await _demandsChannelSubscription.cancel();
    } on Exception {}
    return super.close();
  }

  @override
  Stream<Transition<DemandSheetEvent, DemandSheetState>> transformEvents(
    Stream<DemandSheetEvent> events,
    TransitionFunction<DemandSheetEvent, DemandSheetState> transitionFn,
  ) {
    return super.transformEvents(
      events.debounceTime(const Duration(milliseconds: 100)),
      transitionFn,
    );
  }

  @override
  Stream<DemandSheetState> mapEventToState(DemandSheetEvent event) async* {
    if (event is DemandSheetOnFetchEvent) {
      yield* _mapOnFetchToState(state, event);
    }
  }

  Stream<DemandSheetState> _mapOnFetchToState(DemandSheetState state, DemandSheetOnFetchEvent event) async* {
    try {
      final demand = await appModels.readDemand(event.demandId);
      final definibleFields = await _readDefinibleFields();
      yield state.copyWith(
        status: DemandSheetStatus.success,
        demand: demand,
        definibleFields: definibleFields,
      );
    } on Exception {
      yield state.copyWith(status: DemandSheetStatus.failure);
    }
  }

  Future<Map<String, FieldDefDto>> _readDefinibleFields() async {
    final result = await appModels.listDemandFielddefs();
    return Map.fromEntries(result.map((fieldDef) => MapEntry(fieldDef.code, fieldDef)));
  }
}
