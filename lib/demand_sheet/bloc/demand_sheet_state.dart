part of 'demand_sheet_bloc.dart';

enum DemandSheetStatus { initial, success, failure }

class DemandSheetState extends Equatable {
  const DemandSheetState({
    this.status = DemandSheetStatus.initial,
    this.demand,
    this.definibleFields = const {},
  });

  // Actual status of the screen
  final DemandSheetStatus status;
  // Wich filter are we applying to the oportunities of kind "oofers"
  final DemandDto? demand;
  //
  final Map<String, FieldDefDto> definibleFields;

  DemandSheetState copyWith({
    DemandSheetStatus? status,
    DemandDto? demand,
    Map<String, FieldDefDto>? definibleFields,
    List<PropertymediaDto>? demandPropertyMedias,
  }) {
    return DemandSheetState(
      status: status ?? this.status,
      demand: demand ?? this.demand,
      definibleFields: definibleFields ?? this.definibleFields,
    );
  }

  @override
  List<Object> get props => [status, demand ?? ""];
}
