part of 'demand_sheet_bloc.dart';

abstract class DemandSheetEvent extends Equatable {
  const DemandSheetEvent();

  @override
  List<Object> get props => [];
}

class DemandSheetOnFetchEvent extends DemandSheetEvent {
  final String demandId;
  DemandSheetOnFetchEvent({required this.demandId});
}
class DemandSheetOnFetchedEvent extends DemandSheetEvent {
  final DemandDto demand;
  DemandSheetOnFetchedEvent({required this.demand});
}

class DemandSheetOnRefreshEvent extends DemandSheetEvent {}
