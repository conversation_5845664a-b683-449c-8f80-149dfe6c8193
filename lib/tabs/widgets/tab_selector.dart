import 'package:flutter/material.dart';
import 'package:topbrokers/custom_app_icons.dart';
import 'package:topbrokers/tabs/models/app_tab.dart';
import 'package:topbrokers/common/helpers.dart';

class TabSelector extends StatelessWidget {
  final AppTab activeTab;
  final Function(AppTab) onTabSelected;

  TabSelector({
    Key? key,
    required this.activeTab,
    required this.onTabSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final shownTabs = [AppTab.cloud, AppTab.favourites, AppTab.demands, AppTab.offers, AppTab.actions, AppTab.contacts];
    return BottomNavigationBar(
      //selectedItemColor: Theme.of(context).bottomNavigationBarTheme.selectedItemColor ?? Colors.red,
      //key: ArchSampleKeys.tabs,
      currentIndex: shownTabs.indexOf(activeTab),
      onTap: (index) => onTabSelected(shownTabs[index]),
      items: shownTabs.map((tab) {
        return BottomNavigationBarItem(
          icon: Icon(_tabToIcon(tab), size: 24),
          tooltip: _tabToLabel(context, tab),
          label: _tabToLabel(context, tab),
        );
      }).toList(),
      landscapeLayout: BottomNavigationBarLandscapeLayout.linear,
      fixedColor: Colors.black,
      type: BottomNavigationBarType.fixed,
      showSelectedLabels: false,
      showUnselectedLabels: false,
      iconSize: 48.0,
      selectedFontSize: 12.0,
      unselectedFontSize: 12.0,
    );
  }

  IconData _tabToIcon(AppTab tab) {
    switch (tab) {
      case AppTab.cloud:
        return Icons.search;
      case AppTab.favourites:
        return Icons.star_border_outlined;
      case AppTab.demands:
        return CustomAppIcons.demand;
      case AppTab.offers:
        return CustomAppIcons.offer;
      case AppTab.contacts:
        return Icons.person_rounded;
      default:
        return CustomAppIcons.action;
    }
  }

  String _tabToLabel(BuildContext context, AppTab tab) {
    switch (tab) {
      case AppTab.cloud:
        return context.apploc.home_tabs_search;
      case AppTab.favourites:
        return context.apploc.home_tabs_favorites;
      case AppTab.demands:
        return context.apploc.home_tabs_alerts;
      case AppTab.offers:
        return context.apploc.home_tabs_offers;
      case AppTab.contacts:
        return context.apploc.home_tabs_contacts;
      default:
        return context.apploc.home_tabs_actions;
    }
  }
}
