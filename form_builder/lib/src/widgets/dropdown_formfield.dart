import 'package:flutter/material.dart';

///
/// Custom [FormFieldState] en el que almacenamos [fieldFocusNode] necesario para solicitar el foco
/// cuando el usuario hace click en el dropdown.
/// De otra manera, el foco continuaría en otro campo y al seleccionar/escapar del dropdown se volvería a
/// ese elemento.
///
class _DropDownFormFieldState<T> extends FormFieldState<T> {
  late FocusNode fieldFocusNode;

  @override
  void initState() {
    super.initState();
    fieldFocusNode = FocusNode();
  }

  @override
  void dispose() {
    fieldFocusNode.dispose();
    super.dispose();
  }
}

class DropDownFormField<T> extends FormField<T> {
  final String hintText;
  final bool required;
  final String errorText;
  final dynamic value;
  final List dataSource;
  final String textField;
  final String valueField;
  final Function? onChanged;
  final InputDecoration decoration;

  @override
  FormFieldState<T> createState() {
    return _DropDownFormFieldState<T>();
  }

  DropDownFormField({
    FormFieldSetter<T>? onSaved,
    FormFieldValidator<T>? validator,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    this.hintText = 'Select one option',
    this.required = false,
    this.errorText = 'Please select one option',
    this.value,
    this.dataSource = const [],
    required this.textField,
    required this.valueField,
    this.onChanged,
    this.decoration = const InputDecoration(),
  }) : super(
          onSaved: onSaved,
          validator: validator,
          autovalidateMode: autovalidateMode,
          initialValue: value == '' ? null : value,
          builder: (FormFieldState<T> state) {
            _DropDownFormFieldState<T> ddstate = state as _DropDownFormFieldState<T>;
            return Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  InputDecorator(
                    decoration: !state.hasError
                        ? decoration
                        : decoration.copyWith(
                            labelStyle: decoration.errorStyle ??
                                decoration.labelStyle?.copyWith(
                                  color: Colors.redAccent.shade700,
                                ),
                            counterStyle: decoration.errorStyle ?? decoration.counterStyle,
                          ),
                    child: Row(
                      children: [
                        Expanded(
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<T>(
                              focusNode: ddstate.fieldFocusNode,
                              isDense: true,
                              hint: Text(hintText),
                              value: value == '' ? null : value,
                              onChanged: (T? newValue) {
                                state.didChange(newValue);
                                if (onChanged != null) onChanged(newValue);
                                state.validate();
                              },
                              onTap: () {
                                ddstate.fieldFocusNode.requestFocus();
                              },
                              items: dataSource.map((item) {
                                return DropdownMenuItem<T>(
                                  value: item[valueField],
                                  child: Text(
                                    item[textField],
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                        if (!required && value != null)
                          InkWell(
                            child: Icon(Icons.delete),
                            onTap: () async {
                              state.didChange(null);
                              if(onChanged!=null) onChanged(null);
                              state.validate();
                            },
                          ),
                      ],
                    ),
                  ),
                  if (state.hasError) SizedBox(height: 5.0),
                  if (state.hasError)
                    Text(
                      state.errorText ?? "",
                      style: TextStyle(
                        color: decoration.errorStyle?.color ?? Colors.redAccent.shade700,
                        fontSize: 12.0,
                      ),
                    ),
                  SizedBox(height: 5.0),
                ],
              ),
            );
          },
        );
}
