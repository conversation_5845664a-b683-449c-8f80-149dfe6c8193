import 'package:flutter/material.dart';

///
/// Custom [FormFieldState] en el que almacenamos [fieldFocusNode] necesario para solicitar el foco
/// cuando el usuario hace click en el dropdown.
/// De otra manera, el foco continuaría en otro campo y al seleccionar/escapar del dropdown se volvería a
/// ese elemento.
///
class _CustomSelectFormFieldState<T> extends FormFieldState<T> {
  FocusNode? fieldFocusNode;

  @override
  void initState() {
    super.initState();
    fieldFocusNode = FocusNode();
  }

  @override
  void dispose() {
    fieldFocusNode?.dispose();
    super.dispose();
  }
}

///
/// Muestra el campo usando un "flatbutton" que al ser apretado despliega un dialogo de búsqueda.
///
class CustomSelectFormField<T> extends FormField<T> {
  final String hintText;
  final bool required;
  final String errorText;
  final String Function(T? value) valueToString;
  final T? value;
  final Function(T? value)? onChanged;
  final Future<T?> Function() onSelect;
  final InputDecoration decoration;
  final Widget? rightWidget;

  @override
  FormFieldState<T> createState() {
    return _CustomSelectFormFieldState<T>();
  }

  CustomSelectFormField({
    FormFieldSetter<T>? onSaved,
    FormFieldValidator<T>? validator,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    required this.valueToString,
    this.hintText = 'Click para seleccionar',
    this.required = false,
    this.errorText = 'Por favor, seleccione una opción',
    this.value,
    this.onChanged,
    required this.onSelect,
    this.decoration = const InputDecoration(),
    this.rightWidget,
  }) : super(
          onSaved: onSaved,
          validator: validator,
          autovalidateMode: autovalidateMode,
          initialValue: value,
          builder: (FormFieldState<T> state) {
            final sffState = state as _CustomSelectFormFieldState<T>;
            final theme = Theme.of(state.context);
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                InputDecorator(
                  decoration: decoration,
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: theme.buttonTheme.height * 0.9,
                          child: TextButton(
                            focusNode: sffState.fieldFocusNode,
                            //padding: const EdgeInsets.fromLTRB(0, 0, 0, 5),
                            child: Row(children: [
                              Expanded(child: Text(valueToString(value), style: theme.textTheme.titleMedium)),
                              Icon(Icons.search_rounded),
                            ]),
                            //height: theme.buttonTheme.height * 0.8,
                            onPressed: () async {
                              // Traemos el foco al control (en caso contrario el formulario se situaría en el control previamente enfocado cuando se cierre diálogo o se haga "tab")
                              sffState.fieldFocusNode?.requestFocus();

                              final T? result = await onSelect();
                              if (result != null) {
                                state.didChange(result);
                                if(onChanged!=null) onChanged(result);
                                state.validate();
                              }
                            },
                          ),
                        ),
                      ),
                      if (!required && value != null)
                        InkWell(
                          child: Icon(Icons.delete),
                          onTap: () async {
                            state.didChange(null);
                            if(onChanged!=null) onChanged(null);
                            state.validate();
                          },
                        ),
                      if (rightWidget != null) rightWidget,
                    ],
                  ),
                ),
                SizedBox(height: state.hasError ? 5.0 : 0.0),
                Text(
                  state.hasError ? state.errorText ?? "" : '',
                  style: TextStyle(color: Colors.redAccent.shade700, fontSize: state.hasError ? 12.0 : 0.0),
                ),
                SizedBox(height: 5.0),
              ],
            );
          },
        );
}
