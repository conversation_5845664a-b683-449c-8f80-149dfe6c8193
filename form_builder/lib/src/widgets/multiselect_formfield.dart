import 'package:flutter/material.dart';
import 'package:form_builder/src/form_entries/form_entries.dart';

///
/// Custom [FormFieldState] en el que almacenamos [fieldFocusNode] necesario para solicitar el foco
/// cuando el usuario hace click en el dropdown.
/// De otra manera, el foco continuaría en otro campo y al seleccionar/escapar del dropdown se volvería a
/// ese elemento.
///
class _MultiselectFormFieldState<T> extends FormFieldState<T> {
  late FocusNode fieldFocusNode;

  @override
  void initState() {
    super.initState();
    fieldFocusNode = FocusNode();
  }

  @override
  void dispose() {
    fieldFocusNode.dispose();
    super.dispose();
  }
}

class MultiselectFormField extends FormField<List<String>> {
  final String hintText;
  final bool required;
  final String errorText;
  final List<String>? value;
  final List<SelectOption<String>> dataSource;
  final Function(List<String>?)? onChanged;
  final InputDecoration decoration;

  @override
  FormFieldState<List<String>> createState() {
    return _MultiselectFormFieldState();
  }

  MultiselectFormField({
    FormFieldSetter<List<String>>? onSaved,
    FormFieldValidator<List<String>>? validator,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    this.hintText = 'Select one or more options',
    this.required = false,
    this.errorText = 'Please select, at least, one option',
    this.value,
    required this.dataSource,
    this.onChanged,
    this.decoration = const InputDecoration(),
  }) : super(
          onSaved: onSaved,
          validator: validator,
          autovalidateMode: autovalidateMode,
          initialValue: value ?? <String>[],
          builder: (FormFieldState<List<String>> state) => Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InputDecorator(
                  decoration: !state.hasError
                      ? decoration
                      : decoration.copyWith(
                          labelStyle: decoration.errorStyle ??
                              decoration.labelStyle?.copyWith(
                                color: Colors.redAccent.shade700,
                              ),
                          counterStyle: decoration.errorStyle ?? decoration.counterStyle,
                        ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: <Widget>[] +
                              dataSource
                                  .map(
                                    (o) => CheckboxListTile(
                                      //dense: true,
                                      //contentPadding: EdgeInsets.fromLTRB(5, 0, 0, 0),
                                      title: Text(o.label), //, style: theme.textTheme.bodyText2),
                                      value: (value ?? []).contains(o.value),
                                      onChanged: (bool? checked) {
                                        var selected = value ?? <String>[];
                                        if ((checked ?? false) && !selected.contains(o.value)) {
                                          final newValues = selected + [o.value];
                                          state.didChange(newValues);
                                          if (onChanged != null) onChanged(newValues);
                                        } else if (!(checked ?? false) && selected.contains(o.value)) {
                                          final newValues = selected.where((element) => element != o.value).toList();
                                          state.didChange(newValues);
                                          if (onChanged != null) onChanged(newValues);
                                        }
                                      },
                                    ),
                                  )
                                  .toList() +
                              <Widget>[
                                if (state.hasError && state.errorText != null) SizedBox(height: 5.0),
                                if (state.hasError && state.errorText != null)
                                  Text(
                                    state.errorText as String,
                                    style: TextStyle(
                                      color: decoration.errorStyle?.color ?? Colors.redAccent.shade700,
                                      fontSize: 12.0,
                                    ),
                                  ),
                                SizedBox(height: 5.0),
                              ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
}
