import 'package:flutter/material.dart';
import 'package:form_builder/src/dialogs/agentor_select_dialog.dart';

///
/// Custom [FormFieldState] en el que almacenamos [fieldFocusNode] necesario para solicitar el foco
/// cuando el usuario hace click en el dropdown.
/// De otra manera, el foco continuaría en otro campo y al seleccionar/escapar del dropdown se volvería a
/// ese elemento.
///
class _SearchFormFieldState<T> extends FormFieldState<T> {
  late FocusNode fieldFocusNode;

  @override
  void initState() {
    super.initState();
    fieldFocusNode = FocusNode();
  }

  @override
  void dispose() {
    fieldFocusNode.dispose();
    super.dispose();
  }
}

///
/// Muestra el campo usando un "flatbutton" que al ser apretado despliega un dialogo de búsqueda.
///
class SearchFormField<T> extends FormField<T> {
  final String hintText;
  final String searchHint;
  final bool required;
  final String errorText;
  final String Function(T? value) valueToString;
  final T? value;
  final Function(T? value) onChanged;
  final Future<List<T>> Function(String search) onFind;
  final Future<T?> Function()? onAdd;

  final Widget Function(BuildContext context, T item, bool isSelected) itemBuilder;
  final InputDecoration decoration;
  final Widget? rightWidget;

  @override
  FormFieldState<T> createState() {
    return _SearchFormFieldState<T>();
  }

  SearchFormField({
    FormFieldSetter<T>? onSaved,
    FormFieldValidator<T>? validator,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    required this.valueToString,
    required this.onFind,
    required this.itemBuilder,
    this.searchHint = "Busca aquí",
    this.hintText = 'Click para buscar uno',
    this.required = false,
    this.errorText = 'Por favor, seleccione una opción',
    this.value,
    required this.onChanged,
    this.onAdd,
    this.decoration = const InputDecoration(),
    this.rightWidget,
  }) : super(
          onSaved: onSaved,
          validator: validator,
          autovalidateMode: autovalidateMode,
          initialValue: value,
          builder: (FormFieldState<T> state) {
            final sffState = state as _SearchFormFieldState<T>;
            final theme = Theme.of(state.context);
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                InputDecorator(
                  decoration: decoration,
                  child: IntrinsicHeight(
                    child: Container(
                      height: theme.buttonTheme.height * 0.9,
                      child: TextButton(
                        focusNode: sffState.fieldFocusNode,
                        child: Row(children: [
                          Expanded(child: Text(valueToString(value), style: theme.textTheme.titleMedium)),
                          Icon(Icons.search_rounded),
                          if (!required && value != null)
                            InkWell(
                              child: Icon(Icons.delete),
                              onTap: () async {
                                state.didChange(null);
                                onChanged(null);
                                state.validate();
                              },
                            ),
                          if (onAdd != null && value == null)
                            InkWell(
                              child: Icon(Icons.add),
                              onTap: () async {
                                final created = await onAdd();
                                if (created != null) {
                                  state.didChange(created);
                                  onChanged(created);
                                  state.validate();
                                }
                              },
                            ),
                          if (rightWidget != null) rightWidget,
                        ]),

                        //  padding: const EdgeInsets.fromLTRB(0, 0, 0, 5),
                        //  height: theme.buttonTheme.height * 0.8,
                        onPressed: () {
                          // Traemos el foco al control (en caso contrario el formulario se situaría en el control previamente enfocado cuando se cierre diálogo o se haga "tab")
                          sffState.fieldFocusNode.requestFocus();

                          AgentorSelectDialog.showModal<T>(
                            state.context,
                            label: decoration.labelText ?? "Seleccione uno",
                            onFind: onFind, // ?? _onEmptyFind,
                            selectedValue: value,
                            autofocus: true,
                            emptyBuilder: (context) => Column(children: [
                              Icon(Icons.warning_amber_rounded, color: Colors.black45),
                              Text("No se han encontrado resultados", style: TextStyle(color: Colors.black45))
                            ]),
                            itemBuilder: itemBuilder,
                            onChange: (selected) {
                              state.didChange(selected);
                              onChanged(selected);
                              state.validate();
                            },
                            searchHint: searchHint,
                          );
                        },
                      ),
                    ),
                  ),
                ),
                if (state.hasError) SizedBox(height: 5.0),
                if (state.hasError && state.errorText != null)
                  Text(
                    state.errorText as String,
                    style: TextStyle(color: Colors.redAccent.shade700, fontSize: 12.0),
                  ),
                SizedBox(height: 5.0),
              ],
            );
          },
        );
}
