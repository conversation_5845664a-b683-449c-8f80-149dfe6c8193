import 'package:flutter/material.dart';
import 'package:form_builder/src/form_entries/common/consts.dart';

InputDecoration buildInputDecoration(
  BuildContext context,
  String? text, {
  bool? isM2 = false,
  bool? isCurrency = false,
  bool? isCount = false,
  bool? isPercent = false,
  String? sufixText,
  bool? isMultiline = false,
}) {
  return InputDecoration(
    floatingLabelBehavior: FloatingLabelBehavior.always,
    labelStyle: TextStyle(fontSize: 20),
    labelText: text ?? "",
    isDense: false,
    border: C_inputBorder,
    alignLabelWithHint: true,
    contentPadding: isMultiline ?? false ? null : const EdgeInsets.fromLTRB(0, 0, 0, 0),
    prefix: (isM2 ?? false)
        ? Text("m² ", style: Theme.of(context).textTheme.bodySmall)
        : (isCount ?? false)
            ? Text("# ", style: Theme.of(context).textTheme.bodySmall)
            : (isCurrency ?? false)
                ? Text("€ ", style: Theme.of(context).textTheme.bodySmall)
                : (isPercent ?? false)
                    ? Text("% ", style: Theme.of(context).textTheme.bodySmall)
                    : null,
    suffixStyle: Theme.of(context).textTheme.bodySmall,
    suffixText: sufixText,
  );
}
