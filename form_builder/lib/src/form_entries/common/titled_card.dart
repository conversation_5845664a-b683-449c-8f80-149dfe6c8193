import 'package:flutter/material.dart';

const _c_card_padding = const EdgeInsets.fromLTRB(10, 10, 10, 10);
const _c_card_margin = const EdgeInsets.fromLTRB(5, 5, 5, 5);

class TitledCard extends StatelessWidget {
  final Widget? heading;
  final List<Widget> children;
  final List<Widget> barActions;
  final EdgeInsetsGeometry margin;
  final EdgeInsetsGeometry padding;

  TitledCard(
    this.heading, {
    this.children = const <Widget>[],
    this.barActions = const [],
    this.margin = _c_card_margin,
    this.padding = _c_card_padding,
  }) : super();

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 0,
      borderOnForeground: false,
      margin: margin,
      child: Container(
        padding: padding,
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (heading != null || this.barActions.length != 0)
                    Container(
                      decoration: BoxDecoration(
                        border: Border(bottom: BorderSide(color: Colors.grey)),
                      ),
                      margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                      child: Row(
                        children: [
                          if (heading != null) Expanded(child: heading as Widget),
                          if (barActions.length != 0)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              children: barActions,
                            ),
                        ],
                      ),
                    )
                ]..addAll(children),
              ),
            )
          ],
        ),
      ),
    );
  }
}
