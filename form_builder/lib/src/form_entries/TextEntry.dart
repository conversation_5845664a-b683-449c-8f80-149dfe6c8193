import 'FormEntry.dart';
import 'package:flutter/material.dart';

class TextEntry extends FormEntry {
  final String label;
  final Widget? sufix;
  TextEntry({
    required this.label,
    this.sufix,
  });

  @override
  Widget build(BuildContext context) {
    return _buildFieldGroup(context, this);
  }
}

Widget _buildFieldGroup(BuildContext context, TextEntry gSpec) {
  final theme = Theme.of(context);
  if (gSpec.sufix != null) {
    return Padding(
      padding: EdgeInsets.fromLTRB(1, 12, 1, 12),
      child: Row(
        children: [
          Expanded(child: Text(gSpec.label, style: theme.textTheme.bodyMedium)),
          gSpec.sufix as Widget,
        ],
      ),
    );
  } else {
    return Text(gSpec.label, style: theme.textTheme.titleMedium);
  }
}
