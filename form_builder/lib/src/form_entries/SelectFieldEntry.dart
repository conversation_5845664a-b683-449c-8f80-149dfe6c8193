import 'package:form_builder/src/form_entries/common/utils.dart';
import 'package:form_builder/src/widgets/multiselect_formfield.dart';

import '../widgets/dropdown_formfield.dart';
import 'FieldEntry.dart';
import 'SelectOption.dart';
import 'package:flutter/material.dart';

///
/// 1 value selector
///
class SelectFieldEntry<T> extends FieldEntry<T> {
  final List<SelectOption<T>> options;
  final String? hintText;

  SelectFieldEntry({
    String? name,
    String? label,
    this.options = const [],
    required T? Function() getValue,
    required void Function(T?) setValue,
    bool isRequired = false,
    this.hintText,
  }) : super(name: name, label: label, getValue: getValue, setValue: setValue, isRequired: isRequired);

  @override
  Widget build(BuildContext context) {
    return _buildSelectField(context, this);
  }
}

///
/// N values selector
/// getValue/setValue will use a [List<T>] parameter.  Only T=String is allowed
///
class MultiSelectFieldEntry<T> extends FieldEntry<List<T>> {
  final List<SelectOption<T>> options;

  MultiSelectFieldEntry({
    String? name,
    String? label,
    this.options = const [],
    required List<T>? Function() getValue,
    required void Function(List<T>?) setValue,
    bool isRequired = false,
  }) : super(name: name, label: label, getValue: getValue, setValue: setValue, isRequired: isRequired);

  @override
  Widget build(BuildContext context) {
    if (this is MultiSelectFieldEntry<String>) {
      return _buildMultiSelectStringField(context, this as MultiSelectFieldEntry<String>);
    } else {
      throw throw Exception("MultiSelectFieldSpec.build Unsuported T");
    }
  }
}

Widget _buildMultiSelectStringField(BuildContext context, MultiSelectFieldEntry<String> sSpec) {
  return MultiselectFormField(
    decoration: buildInputDecoration(context, sSpec.label).copyWith(
      contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
    ),
    dataSource: sSpec.options,
    value: sSpec.getValue(),
    required: sSpec.isRequired,
    autovalidateMode: AutovalidateMode.onUserInteraction,
    validator: (List<String>? value) {
      if (sSpec.isRequired && (value == null || value.length == 0))
        return "Debe seleccionar, al menos,  un valor";
      else
        return null;
    },
    onChanged: (List<String>? value) {
      sSpec.setValue(value);
    },
  );
}

Widget _buildSelectField<T>(BuildContext context, SelectFieldEntry<T> sSpec) {
  //final theme = Theme.of(context);
  final selectedValue = sSpec.getValue();
  final dataSource = sSpec.options.map((option) => {"label": option.label, "value": option.value}).toList();
  final isRequired = sSpec.isRequired;

  return DropDownFormField<T>(
    decoration: buildInputDecoration(context, sSpec.label).copyWith(
      contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
    ),
    hintText: sSpec.hintText ?? "Seleccione uno", //sSpec.label ?? "",
    dataSource: dataSource,
    textField: 'label',
    valueField: 'value',
    value: selectedValue,
    required: sSpec.isRequired,
    validator: (T? value) {
      if (value == null && isRequired)
        return "Debe seleccionar un valor";
      else
        return null;
    },
    onChanged: (T? value) {
      sSpec.setValue(value);
    },
    // Esto está causando problemas con los campos que dependen de este valor
    // (Ej: campo ammount de la oferta cuando se elige "sale"... el valor del campo es "0")
    //onSaved: (T value) {
    //  sSpec.setValue(value);
    //},
  );
}
