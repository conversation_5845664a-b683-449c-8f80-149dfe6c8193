import 'package:flutter/material.dart';
import 'package:form_builder/src/form_entries/common/utils.dart';
import 'package:form_builder/src/widgets/datetime_formfield.dart';

import '../formatters.dart';
import '../validators.dart';
import 'FieldEntry.dart';

class SimpleFieldEntry<T> extends FieldEntry<T> {
  final T? min, max;
  final int? minLength, maxLength;
  final bool? onlyDigits;
  final bool isEmail;
  final bool isPhoneNumber;
  final bool isM2;
  final bool isCurrency;
  final bool isPercent;
  final bool isCount;
  final bool isDateOnly;
  final bool readOnly;
  final bool obscureText;
  final bool isMultiline;

  SimpleFieldEntry({
    name,
    String? label,
    this.min,
    this.max,
    this.minLength,
    this.maxLength,
    this.onlyDigits = false,
    this.readOnly = false,
    isRequired = false,
    required T? Function() getValue,
    required void Function(T?) setValue,
    String Function()? getValueComment,
    this.isEmail = false,
    this.isPhoneNumber = false,
    this.isM2 = false,
    this.isCurrency = false,
    this.isPercent = false,
    this.isCount = false,
    this.isDateOnly = false,
    this.obscureText = false,
    this.isMultiline = false,
  }) : super(
          name: name,
          label: label,
          isRequired: isRequired,
          getValue: getValue,
          setValue: setValue,
          getValueComment: getValueComment,
        ) {
    assert(!this.isDateOnly || (this.isDateOnly && this is SimpleFieldEntry<DateTime>),
        "Only FieldSpec<DateTime> accepts isDateOnly:true");
    assert(!this.isEmail || (this.isEmail && this is SimpleFieldEntry<String>),
        "Only FieldSpec<String> accepts isEmail:true");
    assert(!this.isPhoneNumber || (this.isPhoneNumber && this is SimpleFieldEntry<String>),
        "Only FieldSpec<String> accepts isPhoneNumber:true");
    assert(!(this.isEmail && this.isPhoneNumber), "isEmail and isPhoneNumber are mutually exclusive");
    assert(!this.isM2 || (this.isM2 && this is SimpleFieldEntry<double>), "Only FieldSpec<double> accepts isM2:true");
    assert(!this.isCurrency || (this.isCurrency && this is SimpleFieldEntry<double>),
        "Only FieldSpec<double> accepts isCurrency:true");
    assert(!this.isPercent || (this.isPercent && this is SimpleFieldEntry<double>),
        "Only FieldSpec<double> accepts isPercent:true");
    assert(
        !this.isCount || (this.isCount && this is SimpleFieldEntry<int>), "Only FieldSpec<int> accepts isCount:true");
    assert(!this.isMultiline || (this.isMultiline && this is SimpleFieldEntry<String>),
        "Only FieldSpec<String> accepts isMultiline:true");
  }
  @override
  Widget build(BuildContext context) {
    if (this is SimpleFieldEntry<double>) {
      return _buildDoubleField(context, this as SimpleFieldEntry<double>);
    } else if (this is SimpleFieldEntry<int>) {
      return _buildIntField(context, this as SimpleFieldEntry<int>);
    } else if (this is SimpleFieldEntry<bool>) {
      return _buildBoolField(context, this as SimpleFieldEntry<bool>);
    } else if (this is SimpleFieldEntry<String>) {
      return _buildStringField(context, this as SimpleFieldEntry<String>);
    } else if (this is SimpleFieldEntry<DateTime>) {
      return _buildDatetimeField(context, this as SimpleFieldEntry<DateTime>);
    } else {
      throw throw Exception("FieldSpec.build Unsuported T");
    }
  }
}

Widget _buildIntField(BuildContext context, SimpleFieldEntry<int> iSpec) {
  if (iSpec.readOnly) {
    final TextEditingController _textController = new TextEditingController();
    final String initialValue = '${iSpec.getValue() ?? ""}';
    _textController.text = initialValue;
    return TextFormField(
      controller: _textController,
      validator: MyTextInputValidators.isInteger(min: iSpec.min, max: iSpec.max, allowEmpty: !iSpec.isRequired),
      decoration: buildInputDecoration(
        context,
        iSpec.label,
        isM2: iSpec.isM2,
        isCount: iSpec.isCount,
        sufixText: iSpec.getValueComment != null ? iSpec.getValueComment!() : null,
      ), //border: Border(bottom: BorderSide(color: Colors.black.withAlpha(50))),
      keyboardType: TextInputType.numberWithOptions(decimal: false, signed: false),
      inputFormatters: [
        if (iSpec.isRequired) MyTextInputFormatters.positiveIntOrEmpty else MyTextInputFormatters.positiveInt
      ],
      autovalidateMode: AutovalidateMode.onUserInteraction,
      readOnly: true,
      enabled: false,
    );
  } else {
    return TextFormField(
      initialValue: '${iSpec.getValue() ?? ""}',
      validator: MyTextInputValidators.isInteger(min: iSpec.min, max: iSpec.max, allowEmpty: !iSpec.isRequired),
      decoration: buildInputDecoration(
        context,
        iSpec.label,
        isM2: iSpec.isM2,
        isCount: iSpec.isCount,
        sufixText: iSpec.getValueComment != null ? iSpec.getValueComment!() : null,
      ), //border: Border(bottom: BorderSide(color: Colors.black.withAlpha(50))),
      keyboardType: TextInputType.numberWithOptions(decimal: false, signed: false),
      inputFormatters: [
        if (iSpec.isRequired) MyTextInputFormatters.positiveIntOrEmpty else MyTextInputFormatters.positiveInt
      ],
      autovalidateMode: AutovalidateMode.onUserInteraction,
      readOnly: false,
      enabled: true,
      onChanged: (String v) {
        final value = int.tryParse(v);
        iSpec.setValue(value);
      },
    );
  }
}

Widget _buildDoubleField(BuildContext context, SimpleFieldEntry<double> dSpec) {
  var validator = MyTextInputValidators.isNumber(min: dSpec.min, max: dSpec.max, allowEmpty: !dSpec.isRequired);
  return TextFormField(
    initialValue: '${dSpec.getValue() ?? ""}',
    validator: validator,
    decoration: buildInputDecoration(
      context,
      dSpec.label,
      isM2: dSpec.isM2,
      isCount: dSpec.isCount,
      isCurrency: dSpec.isCurrency,
      isPercent: dSpec.isPercent,
      sufixText: dSpec.getValueComment != null ? dSpec.getValueComment!() : null,
    ),
    keyboardType: TextInputType.numberWithOptions(decimal: true, signed: false),
    inputFormatters: [
      if (dSpec.isRequired) MyTextInputFormatters.positiveDecimalOrEmpty else MyTextInputFormatters.positiveDecimal
    ],
    autovalidateMode: AutovalidateMode.onUserInteraction,
    readOnly: dSpec.readOnly,
    enabled: !dSpec.readOnly,
    onChanged: (String v) {
      if (validator(v) == null) {
        if (v == '') {
          dSpec.setValue(null);
        } else {
          var value = double.tryParse(v);
          dSpec.setValue(value);
        }
      }
    },
  );
}

Widget _buildStringField(BuildContext context, SimpleFieldEntry<String> sSpec) {
  final baseDecoration = buildInputDecoration(
    context,
    sSpec.label,
    sufixText: sSpec.getValueComment != null ? sSpec.getValueComment!() : null,
    isMultiline: sSpec.isMultiline,
  );

  return TextFormField(
    initialValue: '${sSpec.getValue() ?? ""}',
    validator: sSpec.isPhoneNumber
        ? MyTextInputValidators.isPhoneNumber(allowEmpty: !sSpec.isRequired)
        : sSpec.isEmail
            ? MyTextInputValidators.isEmail(allowEmpty: !sSpec.isRequired)
            : sSpec.isRequired
                ? MyTextInputValidators.notEmpty(
                    minLength: sSpec.minLength,
                    maxLength: sSpec.maxLength,
                    onlyDigits: sSpec.onlyDigits,
                  )
                : MyTextInputValidators.emptyOrInLength(
                    minLength: sSpec.minLength,
                    maxLength: sSpec.maxLength,
                    onlyDigits: sSpec.onlyDigits,
                  ),
    decoration: sSpec.isPhoneNumber
        ? baseDecoration.copyWith(suffixIcon: Icon(Icons.phone))
        : sSpec.isEmail
            ? baseDecoration.copyWith(suffixIcon: Icon(Icons.email))
            : baseDecoration,
    keyboardType: sSpec.isEmail
        ? TextInputType.emailAddress
        : sSpec.isPhoneNumber
            ? TextInputType.phone
            : TextInputType.text,
    autovalidateMode: AutovalidateMode.onUserInteraction,
    maxLines: sSpec.isMultiline ? null : 1,
    minLines: sSpec.isMultiline ? 2 : 1,
    enabled: !sSpec.readOnly,
    onChanged: (String v) {
      sSpec.setValue(v);
    },
    obscureText: sSpec.obscureText,
  );
}

Widget _buildDatetimeField(BuildContext context, SimpleFieldEntry<DateTime> dtSpec) {
  final baseDecoration = buildInputDecoration(
    context,
    dtSpec.label,
    sufixText: dtSpec.getValueComment != null ? dtSpec.getValueComment!() : null,
  );

  return DateTimeFormField(
    decoration: baseDecoration.copyWith(suffixIcon: Icon(Icons.calendar_today)),
    autovalidateMode: AutovalidateMode.onUserInteraction,
    mode: dtSpec.isDateOnly ? DateFieldPickerMode.date : DateFieldPickerMode.dateAndTime,
    initialDatePickerMode: DatePickerMode.day,
    initialValue: dtSpec.getValue(), //DateTime.now() : DateTime.now(),
    enabled: !dtSpec.readOnly,
    onDateSelected: (DateTime v) {
      dtSpec.setValue(v);
    },
    onSaved: (DateTime? v) {
      dtSpec.setValue(v);
    },
    validator: (DateTime? v) {
      if (dtSpec.isRequired && v == null) {
        return 'Por favor, introduzca una fecha';
      } else {
        return null;
      }
    },
  );
}

Widget _buildBoolField(BuildContext context, SimpleFieldEntry<bool> bSpec) {
  return Column(
    children: [
      SwitchListTile.adaptive(
        value: bSpec.getValue() ?? false,
        onChanged: (bool value) {
          bSpec.setValue(value);
        },
        title: bSpec.label == null ? null : Text(bSpec.label as String, style: Theme.of(context).textTheme.bodyText2),
        dense: true,
        contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
      ),
      Divider(
        thickness: 2,
        color: Colors.grey.withAlpha(100),
      )
    ],
  );
}
