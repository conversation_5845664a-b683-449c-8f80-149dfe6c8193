import 'FormEntry.dart';

abstract class FieldEntry<T> extends FormEntry {
  final String? name;
  final String? label;
  final T? Function() getValue;
  final void Function(T?) setValue;
  final String Function()? getValueComment;
  final bool isRequired;

  FieldEntry({
    this.name,
    this.label,
    required this.getValue,
    required this.setValue,
    this.isRequired = false,
    this.getValueComment,
  });
}
