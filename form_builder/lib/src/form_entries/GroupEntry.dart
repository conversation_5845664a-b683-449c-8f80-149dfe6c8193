import 'package:form_builder/src/form_entries/common/titled_card.dart';

import 'FormEntry.dart';
import 'package:flutter/material.dart';

const _c_card_padding = const EdgeInsets.fromLTRB(10, 10, 10, 10);
const _c_card_margin = const EdgeInsets.fromLTRB(5, 5, 5, 5);
const _c_subcard_padding = const EdgeInsets.all(0);
const _c_subcard_margin = const EdgeInsets.all(0);

class GroupEntry extends FormEntry {
  final String? label;
  final Iterable<FormEntry> children;
  final bool isSubgroup;

  GroupEntry({
    this.label,
    this.children = const <FormEntry>[],
    this.isSubgroup = false,
  });

  @override
  Widget build(BuildContext context) {
    return _buildFieldGroup(context, this);
  }
}

//Widget _buildFieldGroup(BuildContext context, GroupEntry gSpec) {
//  final theme = Theme.of(context);
//  return Container(
//      padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
//      child: Column(children: <Widget>[
//        if (gSpec.label != null)
//          Container(
//            padding: EdgeInsets.fromLTRB(5, 5, 0, 10),
//            child: Column(
//              children: <Widget>[
//                Align(
//                  alignment: Alignment.centerLeft,
//                  child: Text(
//                    gSpec.label,
//                    style: Theme.of(context).textTheme.headline6,
//                  ),
//                ),
//                Divider(height: 2, color: theme.primaryColorLight, thickness: 2.0, indent: 0.0)
//              ],
//            ),
//          ),
//        Container(
//          padding: EdgeInsets.fromLTRB(5, gSpec.label != null ? 0 : 5, 5, 5),
//          decoration: BoxDecoration(
//            border: Border.all(style: BorderStyle.none),
//            borderRadius: BorderRadius.circular(4.0),
//          ),
//          child: Column(
//            children: _buildEntries(
//              context: context,
//              fields: gSpec.children,
//            ),
//          ),
//        )
//      ]));
//}

Widget _buildFieldGroup(BuildContext context, GroupEntry gSpec) {
  final theme = Theme.of(context);
  return TitledCard(
    gSpec.label != null
        ? Text(
            gSpec.label as String,
            style: theme.textTheme.headline6,
          )
        : null,
    margin: gSpec.isSubgroup ? _c_subcard_margin : _c_card_margin,
    padding: gSpec.isSubgroup ? _c_subcard_padding : _c_card_padding,
    children: _buildEntries(
      context: context,
      fields: gSpec.children,
    ),
  );
}

List<Widget> _buildEntries({required BuildContext context, required Iterable<FormEntry> fields}) {
  return fields
      .map((spec) => spec.build(context))
      .fold([], (total, item) => total.length != 0 ? total + [SizedBox(height: 4), item] : total + [item]);
}
