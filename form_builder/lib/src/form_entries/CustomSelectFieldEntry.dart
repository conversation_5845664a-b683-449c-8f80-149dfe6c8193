import 'package:form_builder/src/form_entries/common/utils.dart';
import 'package:form_builder/src/widgets/custom_select_formfield.dart';
import 'FieldEntry.dart';
import 'package:flutter/material.dart';

class CustomSelectFieldEntry<T> extends FieldEntry<T> {
  /// Called to fill the autocomplete list's data.

  final String Function(T? item) valueToString;
  final Future<T?> Function() onSelect;
  final Widget? rightWidget;

  ///
  ///
  ///  [onSelect] Método que es evocado cuando usuario desea "desplegar" los elementos entre los que seleccionar.
  ///
  ///
  ///
  CustomSelectFieldEntry({
    String? name,
    String? label,
    isRequired = false,
    required T? Function() getValue,
    required void Function(T?) setValue,
    required this.onSelect,
    required this.valueToString,
    this.rightWidget,
  }) : super(name: name, label: label, getValue: getValue, setValue: setValue, isRequired: isRequired);

  @override
  Widget build(BuildContext context) {
    return _buildField(context, this);
  }
}

Widget _buildField<T>(BuildContext context, CustomSelectFieldEntry<T> tSpec) {
  return CustomSelectFormField<T>(
    decoration: buildInputDecoration(context, tSpec.label).copyWith(
      contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
    ),
    value: tSpec.getValue(),
    valueToString: tSpec.valueToString,
    onSelect: () => tSpec.onSelect(),
    onChanged: (selected) {
      tSpec.setValue(selected);
    },
    onSaved: (T? data) {
      tSpec.setValue(data);
    },
    required: tSpec.isRequired,
    autovalidateMode: AutovalidateMode.onUserInteraction,
    validator: (v) {
      if (tSpec.isRequired && v == null) {
        return "Debe seleccionar un valor";
      } else {
        return null;
      }
    },
    rightWidget: tSpec.rightWidget,
  );
}
