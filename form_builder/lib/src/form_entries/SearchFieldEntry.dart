import 'package:form_builder/src/form_entries/common/utils.dart';
import 'package:form_builder/src/widgets/search_formfield.dart';
import 'FieldEntry.dart';
import 'package:flutter/material.dart';

class SearchFieldEntry<T> extends FieldEntry<T> {
  /// Called to fill the autocomplete list's data.
  final Future<List<T>> Function(String search) onSearch;

  /// * Si se indica, se mostrará un "+" a la derecha del campo.
  /// * Cuando el usuario clicke en "+", se evocará este método.
  /// * El método debe devolver el nuevo valor que debe asignarse al campo o [null] si
  /// no debe cambiarse nada.
  final Future<T?> Function()? onAdd;
  final Widget Function(BuildContext context, T item, {bool isSelected}) itemBuilder;
  final String Function(T? item) valueToString;

  ///
  /// Widget que, si se indica, se situará a la derecha del campo.
  /// * Útil para crear botones adicionales a la derecha (Ej: botón para "ver detalles" o "ayuda").
  /// * El código del widget no tienen forma alguna de cambiar el valor interno del campo, por lo que
  ///   debería ser de tipo "consulta".
  final Widget? rightWidget;

  SearchFieldEntry({
    String? name,
    String? label,
    required T? Function() getValue,
    required void Function(T?) setValue,
    required this.onSearch,
    required this.itemBuilder,
    required this.valueToString,
    bool isRequired = false,
    this.onAdd,
    this.rightWidget,
  }) : super(name: name, label: label, getValue: getValue, setValue: setValue, isRequired: isRequired);

  @override
  Widget build(BuildContext context) {
    return _buildSearchField(context, this);
  }
}

Widget _buildSearchField<T>(BuildContext context, SearchFieldEntry<T> tSpec) {
  return SearchFormField<T>(
    decoration: buildInputDecoration(context, tSpec.label).copyWith(
      contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
    ),
    hintText: "Click para buscar",
    searchHint: "...",
    itemBuilder: (BuildContext context, T item, bool isSelected) =>
        tSpec.itemBuilder(context, item, isSelected: isSelected),
    value: tSpec.getValue(),
    valueToString: tSpec.valueToString,
    onFind: (String filter) => tSpec.onSearch(filter),
    onAdd: tSpec.onAdd,
    onChanged: (selected) {
      tSpec.setValue(selected);
    },
    onSaved: (data) {
      tSpec.setValue(data);
    },
    required: tSpec.isRequired,
    validator: (v) {
      if (tSpec.isRequired && v == null) {
        return "Debe seleccionar un valor";
      } else {
        return null;
      }
    },
    rightWidget: tSpec.rightWidget,
  );
}
