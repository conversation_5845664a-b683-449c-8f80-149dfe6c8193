import 'dart:math';

import 'package:flutter_rating_bar/flutter_rating_bar.dart';

import 'package:form_builder/src/form_entries/common/utils.dart';

import 'FieldEntry.dart';
import 'SelectOption.dart';
import 'package:flutter/material.dart';

///
/// 1 value selector
///
class RankFieldEntry<T> extends FieldEntry<T> {
  // Options represents the different ranked values.
  // First one is the smaller
  // Last one is the bugger
  final List<SelectOption<T>> options;

  RankFieldEntry({name, label, this.options = const [], isMultiple, getValue, setValue})
      : super(name: name, label: label, getValue: getValue, setValue: setValue);
  @override
  Widget build(BuildContext context) {
    //if (this is SelectFieldSpec<String>) {
    return _buildSelectRatingField(context, this);
    //} else {
    //  throw throw Exception("FieldSpec.build Unsuported T");
    //}
  }
}

Widget _buildSelectRatingField<T>(BuildContext context, RankFieldEntry<T> tSpec) {
  final theme = Theme.of(context);

  final int minRating = 1;
  final int totalRatings = tSpec.options.length;
  final T? initialValue = tSpec.getValue();
  final int initialRating = minRating + max(0, tSpec.options.indexWhere((op) => op.value == initialValue));

  return Column(children: [
    InputDecorator(
        decoration: buildInputDecoration(context, tSpec.label),
        child: Container(
            //height: (theme.buttonTheme.height ?? 24) ,
            //padding: EdgeInsets.fromLTRB(0,0,0,0),
            child: Row(children: [
          Expanded(child: Text(tSpec.options[initialRating - minRating].label, style: theme.textTheme.subtitle1)),
          RatingBar.builder(
            initialRating: initialRating.toDouble(),
            minRating: minRating.toDouble(),
            direction: Axis.horizontal,
            allowHalfRating: false,
            itemSize: theme.buttonTheme.height * 0.9,
            itemCount: totalRatings,
            itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
            itemBuilder: (context, index) => Icon(
              Icons.star_rate_rounded,
              color: theme.primaryColor.withAlpha(50 + 205 * (index + 1) ~/ totalRatings),
            ),
            onRatingUpdate: (rating) {
              if (rating != null) {
                var iRating = rating.toInt();
                var value = tSpec.options[iRating - minRating].value;
                tSpec.setValue(value);
              } else {
                tSpec.setValue(null);
              }
            },
          )
        ])))
  ]);
}
