import 'form_entries/form_entries.dart';
import 'package:flutter/material.dart';

extension FormBuilderExt on List<FormEntry> {
  Form buildForm({Key? key, required BuildContext context}) {
    return _buildEntriesForm(entries: this, key: key, context: context);
  }
}

extension FormBuilderExt2 on List<Widget> {
  Form buildForm({Key? key, required BuildContext context}) => Form(
        key: key,
        child: Column(
          children: this.fold(
            [],
            (total, item) => total.length != 0 ? total + [SizedBox(height: 4), item] : total + [item],
          ),
        ),
      );
}

Form _buildEntriesForm({Key? key, required BuildContext context, required List<FormEntry> entries}) =>
    Form(key: key, child: Column(children: _buildEntries(context: context, fields: entries)));

List<Widget> _buildEntries({required BuildContext context, required List<FormEntry> fields}) => fields
    .map((spec) => spec.build(context))
    .fold([], (total, item) => total.length != 0 ? total + [SizedBox(height: 4), item] : total + [item]);
