import 'package:flutter/services.dart';

class MyTextInputFormatters {
  static final positiveDecimal = FilteringTextInputFormatter.allow(RegExp(r'[0-9]+(\.[0-9]*)?'));
  static final positiveDecimalOrEmpty = FilteringTextInputFormatter.allow(RegExp(r'([0-9]+(\.[0-9]*)?)?'));
  static final positiveInt = FilteringTextInputFormatter.allow(RegExp(r'[0-9]+'));
  static final positiveIntOrEmpty = FilteringTextInputFormatter.allow(RegExp(r'[0-9]*'));
}
