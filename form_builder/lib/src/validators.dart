class MyTextInputValidators {
  static String? Function(String?) emptyOrInLength({int? minLength = 1, int? maxLength, bool? onlyDigits = false}) {
    return (value) {
      if (value == null || value.isEmpty)
        return null;
      else
        return notEmpty(minLength: minLength, maxLength: maxLength, onlyDigits: onlyDigits)(value);
    };
  }
  static String? Function(String?) notEmpty({int? minLength = 1, int? maxLength, bool? onlyDigits = false}) {
    return (value) {
      if (value == null || value.isEmpty)
        return 'Por favor, introduzca un valor';
      else if (onlyDigits == true && !_isOnlyDigitsString(value))
        return "Sólo puede usar dígitos decimales (0..9)";
      else if (minLength != null && value.length < minLength)
        return "Por favor, introduzca $minLength o más caracteres";
      else if (maxLength != null && value.length > maxLength)
        return "Por favor, introduzca $maxLength o menos caracteres";
      else
        return null;
    };
  }
  

  static String? Function(String?) isPassword({int minLength = 6}) {
    return (value) {
      final result = MyTextInputValidators.notEmpty(minLength: minLength)(value);
      if (result == null) {
        // Adicionales
      }
      return result;
    };
  }

  static String? Function(String?) isNumber({double? min, double? max, bool allowEmpty = false}) {
    return (value) {
      if (value == "" && allowEmpty) {
        return null;
      } else if (value == null && allowEmpty) {
        return null;
      } else {
        final dValue = value == null ? null : double.tryParse(value);
        if (dValue == null) {
          return 'Por favor, introduzca un valor numérico';
        } else if (min != null && dValue < min) {
          return 'Por favor, introduzca un valor mayor de $min';
        } else if (max != null && dValue > max) {
          return 'Por favor, introduzca un valor menor de $max';
        } else {
          return null;
        }
      }
    };
  }

  static String? Function(String?) isInteger({
    int? min,
    int? max,
    bool allowEmpty = false,
  }) {
    return (value) {
      if (allowEmpty && (value == null || value == ""))
        return null;
      else {
        final iValue = value == null ? null : int.tryParse(value);
        if (iValue == null) {
          return 'Por favor, introduzca un valor entero';
        } else if (min != null && iValue < min) {
          return 'Por favor, introduzca un valor mayor de $min';
        } else if (max != null && iValue > max) {
          return 'Por favor, introduzca un valor menor de $max';
        } else {
          return null;
        }
      }
    };
  }

  static String? Function(String?) isEmail({bool allowEmpty = false}) {
    return (value) {
      if (value == "" || value == null) {
        if (allowEmpty)
          return null;
        else
          return 'Por favor, introduzca un email';
      } else if (!_isEmailString(value))
        return 'Por favor, introduzca un email válido';
      else
        return null;
    };
  }

  static String? Function(String?) isPhoneNumber({bool allowEmpty = false}) {
    return (phoneNumber) {
      if (phoneNumber == "" || phoneNumber == null) {
        if (allowEmpty)
          return null;
        else
          return 'Por favor, introduzca un número de teléfono';
      } else if (!_isPhoneNumberString(phoneNumber))
        return 'Por favor, introduzca un número de teléfono válido';
      else
        return null;
    };
  }

  ///
  /// Indica si la cadena contiene únicamente dígitos (0,1,...,9)
  ///
  static bool _isOnlyDigitsString(String value) =>
      value.split("").firstWhere((s) => !s.contains(RegExp(r'[0-9]')), orElse: () => "") == "";
  static bool _isEmailString(String value) => RegExp(
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
      ).hasMatch(value);

  static bool _isPhoneNumberString(String value) =>
      // You may need to change this pattern to fit your requirement.
      // I just copied the pattern from here: https://regexr.com/3c53v
      RegExp(r'^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$').hasMatch(value);
}
