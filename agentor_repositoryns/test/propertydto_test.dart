import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Empty propertyattributes', () {
    final property = PropertyDto();
    expect(property.address is None, true);
    expect(property.attributes is None, true);
  });

  test('fromJson', () {
    expect(PropertyDto.fromJson({}).attributes is None, true);
    expect(
      () => PropertyDto.fromJson({"attributes": null}),
      throwsA(isException),
      reason: "attributes doensn't accept null",
    );
    // Probamos la conversión "combinada" de attributes usados pro property
    expect(PropertyDto.fromJson({"attributes": {}}).attributes is! None, true);
    expect(
      PropertyDto.fromJson({
        "attributes": {"toiletsCount": 2}
      }).attributes.v.toiletsCount,
      Some(2),
    );
    expect(
      PropertyDto.fromJson({
        "attributes": {"toiletsCount": 2}
      }).attributes.v.totalSurfaceM2 is None,
      true,
    );

    expect(
      () => PropertyDto.fromJson({"address": null}),
      throwsA(isException),
      reason: "address can't be null",
    );
    expect(
      PropertyDto.fromJson({"address": {}}).address.v.city is None,
      true,
    );
  });

  test('toJson', () {
    expect(PropertyDto().toJson().keys.isEmpty, true);
    expect(PropertyDto(attributes: Some(PropertyAttributesDto())).toJson().containsKey("attributes"), true);
    expect(
      PropertyDto.fromJson({
        "attributes": {"toiletsCount": 2}
      }).toJson()["attributes"]["toiletsCount"],
      2,
    );
    expect(
      PropertyDto.fromJson({
        "attributes": {"toilets": 2},
      }).toJson()["attributes"].containsKey("totalSurfaceM2"),
      false,
    );
  });
}
