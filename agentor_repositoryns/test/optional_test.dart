import 'package:agentor_repositoryns/optional.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Optional<T?> v property', () {
    final name = None<String>();
    expect(() => name.v, throwsA(isException), reason: "None has not value");
    expect(name.valueOrNull, null);
    final age = Some<int>(219);
    expect(age.v, 219);

    final agen = Some<int?>(null);
    expect(agen.v, null);

    expect(agen.where((v) => v != null).length, 0);
    expect(agen.where((v) => v == null).length, 1);
  });

  test("Comparing Optional<String>", () {
    expect(None<String>(), None<String>());
    expect(Some<String>("Hola") == Some<String>("Hola"), true);
    expect(Some<String>("Hola") != Some<String>("Adios"), true);
    final i = "Hola";
    expect(i.hashCode, i.hashCode);
  });

  test("None", () {
    expect(None<String>(), None<String>());
    expect(None<String>() is None, true);
    expect(None<String>() is! None, false);
    expect(Optional<int>.fromJson("123"), Some<int>(123));
    //print(i.hashCode);
    //print(Some<String>("Hola").hashCode);
    //print(Some<String>("Hola").hashCode);
  });

  test("True and False", () {
    expect(True, Some(true));
    expect(False, Some(false));
    expect(True is Some, false, reason: "True no hereda de Some por limitaciones en Dart");
    expect(False is Some, false, reason: "True no hereda de Some por limitaciones en Dart");
  });
  test("from Map", () {
    expect(Map<String, dynamic>().keyToOptional("unknown") is None, true);
    expect({"n": 233}.keyToOptional("n") is! None, true);
    expect({"n": 233}.keyToOptional("n").v, 233);
    expect({"n": null}.keyToOptional<int?>("n").v, null);
    expect(() => {"n": null}.keyToOptional<int>("n"), throwsA(isException),
        reason: "not nullable optional can't contain a null");
  });

  test("list from Map", () {
    expect(
      {
        "alist": ["1", "2", "3", "4", "5"]
      }.keyToOptionalList<int>("alist").v,
      [1, 2, 3, 4, 5],
    );
    expect(()=>{"alist":null}.keyToOptionalList<int>("alist"), throwsA(isException), reason: "keyToOptionalList needs a not null value");
  });
}
