import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Empty contact', () {
    final contact = ContactDto();
    expect(contact.email is None, true);
    expect(contact.firstName is None, true);
    expect(contact.lastName is None, true);
    expect(contact.mobile is None, true);
    expect(contact.notes is None, true);
    expect(contact.name is None, true);
  });

  test('fromJson', () {

    expect( ContactDto.fromJson({"email":"<EMAIL>"}).email, Some("<EMAIL>"));
    expect( ContactDto.fromJson({"email":null}).email.v == null, true);
    expect( ContactDto.fromJson({}).email is None, true);

    expect( ContactDto.fromJson({"firstName":"Paco"}).firstName, Some("Paco"));
    expect(() => ContactDto.fromJson({"firstName":null}), throwsA(isException), reason: "firstName doensn't accept null");
    expect( ContactDto.fromJson({}).firstName is None, true);

    expect( ContactDto.fromJson({"lastName":"Pil"}).lastName, Some("Pil"));
    expect( ContactDto.fromJson({"lastName":null}).lastName.v == null, true);
    expect( ContactDto.fromJson({}).lastName is None, true);

    expect( ContactDto.fromJson({"mobile":"+12345667"}).mobile, Some("+12345667"));
    expect( ContactDto.fromJson({"mobile":null}).mobile.v == null, true);
    expect( ContactDto.fromJson({}).mobile is None, true);

    expect( ContactDto.fromJson({"notes":"Notes Notes"}).notes, Some("Notes Notes"));
    expect( ContactDto.fromJson({"notes":null}).notes.v == null, true);
    expect( ContactDto.fromJson({}).notes is None, true);

    expect( ContactDto.fromJson({"firstName":"Paco"}).name, Some("Paco"));
    expect( ContactDto.fromJson({"lastName":"Paco"}).name, Some("Paco"));
    expect( ContactDto.fromJson({"firstName":"Paco", "lastName":"Pil"}).name, Some("Paco Pil"));
    expect( ContactDto.fromJson({}).name is None, true);

  });

  test("toJson", () {

    expect(ContactDto().toJson().containsKey("email"), false);
    expect(ContactDto(email: Some("<EMAIL>") ).toJson()["email"], "<EMAIL>");
    expect(ContactDto(email: Some(null) ).toJson()["email"], null);

    expect(ContactDto().toJson().containsKey("firstName"), false);
    expect(ContactDto(firstName: Some("Paco") ).toJson()["firstName"], "Paco");

    expect(ContactDto().toJson().containsKey("lastName"), false);
    expect(ContactDto(lastName: Some("Pil") ).toJson()["lastName"], "Pil");
    expect(ContactDto(lastName: Some(null) ).toJson()["lastName"], null);

    expect(ContactDto().toJson().containsKey("mobile"), false);
    expect(ContactDto(mobile: Some("+12345") ).toJson()["mobile"], "+12345");
    expect(ContactDto(mobile: Some(null) ).toJson()["mobile"], null);

    expect(ContactDto().toJson().containsKey("notes"), false);
    expect(ContactDto(notes: Some("Notes Notes") ).toJson()["notes"], "Notes Notes");
    expect(ContactDto(notes: Some(null) ).toJson()["notes"], null);

  });
}
