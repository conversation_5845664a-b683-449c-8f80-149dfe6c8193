import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Empty property', () {
    final attrs = PropertyAttributesDto();
    expect(attrs.totalSurfaceM2 is None, true);
    expect(attrs.orientationCodes is None, true);
  });

  test('fromJson', () {
    expect(PropertyAttributesDto.fromJson({}).totalSurfaceM2 is None, true);
    expect(PropertyAttributesDto.fromJson({"totalSurfaceM2": 42}).totalSurfaceM2, Some(42));
    expect(
      () => PropertyAttributesDto.fromJson({"totalSurfaceM2": null}),
      throwsA(isException),
      reason: "totalSurfaceM2 doensn't accept null",
    );

    expect(PropertyAttributesDto.fromJson({}).orientationCodes is None, true);
    expect(PropertyAttributesDto.fromJson({"orientationCodes": null}).orientationCodes.v, null);
    expect(PropertyAttributesDto.fromJson({"orientationCodes": []}).orientationCodes.v, []);
    expect(
      PropertyAttributesDto.fromJson({
        "orientationCodes": ["north", "west"]
      }).orientationCodes.v,
      ["north", "west"],
    );
  });

  test('toJson', () {
    expect(PropertyAttributesDto().toJson().keys.isEmpty, true);

    expect(
      PropertyAttributesDto.fromJson({"orientationCodes": null}).toJson()["orientationCodes"],
      null,
    );
    expect(
      PropertyAttributesDto.fromJson({"orientationCodes": []}).toJson()["orientationCodes"],
      [],
    );
    expect(
      PropertyAttributesDto.fromJson({
        "orientationCodes": ["north", "west"]
      }).toJson()["orientationCodes"],
      ["north", "west"],
    );
  });
}
