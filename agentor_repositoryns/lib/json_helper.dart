class JsonHelper {
  static T unserializeValue<T>(dynamic v) {
    if (v is T) {
      return v;
    } else if (v is int && _dummyDouble is T) {
      return v.toDouble() as T;
    } else if (v is double && _dummyInt is T) {
      return v.toInt() as T;
    } else if (v is String && _dummyDouble is T) {
      return double.tryParse(v) as T;
    } else if (v is String && _dummyInt is T) {
      return int.tryParse(v) as T;
    } else if (v is String && _dummyDatetime is T) {
      return DateTime.parse(v) as T;
    } else {
      throw Exception(
          "JsonHelper.unserializeValue.  Expected type ${T.toString()} but found ${v.runtimeType.toString()}");
    }
  }

  static List<T> unserializeList<T>(List vs) {
    if (vs is List<T>)
      return vs;
    else
      return vs.map((v) => unserializeValue<T>(v)).toList();
  }

  static T? keyToUnserialized<T>(Map map, String key, {T Function(dynamic)? fTransform}) {
    if (map.containsKey(key)) {
      final v = map[key];
      if (fTransform != null)
        return fTransform(v);
      else
        return unserializeValue<T>(v);
    } else {
      return null;
    }
  }

  /// Useful dummy variables/consts used to check a generic type  ( _dummyInt is T)
  static const int _dummyInt = 0;
  static const double _dummyDouble = 0;
  static final DateTime _dummyDatetime = DateTime(1);
}
