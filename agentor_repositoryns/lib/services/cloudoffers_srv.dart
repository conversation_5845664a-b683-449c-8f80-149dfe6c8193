import 'dart:convert';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';
import '../models/models.dart';

enum CloudoffersListOrderBy {
  created_at_desc,
  amount_desc,
  amount_asc,
}

enum CloudOffersListDateIntervalCode {
  last_48h,
  this_week,
  last_week,
  this_month,
  last_month,
  between_two_dates,
}

class CloudoffersListFilter {
  final Set<OfferstatusCode>? statuses;
  final Optional<bool> sourceIndividual;
  final Optional<String> sourceContactPhone;
  final Optional<bool> includeFavourites;
  final Optional<bool> includeNotFavourites;
  final Optional<String> containerzoneId;
  final Optional<String> propertytypeCode;
  final Optional<double> propertyM2Min;
  final Optional<double> propertyM2Max;
  final Optional<String> propertyAddrCityCode;
  final Optional<CloudOffersListDateIntervalCode> createdAtInterval;
  final Optional<DateTime> createdAtMin;
  final Optional<DateTime> createdAtMax;
  final Optional<bool> saleAllowed;
  final Optional<double> saleAmountMin;
  final Optional<double> saleAmountMax;

  final Optional<bool> rentAllowed;
  final Optional<double> rentAmountMin;
  final Optional<double> rentAmountMax;

  const CloudoffersListFilter({
    this.statuses = OfferstatusCodes.notHistoricOnes,
    this.sourceIndividual = const None(),
    this.sourceContactPhone = const None(),
    this.includeFavourites = const None(),
    this.includeNotFavourites = const None(),
    this.containerzoneId = const None(),
    this.propertytypeCode = const None(),
    this.createdAtInterval = const None(),
    this.createdAtMin = const None(),
    this.createdAtMax = const None(),
    this.saleAllowed = const None(),
    this.saleAmountMin = const None(),
    this.saleAmountMax = const None(),
    this.rentAllowed = const None(),
    this.rentAmountMin = const None(),
    this.rentAmountMax = const None(),
    this.propertyM2Min = const None(),
    this.propertyM2Max = const None(),
    this.propertyAddrCityCode = const None(),
  });

  CloudoffersListFilter copyWith({
    Set<OfferstatusCode>? statuses,
    Optional<bool>? sourceIndividual,
    Optional<String>? sourceContactPhone,
    Optional<bool>? includeFavourites,
    Optional<bool>? includeNotFavourites,
    Optional<String>? containerzoneId,
    Optional<String>? propertytypeCode,
    Optional<double>? propertyM2Min,
    Optional<double>? propertyM2Max,
    Optional<CloudOffersListDateIntervalCode>? createdAtInterval,
    Optional<DateTime>? createdAtMin,
    Optional<DateTime>? createdAtMax,
    Optional<bool>? saleAllowed,
    Optional<double>? saleAmountMin,
    Optional<double>? saleAmountMax,
    Optional<bool>? rentAllowed,
    Optional<double>? rentAmountMin,
    Optional<double>? rentAmountMax,
    Optional<String>? propertyAddrCityCode,
  }) {
    return CloudoffersListFilter(
      statuses: statuses ?? this.statuses,
      sourceIndividual: sourceIndividual ?? this.sourceIndividual,
      sourceContactPhone: sourceContactPhone ?? this.sourceContactPhone,
      includeFavourites: includeFavourites ?? this.includeFavourites,
      includeNotFavourites: includeNotFavourites ?? this.includeNotFavourites,
      containerzoneId: containerzoneId ?? this.containerzoneId,
      propertytypeCode: propertytypeCode ?? this.propertytypeCode,
      propertyM2Min: propertyM2Min ?? this.propertyM2Min,
      propertyM2Max: propertyM2Max ?? this.propertyM2Max,
      createdAtInterval: createdAtInterval ?? this.createdAtInterval,
      createdAtMin: createdAtMin ?? this.createdAtMin,
      createdAtMax: createdAtMax ?? this.createdAtMax,
      saleAllowed: saleAllowed ?? this.saleAllowed,
      saleAmountMin: saleAmountMin ?? this.saleAmountMin,
      saleAmountMax: saleAmountMax ?? this.saleAmountMax,
      rentAllowed: rentAllowed ?? this.rentAllowed,
      rentAmountMin: rentAmountMin ?? this.rentAmountMin,
      rentAmountMax: rentAmountMax ?? this.rentAmountMax,
      propertyAddrCityCode: propertyAddrCityCode ?? this.propertyAddrCityCode,
    );
  }

  String toString() =>
      "OffersListFilter  :$statuses, ${sourceIndividual.vn}, $propertytypeCode, $includeFavourites, $includeNotFavourites, $containerzoneId, $propertytypeCode, $saleAllowed, $rentAllowed, $propertyAddrCityCode";
}

extension CloudoffersSrv on ApiServices {
  Future<List<OfferDto>> listCloudoffers({
    CloudoffersListFilter filter = const CloudoffersListFilter(),
    CloudoffersListOrderBy orderBy = CloudoffersListOrderBy.created_at_desc,
    int offset = 0,
    int limit = 30,
  }) async {
    
    final now = DateTime.now();
    final Map<String, dynamic> params = {
      if (filter.statuses != null) "status_codes": (filter.statuses ?? {}).map((e) => e.toJsonString()).join(","),
      if (filter.sourceIndividual is! None) "source_individual": filter.sourceIndividual.v,
      if (filter.sourceContactPhone is! None) "source_contact_phone_contains": filter.sourceContactPhone.vn,
      if (filter.includeFavourites is! None) "include_favourites": filter.includeFavourites.v,
      if (filter.includeNotFavourites is! None) "include_not_favourites": filter.includeNotFavourites.v,
      if (filter.containerzoneId is! None) "containerzone_id": filter.containerzoneId.v,
      if (filter.propertytypeCode is! None) "property_type_code": filter.propertytypeCode.v,
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.between_two_dates &&
          filter.createdAtMin.vn != null)
        "created_at_min": _beginOfDay(filter.createdAtMin.v).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.between_two_dates &&
          filter.createdAtMax.vn != null)
        "created_at_max": _endOfDay(filter.createdAtMax.v).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.last_48h)
        "created_at_min": _last48h(now:now).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.this_week)
        "created_at_min": _weekFirstDate(now: now).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.this_week)
        "created_at_max": _weekLastDate(now: now).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.last_week)
        "created_at_min": _weekFirstDate(now: now, weeksAgo: 1).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.last_week)
        "created_at_max": _weekLastDate(now: now, weeksAgo: 1).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.this_month)
        "created_at_min": _monthFirstDate(now: now).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.this_month)
        "created_at_max": _monthLastDate(now: now).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.last_month)
        "created_at_min": _monthFirstDate(now: now, monthsAgo: 1).toUtc().toIso8601String(),
      if (filter.createdAtInterval.vn == CloudOffersListDateIntervalCode.last_month)
        "created_at_max": _monthLastDate(now: now, monthsAgo: 1).toUtc().toIso8601String(),
      if (filter.rentAllowed is! None) "rent_allowed": filter.rentAllowed.v,
      if (filter.rentAmountMin is! None) "rent_amount_min": filter.rentAmountMin.v,
      if (filter.rentAmountMax is! None) "rent_amount_max": filter.rentAmountMax.v,
      if (filter.saleAllowed is! None) "sale_allowed": filter.saleAllowed.v,
      if (filter.saleAmountMin is! None) "sale_amount_min": filter.saleAmountMin.v,
      if (filter.saleAmountMax is! None) "sale_amount_max": filter.saleAmountMax.v,
      if (filter.propertyM2Min is! None) "property_m2_min": filter.propertyM2Min.v,
      if (filter.propertyM2Max is! None) "property_m2_max": filter.propertyM2Max.v,
      if (filter.propertyAddrCityCode is! None) "property_address_city_code": filter.propertyAddrCityCode.v,
      if (orderBy == CloudoffersListOrderBy.created_at_desc) "oby_created_at": "desc",
      if (orderBy == CloudoffersListOrderBy.amount_asc) "oby_amount": "asc",
      if (orderBy == CloudoffersListOrderBy.amount_desc) "oby_amount": "desc",
      "offset": offset,
      "limit": limit
    };
    final body = await apiGet(
      'agents/me/cloud_offers',
      params: params,
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => OfferDto.fromJson(item as Map<String, dynamic>)).toList();
  }
  static DateTime _last48h({DateTime? now}){
    now = now ?? DateTime.now();
    return now.subtract(Duration(days:2));
  }
  static DateTime _weekFirstDate({DateTime? now, int weeksAgo = 0}) {
    now = now ?? DateTime.now();
    final mondayWithTime = now.subtract(Duration(days: now.weekday - 1)).subtract(Duration(days: weeksAgo * 7));

    return new DateTime(mondayWithTime.year, mondayWithTime.month, mondayWithTime.day);
  }

  static DateTime _weekLastDate({DateTime? now, int weeksAgo = 0}) {
    final mondayDate = _weekFirstDate(now: now, weeksAgo: weeksAgo);
    return mondayDate.add(Duration(days: 6, hours: 23, minutes: 59, seconds: 59, milliseconds: 999));
  }

  static DateTime _monthFirstDate({DateTime? now, int monthsAgo = 0}) {
    now = now ?? DateTime.now();
    final firstWithTime = now.subtract(Duration(days: now.day - 1));
    // note: If months is negative, date is correctly generated ()
    return new DateTime(firstWithTime.year, firstWithTime.month - monthsAgo, firstWithTime.day);
  }

  static DateTime _monthLastDate({DateTime? now, int monthsAgo = 0}) {
    final monthFirst = _monthFirstDate(now: now, monthsAgo: monthsAgo);
    final nextMonthFirst = new DateTime(monthFirst.year, monthFirst.month + 1, monthFirst.day);
    return nextMonthFirst.subtract(Duration(milliseconds: 1));
  }

  static DateTime _endOfDay(DateTime date) {
    return new DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  static DateTime _beginOfDay(DateTime date) {
    return new DateTime(date.year, date.month, date.day);
  }
}
