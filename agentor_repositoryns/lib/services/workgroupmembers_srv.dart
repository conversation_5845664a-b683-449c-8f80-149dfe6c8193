import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';




class AgentAffiliationsListFilter {
  final Optional<bool> includeCanRead;
  final Optional<bool> includeCanPublish;

  const AgentAffiliationsListFilter({
    this.includeCanRead = const None(),
    this.includeCanPublish = const None(),
  });

  AgentAffiliationsListFilter copyWith({Optional<bool>? includeCanRead, Optional<bool>? includeCanPublish}) {
    return AgentAffiliationsListFilter(
      includeCanRead: includeCanRead ?? this.includeCanRead,
      includeCanPublish: includeCanPublish ?? this.includeCanPublish,
    );
  }

  String toString() => "AgentAffiliationsListFilter $includeCanRead, $includeCanPublish";
}

extension WorkgroupmembersSrv on ApiServices {
  Future<List<WorkgroupMemberDto>> listAgentAffiliations({
    AgentAffiliationsListFilter filter = const AgentAffiliationsListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {
    final Map<String, dynamic> params = {
      if (filter.includeCanRead is! None) "include_can_read": filter.includeCanRead.v,
      if (filter.includeCanPublish is! None) "include_can_publish": filter.includeCanPublish.v,
      "offset": offset,
      "limit": limit
    };

    final body = await apiGet(
      'agents/me/affiliations',
      params: params,
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => WorkgroupMemberDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
