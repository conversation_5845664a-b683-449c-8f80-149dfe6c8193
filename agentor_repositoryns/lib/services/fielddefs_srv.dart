import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';

extension FielddefsSrv on ApiServices {
  static const _c_code = "code";
  static const _c_label = "label";
  static const _c_type = "type";
  static const _c_values = "values";
  static const _c_value = "value";
  static const _c_ranks = "ranks";
  static const _c_max = "max";
  static const _c_min = "min";
  static const _c_default = "default";
  static const _c_en = "en";
  static const _c_es = "es";
  static const _c_for_demand = "for_demand";
  static const _c_for_offer = "for_offer";

  static const c_offer_status = "offer_status";
  static const c_offer_mandate_type = "offer_mandate_type";
  static const c_offer_mandate_start = "offer_mandate_start";
  static const c_offer_mandate_end = "offer_mandate_end";
  static const c_offer_historic_date = "offer_historic_date";
  static const c_offer_historic_cause_type = "offer_historic_cause_type";
  static const c_offer_customer = "offer_customer";
  static const c_offer_urgency = "offer_urgency";
  static const c_offer_type = "offer_type"; // sale, rent
  static const c_offer_sale_amount = "offer_sale_amount";
  static const c_offer_sale_monthlyPayment = "offer_sale_monthlyPayment";
  static const c_offer_sale_marketAmount = "offer_sale_marketAmount";
  static const c_offer_sale_fee_type = "offer_sale_fee_type";
  static const c_offer_sale_fee_percentValue = "offer_sale_fee_percentValue"; // Internamente alimenta el campo fee.value, se usa cuando fee.type.code=='percent'
  static const c_offer_sale_fee_fixedValue = "offer_sale_fee_fixedValue"; // Internamente alimenta el campo fee.value, se usa cuando fee.type.code=='fixed'
  static const c_offer_rent_amount = "offer_rent_amount";
  static const c_offer_rent_marketAmount = "offer_rent_marketAmount";
  static const c_offer_notes = "offer_notes";
  static const c_offer_version_disclaimer = "offer_version_disclaimer";
  static const c_offer_description = "offer_description";

  static const c_demand_status = "demand_status";
  static const c_demand_customer = "demand_customer";
  static const c_demand_urgency = "demand_urgency";
  static const c_demand_type = "demand_type"; // sale, rent
  static const c_demand_sale_amount = "offer_sale_amount";
  static const c_demand_rent_amount = "offer_rent_amount";

  static const c_contact_firstName = "contact_firstName";
  static const c_contact_lastName = "contact_lastName";
  static const c_contact_email = "contact_email";
  static const c_contact_mobile = "contact_mobile";
  static const c_contact_notes = "contact_notes";

  static const c_prop_type = "prop_type";
  static const c_prop_subtype = "prop_subtype";
  static const c_prop_zone = "prop_zone";
  static const c_prop_address_city = "prop_address_city";
  static const c_prop_address_postcode = "prop_address_postcode";
  static const c_prop_address_streettype = "prop_address_streettype";
  static const c_prop_address_streetname = "prop_address_streetname";
  static const c_prop_address_number = "prop_address_number";
  static const c_prop_address_detail = "prop_address_detail";
  // #region Property Attributes Field Codes
  static const c_prop_attrs_totalSurface = "prop_attrs_totalSurface";
  static const c_prop_attrs_usefulSurface = "prop_attrs_usefulSurface";
  static const c_prop_attrs_solarSurface = "prop_attrs_solarSurface";
  static const c_prop_attrs_constructionYear = "prop_attrs_constructionYear";
  static const c_prop_attrs_status = "prop_attrs_status";
  static const c_prop_attrs_conservationStatus = "prop_attrs_conservationStatus";
  static const c_prop_attrs_individualBedroomsCount = "prop_attrs_individualBedroomsCount";
  static const c_prop_attrs_doubleBedroomsCount = "prop_attrs_doubleBedroomsCount";
  static const c_prop_attrs_suiteBedroomsCount = "prop_attrs_suiteBedroomsCount";
  static const c_prop_attrs_totalBedroomsCount = "prop_attrs_totalBedroomsCount";
  static const c_prop_attrs_bathroomsCount = "prop_attrs_bathroomsCount";
  static const c_prop_attrs_toiletsCount = "prop_attrs_toiletsCount";
  static const c_prop_attrs_builtInCabinetsCount = "prop_attrs_builtInCabinetsCount";
  static const c_prop_attrs_buddleHas = "prop_attrs_buddleHas";
  static const c_prop_attrs_dinningRoomHas = "prop_attrs_dinningRoomHas";
  static const c_prop_attrs_storageRoomHas = "prop_attrs_storageRoomHas";
  static const c_prop_attrs_balconyHas = "prop_attrs_balconyHas";
  static const c_prop_attrs_terraceHas = "prop_attrs_terraceHas";
  static const c_prop_attrs_externalJoinery = "prop_attrs_externalJoinery";
  static const c_prop_attrs_ground = "prop_attrs_ground";
  static const c_prop_attrs_waterSupplyHas = "prop_attrs_waterSupplyHas";
  static const c_prop_attrs_powerSupplyHas = "prop_attrs_powerSupplyHas";
  static const c_prop_attrs_gasSupplyHas = "prop_attrs_gasSupplyHas";
  static const c_prop_attrs_airConditioning = "prop_attrs_airConditioning";
  static const c_prop_attrs_heating = "prop_attrs_heating";
  static const c_prop_attrs_fireplaceHas = "prop_attrs_fireplaceHas";
  static const c_prop_attrs_intercomHas = "prop_attrs_intercomHas";
  static const c_prop_attrs_reinforcedDoorHas = "prop_attrs_reinforcedDoorHas";
  static const c_prop_attrs_alarmSystemHas = "prop_attrs_alarmSystemHas";
  static const c_prop_attrs_elevatorHas = "prop_attrs_elevatorHas";
  static const c_prop_attrs_handicappedAccessibleIs = "prop_attrs_handicappedAccessibleIs";
  static const c_prop_attrs_furnishedIs = "prop_attrs_furnishedIs";
  static const c_prop_attrs_garden = "prop_attrs_garden";
  static const c_prop_attrs_outsideArea = "prop_attrs_outsideArea";
  static const c_prop_attrs_swimmingPool = "prop_attrs_swimmingPool";
  static const c_prop_attrs_parkingPlacesCount = "prop_attrs_parkingPlacesCount";
  static const c_prop_attrs_optionalParkingIs = "prop_attrs_optionalParkingIs";
  static const c_prop_attrs_facade = "prop_attrs_facade";
  static const c_prop_attrs_orientation = "prop_attrs_orientation";
  static const c_prop_attrs_sunnyIs = "prop_attrs_sunnyIs";
  static const c_prop_attrs_communityFeesAmount = "prop_attrs_communityFeesAmount";
  static const c_prop_attrs_neighborsPerFloorCount = "prop_attrs_neighborsPerFloorCount";
  static const c_prop_attrs_buildingFloorsCount = "prop_attrs_buildingFloorsCount";
  static const c_prop_attrs_floor = "prop_attrs_floor";
  static const c_prop_attrs_energyCertificate = "prop_attrs_energiCertificate";
  static const c_prop_attrs_consumptionLevel = "prop_attrs_consumptionLevel";
  static const c_prop_attrs_emissionLevel = "prop_attrs_emmisionLevel";
  // #endregion

  static final List<Map<String, Object>> _propertyFieldDefsJson = [
    {
      _c_code: c_prop_type,
      _c_label: {_c_default: "tipo", _c_es: "type"},
      _c_type: "code",
      _c_values: [
        {
          _c_code: "flat",
          _c_label: {_c_default: "Flat", _c_es: "Piso"}
        },
        {
          _c_code: "house",
          _c_label: {_c_default: "House", _c_es: "Casa"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: true,
    },
    {
      _c_code: c_prop_subtype,
      _c_label: {_c_default: "sub-type", _c_es: "sub-tipo"},
      _c_type: "code",
      _c_values: [
        // Los valoresdependen del tipo del inmueble
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_zone,
      _c_label: {_c_default: "zone", _c_es: "zona"},
      _c_type: "search",
      _c_for_offer: true,
      _c_for_demand: true,
    },
    {
      // Ojo: Debe construirse a mano... ya que los valores se obtienen de una búsqueda
      _c_code: c_prop_address_city,
      _c_label: {_c_default: "city", _c_es: "ciudad"},
      _c_type: "search",
      _c_for_offer: true,
      _c_for_demand: true,
    },
    {
      _c_code: c_prop_address_postcode,
      _c_label: {_c_default: "postal code", _c_es: "código postal"},
      _c_type: "text",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_address_streettype,
      _c_label: {_c_default: "street type", _c_es: "tipo de vía"},
      _c_type: "code",
      /** TODO: usar la definición obtenida a través de REST */
      _c_values: _sortValuesByLabel([
        {
          _c_code: "1",
          _c_label: {_c_default: "Street", _c_es: "Calle"}
        },
        {
          _c_code: "2",
          _c_label: {_c_default: "Ride", _c_es: "Paseo"}
        },
        {
          _c_code: "3",
          _c_label: {_c_default: "Avenue", _c_es: "Avenida"}
        },
        {
          _c_code: "4",
          _c_label: {_c_default: "Round", _c_es: "Ronda"}
        },
        {
          _c_code: "5",
          _c_label: {_c_default: "Crossing", _c_es: "Travesía"}
        },
        {
          _c_code: "6",
          _c_label: {_c_default: "Road", _c_es: "Carretera"}
        },
        {
          _c_code: "7",
          _c_label: {_c_default: "Boulevard", _c_es: "Rambla"}
        },
        {
          _c_code: "8",
          _c_label: {_c_default: "Square", _c_es: "Plaza"}
        },
        {
          _c_code: "9",
          _c_label: {_c_default: "Passage", _c_es: "Pasaje"}
        },
        {
          _c_code: "10",
          _c_label: {_c_default: "Slope", _c_es: "Bajada"}
        },
        {
          _c_code: "11",
          _c_label: {_c_default: "Way", _c_es: "Vía"}
        },
        {
          _c_code: "13",
          _c_label: {_c_default: "Urbanization", _c_es: "Urbanización"}
        },
        {
          _c_code: "14",
          _c_label: {_c_default: "Way", _c_es: "Camino"}
        },
        {
          _c_code: "15",
          _c_label: {_c_default: "Sector", _c_es: "Sector"}
        },
        {
          _c_code: "16",
          _c_label: {_c_default: "Roundabout", _c_es: "Glorieta"}
        },
        {
          _c_code: "17",
          _c_label: {_c_default: "Mall", _c_es: "Alameda"}
        },
        {
          _c_code: "18",
          _c_label: {_c_default: "Ravine", _c_es: "Barranco"}
        },
        {
          _c_code: "19",
          _c_label: {_c_default: "Lane", _c_es: "Calleja"}
        },
        {
          _c_code: "20",
          _c_label: {_c_default: "Hill", _c_es: "Cuesta"}
        },
        {
          _c_code: "21",
          _c_label: {_c_default: "Group", _c_es: "Grupo"}
        },
        {
          _c_code: "22",
          _c_label: {_c_default: "Great way", _c_es: "Gran via"}
        },
        {
          _c_code: "23",
          _c_label: {_c_default: "Gardens", _c_es: "Jardines"}
        },
        {
          _c_code: "24",
          _c_label: {_c_default: "Dock", _c_es: "Muelle"}
        },
        {
          _c_code: "25",
          _c_label: {_c_default: "Industrial park", _c_es: "Poligono industrial"}
        },
        {
          _c_code: "26",
          _c_label: {_c_default: "Park", _c_es: "Parque"}
        },
        {
          _c_code: "27",
          _c_label: {_c_default: "Extension", _c_es: "Prolongación"}
        },
        {
          _c_code: "28",
          _c_label: {_c_default: "Would laugh", _c_es: "Riera"}
        },
        {
          _c_code: "29",
          _c_label: {_c_default: "Rua", _c_es: "Rua"}
        },
        {
          _c_code: "30",
          _c_label: {_c_default: "Rise", _c_es: "Subida"}
        },
      ]),
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_address_streetname,
      _c_label: {_c_default: "name of the road", _c_es: "nombre de la vía"},
      _c_type: "text",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_address_number,
      _c_label: {_c_default: "number", _c_es: "número"},
      _c_type: "text",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_address_detail,
      _c_label: {_c_default: "Stair, floor, door, ...", _c_es: "Escalera, piso, puerta, ..."},
      _c_type: "text",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_totalSurface,
      _c_label: {_c_default: "surface", _c_es: "superficie"},
      _c_type: "m2",
      _c_for_offer: true,
      _c_for_demand: true,
    },
    {
      _c_code: c_prop_attrs_usefulSurface,
      _c_label: {_c_default: "useful surface", _c_es: "superficie útil"},
      _c_type: "m2",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_solarSurface,
      _c_label: {_c_default: "parcel area", _c_es: "superficie del solar"},
      _c_type: "m2",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_constructionYear,
      _c_label: {_c_default: "construction yerar", _c_es: "año de construcción"},
      _c_type: "year",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_status,
      _c_label: {_c_default: "status", _c_es: "estado"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "new",
          _c_label: {_c_default: "new", _c_es: "nuevo"}
        },
        {
          _c_code: "second_hand",
          _c_label: {_c_default: "second hand", _c_es: "segunda mano"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_conservationStatus,
      _c_label: {_c_default: "conservaton state", _c_es: "estado de conservación"},
      _c_type: "code", // FieldDefType.code.enumToString(), //FieldDefType.code,
      _c_values: [
        {
          _c_code: "mint",
          _c_label: {_c_default: "Mint", _c_es: "Por estrenar"}
        },
        {
          _c_code: "good",
          _c_label: {_c_default: "Good", _c_es: "Bueno"}
        },
        {
          _c_code: "reformed",
          _c_label: {_c_default: "Reformed", _c_es: "Reformado"}
        },
        {
          _c_code: "to_reform",
          _c_label: {_c_default: "To reform", _c_es: "Para reformar"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_individualBedroomsCount,
      _c_label: {_c_default: "individual bedrooms", _c_es: "habitaciones individuales"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_doubleBedroomsCount,
      _c_label: {_c_default: "double bedrooms", _c_es: "habitaciones dobles"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_suiteBedroomsCount,
      _c_label: {_c_default: "suite bedrooms", _c_es: "habitaciones suite"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_totalBedroomsCount,
      _c_label: {_c_default: "total bedrooms", _c_es: "habitaciones totales"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: true,
    },
    {
      _c_code: c_prop_attrs_bathroomsCount,
      _c_label: {_c_default: "bathrooms", _c_es: "baños"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_toiletsCount,
      _c_label: {_c_default: "toilets", _c_es: "aseos"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_builtInCabinetsCount,
      _c_label: {_c_default: "built in cabinets", _c_es: "armarios empotrados"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 100,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_buddleHas,
      _c_label: {_c_default: "buddle", _c_es: "lavadero"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_dinningRoomHas,
      _c_label: {_c_default: "dinning", _c_es: "comedor"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_storageRoomHas,
      _c_label: {_c_default: "storage", _c_es: "trastero"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_balconyHas,
      _c_label: {_c_default: "balcony", _c_es: "balcón"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_terraceHas,
      _c_label: {_c_default: "terrace", _c_es: "terraza"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_externalJoinery,
      _c_label: {_c_default: "external joinery", _c_es: "carpintería exterior"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "aluminium",
          _c_label: {_c_default: "Aluminium", _c_es: "Aluminio"}
        },
        {
          _c_code: "wood",
          _c_label: {_c_default: "Wood", _c_es: "Madera"}
        },
        {
          _c_code: "pvc",
          _c_label: {_c_default: "PVC", _c_es: "PVC"}
        },
        {
          _c_code: "other",
          _c_label: {_c_default: "Other", _c_es: "Otro"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_ground,
      _c_label: {_c_default: "ground", _c_es: "tipo de suelo"},
      _c_type: "multicode", // FieldDefType.code.enumToString()
      _c_values: [
        {
          _c_code: "gres",
          _c_label: {_c_default: "Gres", _c_es: "Gres"}
        },
        {
          _c_code: "marble",
          _c_label: {_c_default: "Marble", _c_es: "Mármol"}
        },
        {
          _c_code: "carpet",
          _c_label: {_c_default: "Carpet", _c_es: "Moqueta"}
        },
        {
          _c_code: "parquet",
          _c_label: {_c_default: "Parquet", _c_es: "Parquet"}
        },
        {
          _c_code: "laminatedFlooring",
          _c_label: {_c_default: "Laminated flooring", _c_es: "Tarima flotante"}
        },
        {
          _c_code: "solidFlooring",
          _c_label: {_c_default: "Solid flooring", _c_es: "Tarima maciza"}
        },
        {
          _c_code: "terrazzo",
          _c_label: {_c_default: "Terrazzo", _c_es: "Terrazo"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_waterSupplyHas,
      _c_label: {_c_default: "water supply", _c_es: "suministro de agua"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_powerSupplyHas,
      _c_label: {_c_default: "power supply", _c_es: "suministro eléctrico"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_gasSupplyHas,
      _c_label: {_c_default: "gas supply", _c_es: "suministro gas"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_airConditioning,
      _c_label: {_c_default: "air conditioning", _c_es: "aire acondicionado"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "none",
          _c_label: {_c_default: "None", _c_es: "No"}
        },
        {
          _c_code: "cold",
          _c_label: {_c_default: "Cold", _c_es: "Frío"}
        },
        {
          _c_code: "coldAndHeat",
          _c_label: {_c_default: "Cold and heat", _c_es: "Frío y calor"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_heating,
      _c_label: {_c_default: "heating", _c_es: "calefacción"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "none",
          _c_label: {_c_default: "None", _c_es: "No"}
        },
        {
          _c_code: "central",
          _c_label: {_c_default: "Central", _c_es: "Central"}
        },
        {
          _c_code: "electric",
          _c_label: {_c_default: "Electric", _c_es: "Eléctrica"}
        },
        {
          _c_code: "naturalGas",
          _c_label: {_c_default: "Natural gas", _c_es: "Gas natural"}
        },
        {
          _c_code: "gasoil",
          _c_label: {_c_default: "Gasoil", _c_es: "Gasoil"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_fireplaceHas,
      _c_label: {_c_default: "Fireplace", _c_es: "Chimenea"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_intercomHas,
      _c_label: {_c_default: "Intercom", _c_es: "Interfono"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_reinforcedDoorHas,
      _c_label: {_c_default: "Reinforced door", _c_es: "Puerta blindada"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_alarmSystemHas,
      _c_label: {_c_default: "System alarm", _c_es: "Sistema de alarma"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_elevatorHas,
      _c_label: {_c_default: "Elevator", _c_es: "Ascensor"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_handicappedAccessibleIs,
      _c_label: {_c_default: "Handicapped accessible", _c_es: "accesible para discapacitados"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_furnishedIs,
      _c_label: {_c_default: "furnished", _c_es: "amueblado"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_garden,
      _c_label: {_c_default: "garden", _c_es: "jardín"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "none",
          _c_label: {_c_default: "None", _c_es: "No"}
        },
        {
          _c_code: "own",
          _c_label: {_c_default: "Own", _c_es: "Propio"}
        },
        {
          _c_code: "community",
          _c_label: {_c_default: "Community", _c_es: "Comunitario"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_outsideArea,
      _c_label: {_c_default: "Outside area", _c_es: "Zona exterior"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "none",
          _c_label: {_c_default: "None", _c_es: "No"}
        },
        {
          _c_code: "own",
          _c_label: {_c_default: "Own", _c_es: "Propia"}
        },
        {
          _c_code: "community",
          _c_label: {_c_default: "Community", _c_es: "Comunitaria"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_swimmingPool,
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_label: {_c_default: "swimming", _c_es: "piscina"},
      _c_values: [
        {
          _c_code: "none",
          _c_label: {_c_default: "None", _c_es: "No"}
        },
        {
          _c_code: "own",
          _c_label: {_c_default: "Own", _c_es: "Propia"}
        },
        {
          _c_code: "community",
          _c_label: {_c_default: "Community", _c_es: "Comunitaria"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_parkingPlacesCount,
      _c_label: {_c_default: "Parking places", _c_es: "Plazas de parking"},
      _c_type: "count",
      _c_min: 0,
      _c_max: 10,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_optionalParkingIs,
      _c_label: {_c_default: "Optional parking", _c_es: "Parking opcional"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_facade,
      _c_type: "multicode", // FieldDefType.code.enumToString(),
      _c_label: {_c_default: "fachada", _c_es: "fachada"},
      _c_values: [
        {
          _c_code: "interior",
          _c_label: {_c_default: "Interior", _c_es: "Interior"}
        },
        {
          _c_code: "exterior",
          _c_label: {_c_default: "Exterior", _c_es: "Exterior"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_orientation,
      _c_label: {_c_default: "orientation", _c_es: "orientación"},
      _c_type: "multicode", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "north",
          _c_label: {_c_default: "North", _c_es: "Norte"}
        },
        {
          _c_code: "northeast",
          _c_label: {_c_default: "North-east", _c_es: "Noreste"}
        },
        {
          _c_code: "east",
          _c_label: {_c_default: "East", _c_es: "Este"}
        },
        {
          _c_code: "southeast",
          _c_label: {_c_default: "South-east", _c_es: "Sureste"}
        },
        {
          _c_code: "south",
          _c_label: {_c_default: "South", _c_es: "Sur"}
        },
        {
          _c_code: "southwest",
          _c_label: {_c_default: "South-west", _c_es: "Suroeste"}
        },
        {
          _c_code: "west",
          _c_label: {_c_default: "West", _c_es: "Oeste"}
        },
        {
          _c_code: "northwest",
          _c_label: {_c_default: "North-west", _c_es: "Noroeste"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_sunnyIs,
      _c_label: {_c_default: "sunny", _c_es: "soleado"},
      _c_type: "yesnot",
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_communityFeesAmount,
      _c_label: {_c_default: "community fees (monthly)", _c_es: "cuota comunidad (mensual)"},
      _c_type: "currency",
      _c_min: 0,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_neighborsPerFloorCount,
      _c_label: {_c_default: "Neighbors per floor", _c_es: "Vecinos por planta"},
      _c_type: "count",
      _c_min: 1,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_buildingFloorsCount,
      _c_label: {_c_default: "Building floors", _c_es: "Plantas del edificio"},
      _c_type: "count",
      _c_min: 1,
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_floor,
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_label: {_c_default: "floor", _c_es: "planta"},
      _c_values: [
        {
          _c_code: "basement",
          _c_label: {_c_default: "Basement", _c_es: "Sótano"}
        },
        {
          _c_code: "semibasement",
          _c_label: {_c_default: "Semibasement", _c_es: "Semisótano"}
        },
        {
          _c_code: "ground",
          _c_label: {_c_default: "Ground", _c_es: "Baja"}
        },
        {
          _c_code: "mezzanine",
          _c_label: {_c_default: "Mezzanine", _c_es: "Entresuelo"}
        },
        {
          _c_code: "main",
          _c_label: {_c_default: "Main", _c_es: "Principal"}
        },
        {
          _c_code: "1",
          _c_label: {_c_default: "1st", _c_es: "1ª"}
        },
        {
          _c_code: "2",
          _c_label: {_c_default: "2nd", _c_es: "2ª"}
        },
        {
          _c_code: "3",
          _c_label: {_c_default: "3rd", _c_es: "3ª"}
        },
        {
          _c_code: "4",
          _c_label: {_c_default: "4th", _c_es: "4ª"}
        },
        {
          _c_code: "5",
          _c_label: {_c_default: "5th", _c_es: "5ª"}
        },
        {
          _c_code: "6",
          _c_label: {_c_default: "6th", _c_es: "6ª"}
        },
        {
          _c_code: "7",
          _c_label: {_c_default: "7th", _c_es: "7ª"}
        },
        {
          _c_code: "8",
          _c_label: {_c_default: "8th", _c_es: "8ª"}
        },
        {
          _c_code: "9",
          _c_label: {_c_default: "9th", _c_es: "9ª"}
        },
        {
          _c_code: "10",
          _c_label: {_c_default: "10th", _c_es: "10ª"}
        },
        {
          _c_code: "11",
          _c_label: {_c_default: "11th", _c_es: "11ª"}
        },
        {
          _c_code: "12",
          _c_label: {_c_default: "12th", _c_es: "12ª"}
        },
        {
          _c_code: "13",
          _c_label: {_c_default: "13th", _c_es: "13ª"}
        },
        {
          _c_code: "14",
          _c_label: {_c_default: "14th", _c_es: "14ª"}
        },
        {
          _c_code: "15",
          _c_label: {_c_default: "15th", _c_es: "15ª"}
        },
        {
          _c_code: "16",
          _c_label: {_c_default: "16th", _c_es: "16ª"}
        },
        {
          _c_code: "17",
          _c_label: {_c_default: "17th", _c_es: "17ª"}
        },
        {
          _c_code: "18",
          _c_label: {_c_default: "18th", _c_es: "18ª"}
        },
        {
          _c_code: "19",
          _c_label: {_c_default: "19th", _c_es: "19ª"}
        },
        {
          _c_code: "20",
          _c_label: {_c_default: "+20th", _c_es: "+20ª"}
        },
        {
          _c_code: "penthouse",
          _c_label: {_c_default: "Penthouse", _c_es: "Ático"}
        },
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_energyCertificate,
      _c_label: {_c_default: "energy certificate", _c_es: "certificado energético"},
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_values: [
        {
          _c_code: "available",
          _c_label: {_c_default: "Available", _c_es: "Disponible"}
        },
        {
          _c_code: "inProcess",
          _c_label: {_c_default: "In process", _c_es: "En proceso"}
        },
        {
          _c_code: "exempt",
          _c_label: {_c_default: "Exempt", _c_es: "Exento"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_consumptionLevel,
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_label: {_c_default: "consumption level", _c_es: "nivel de consumo energético"},
      _c_values: [
        {
          _c_code: "A",
          _c_label: {_c_default: "A", _c_es: "A"}
        },
        {
          _c_code: "B",
          _c_label: {_c_default: "B", _c_es: "B"}
        },
        {
          _c_code: "C",
          _c_label: {_c_default: "C", _c_es: "C"}
        },
        {
          _c_code: "D",
          _c_label: {_c_default: "D", _c_es: "D"}
        },
        {
          _c_code: "E",
          _c_label: {_c_default: "E", _c_es: "E"}
        },
        {
          _c_code: "F",
          _c_label: {_c_default: "F", _c_es: "F"}
        },
        {
          _c_code: "G",
          _c_label: {_c_default: "G", _c_es: "G"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    },
    {
      _c_code: c_prop_attrs_emissionLevel,
      _c_type: "code", // FieldDefType.code.enumToString(),
      _c_label: {_c_default: "emissions level", _c_es: "nivel de emisiones"},
      _c_values: [
        {
          _c_code: "A",
          _c_label: {_c_default: "A", _c_es: "A"}
        },
        {
          _c_code: "B",
          _c_label: {_c_default: "B", _c_es: "B"}
        },
        {
          _c_code: "C",
          _c_label: {_c_default: "C", _c_es: "C"}
        },
        {
          _c_code: "D",
          _c_label: {_c_default: "D", _c_es: "D"}
        },
        {
          _c_code: "E",
          _c_label: {_c_default: "E", _c_es: "E"}
        },
        {
          _c_code: "F",
          _c_label: {_c_default: "F", _c_es: "F"}
        },
        {
          _c_code: "G",
          _c_label: {_c_default: "G", _c_es: "G"}
        }
      ],
      _c_for_offer: true,
      _c_for_demand: false,
    }
  ];
  static final List<Map<String, dynamic>> _offerFieldsDefsJson = [
        {
          _c_code: c_offer_status,
          _c_label: {_c_default: "status", _c_es: "estado"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "news",
              _c_label: {_c_default: "News", _c_es: "Noticia"}
            },
            {
              _c_code: "draft",
              _c_label: {_c_default: "Draft", _c_es: "Borrador"}
            },
            {
              _c_code: "commercialization",
              _c_label: {_c_default: "Available", _c_es: "Disponible"}
            },
            {
              _c_code: "historic",
              _c_label: {_c_default: "Bin", _c_es: "Papelera"}
            }
          ]
        },
        {
          _c_code: c_offer_historic_date,
          _c_label: {_c_default: "Bin date", _c_es: "fecha de papelera"},
          _c_type: "date"
        },
        {
          _c_code: c_offer_historic_cause_type,
          _c_label: {_c_default: "Bin cause", _c_es: "causa de papelera"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "sold_by_agent",
              _c_label: {_c_default: "Sold by agent", _c_es: "Vendido por el agente"}
            },
            {
              _c_code: "sold_by_competitor",
              _c_label: {_c_default: "Sold by competitor", _c_es: "Vendido por la competencia"}
            },
            {
              _c_code: "sold_by_owner",
              _c_label: {_c_default: "Sold by owner", _c_es: "Vendido por el propietario"}
            },
            {
              _c_code: "canceled_by_agent",
              _c_label: {_c_default: "Canceled by agent", _c_es: "Cancelado por el agente"}
            },
            {
              _c_code: "canceled_by_owner",
              _c_label: {_c_default: "Canceled by owner", _c_es: "Cancelado por el propietario"}
            },
          ]
        },
        {
          _c_code: c_offer_mandate_type,
          _c_label: {_c_default: "mandate type", _c_es: "tipo de mandato"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "exclusive",
              _c_label: {_c_default: "Exclusive", _c_es: "En exclusiva"}
            },
            {
              _c_code: "open",
              _c_label: {_c_default: "Open", _c_es: "Abierto"}
            },
            {
              _c_code: "verbal",
              _c_label: {_c_default: "Verbal", _c_es: "Verbal"}
            },
            {
              _c_code: "flat_rate",
              _c_label: {_c_default: "Flat rate", _c_es: "Tarifa plana"}
            },
            {
              _c_code: "other",
              _c_label: {_c_default: "Other", _c_es: "Otro"}
            }
          ]
        },
        {
          _c_code: c_offer_mandate_start,
          _c_label: {_c_default: "mandate start", _c_es: "inicio de mandato"},
          _c_type: "date"
        },
        {
          _c_code: c_offer_mandate_end,
          _c_label: {_c_default: "mandate end", _c_es: "fin de mandato"},
          _c_type: "date"
        },
        {
          // Ojo: Debe construirse a mano... ya que los valores se obtienen de una búsqueda
          _c_code: c_offer_customer,
          _c_label: {_c_default: "owner", _c_en: "owner", _c_es: "propietario"},
          _c_type: "search"
        },
        {
          _c_code: c_offer_urgency,
          _c_label: {_c_default: "urgency", _c_es: "urgencia del cliente"},
          _c_type: "rank",
          _c_ranks: [
            {
              _c_value: 0,
              _c_label: {_c_default: "None", _c_es: "Ninguna"}
            },
            {
              _c_value: 1,
              _c_label: {_c_default: "Little", _c_es: "Poca"}
            },
            {
              _c_value: 2,
              _c_label: {_c_default: "Normal", _c_es: "Normal"}
            },
            {
              _c_value: 3,
              _c_label: {_c_default: "A lot", _c_es: "Mucha"}
            }
          ]
        },
        {
          _c_code: c_offer_type,
          _c_label: {_c_default: "type", _c_es: "tipo"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "sale",
              _c_label: {_c_default: "Sale", _c_es: "Venta"}
            },
            {
              _c_code: "rent",
              _c_label: {_c_default: "Rent", _c_es: "Alquiler"}
            }
          ]
        },
        {
          _c_code: c_offer_sale_amount,
          _c_label: {_c_default: "sale amount", _c_es: "precio de venta"},
          _c_type: "currency",
          _c_min: 0
        },
        {
          _c_code: c_offer_sale_monthlyPayment,
          _c_label: {_c_default: "monthly payment", _c_es: "Cuota"},
          _c_type: "currency",
          _c_min: 0
        },
        {
          _c_code: c_offer_sale_marketAmount,
          _c_label: {_c_default: "market sale amount", _c_es: "precio de venta de mercado"},
          _c_type: "currency",
          _c_min: 0
        },
        {
          _c_code: c_offer_sale_fee_type,
          _c_label: {_c_default: "sale fee type", _c_es: "tipo de comisión"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "percent",
              _c_label: {_c_default: "Percent", _c_es: "Porcentage"}
            },
            {
              _c_code: "fixed",
              _c_label: {_c_default: "Fixed", _c_es: "Fija"}
            }
          ]
        },
        {
          _c_code: c_offer_sale_fee_fixedValue,
          _c_label: {_c_default: "fee", _c_es: "comisión"},
          _c_type: "currency",
          _c_min: 0,
        },
        {
          _c_code: c_offer_sale_fee_percentValue,
          _c_label: {_c_default: "fee", _c_es: "comisión"},
          _c_type: "percent",
          _c_min: 0,
        },
        {
          _c_code: c_offer_rent_amount,
          _c_label: {_c_default: "rent amount", _c_es: "precio de alquiler"},
          _c_type: "currency",
          _c_min: 0
        },
        {
          _c_code: c_offer_rent_marketAmount,
          _c_label: {_c_default: "market rent amount", _c_es: "precio de alquiler de mercado"},
          _c_type: "currency",
          _c_min: 0
        },
        {
          _c_code: c_offer_notes,
          _c_label: {_c_default: "notes", _c_es: "notas"},
          _c_type: "description"
        },
        {
          _c_code: c_offer_version_disclaimer,
          _c_label: {_c_default: "disclaimer", _c_es: "pre-descripción"},
          _c_type: "description"
        },
        {
          _c_code: c_offer_description,
          _c_label: {_c_default: "description", _c_es: "descripción"},
          _c_type: "description"
        }
      ] +
      _propertyFieldDefsJson.where((pf) {
        final forOffer = pf[_c_for_offer];
        return forOffer == true;
      }).toList();

  static final List<Map<String, dynamic>> _demandFieldsDefsJson = [
        {
          _c_code: c_demand_status,
          _c_label: {_c_default: "status", _c_es: "estado"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "active",
              _c_label: {_c_default: "Active", _c_es: "Activa"}
            },
            {
              _c_code: "historic",
              _c_label: {_c_default: "Bin", _c_es: "Papelera"}
            }
          ]
        },
        {
          // Ojo: Debe construirse a mano... ya que los valores se obtienen de una búsqueda
          _c_code: c_demand_customer,
          _c_label: {_c_default: "customer", _c_es: "cliente"},
          _c_type: "search"
        },
        {
          _c_code: c_demand_urgency,
          _c_label: {_c_default: "customer urgency", _c_es: "urgencia del cliente"},
          _c_type: "rank",
          _c_ranks: [
            {
              _c_value: 0,
              _c_label: {_c_default: "None", _c_es: "Ninguna"}
            },
            {
              _c_value: 1,
              _c_label: {_c_default: "little", _c_es: "Poca"}
            },
            {
              _c_value: 2,
              _c_label: {_c_default: "Normal", _c_es: "Normal"}
            },
            {
              _c_value: 3,
              _c_label: {_c_default: "A lot", _c_es: "Mucha"}
            }
          ]
        },
        {
          _c_code: c_demand_type,
          _c_label: {_c_default: "type", _c_es: "tipo"},
          _c_type: "code",
          _c_values: [
            {
              _c_code: "sale",
              _c_label: {_c_default: "Sale", _c_es: "Venta"}
            },
            {
              _c_code: "rent",
              _c_label: {_c_default: "Rent", _c_es: "Alquiler"}
            }
          ]
        },
        {
          _c_code: c_demand_sale_amount,
          _c_label: {_c_default: "sale amount", _c_es: "precio de venta"},
          _c_type: "currency",
          _c_min: 0
        },
        {
          _c_code: c_demand_rent_amount,
          _c_label: {_c_default: "rent amount", _c_es: "precio de alquiler"},
          _c_type: "currency",
          _c_min: 0
        }
      ] +
      _propertyFieldDefsJson.where((pf) => pf[_c_for_demand] == true).toList();

  static final List<Map<String, dynamic>> _contactFieldDefsJson = [
    {
      _c_code: c_contact_firstName,
      _c_label: {_c_default: "name", _c_es: "nombre"},
      _c_type: "text",
    },
    {
      _c_code: c_contact_lastName,
      _c_label: {_c_default: "surname", _c_es: "apellidos"},
      _c_type: "text",
    },
    {
      _c_code: c_contact_email,
      _c_label: {_c_default: "email", _c_es: "email"},
      _c_type: "email",
    },
    {
      _c_code: c_contact_mobile,
      _c_label: {_c_default: "phone (mobile)", _c_es: "teléfono (móvil)"},
      _c_type: "phonenumber"
    },
    {
      _c_code: c_contact_notes,
      _c_label: {_c_default: "notes", _c_es: "notas"},
      _c_type: "description"
    },
  ];

  static final List<FieldDefDto> _offerFieldDefs = _offerFieldsDefsJson.map((json) => FieldDefDto.fromJson(json)).toList();
  static final List<FieldDefDto> _demandFieldDefs = _demandFieldsDefsJson.map((json) => FieldDefDto.fromJson(json)).toList();
  static final List<FieldDefDto> _contactFieldDefs = _contactFieldDefsJson.map((json) => FieldDefDto.fromJson(json)).toList();

  Future<List<FieldDefDto>> listOfferFielddefs() async => _offerFieldDefs;
  Future<List<FieldDefDto>> listDemandFielddefs() async => _demandFieldDefs;
  Future<List<FieldDefDto>> listContactFielddefs() async => _contactFieldDefs;

  Future<FieldDefDto> getFielddef(String code) async => _offerFieldDefs.where((fd) => fd.code == code).first;

  ///
  /// Dada una lista de valores en un campo de selección, genera la versión ordenada según el idioma indicado
  ///
  static List<Map<String, Object>> _sortValuesByLabel(final List<Map<String, Object>> values, {String language = _c_default}) {
    // no cambiamos la lista original
    var result = values.toList();
    result.sort((a, b) {
      final av = (a[_c_label] as Map)[language] as String? ?? "";
      final bv = (b[_c_label] as Map)[language] as String? ?? "";
      return av.compareTo(bv);
    });
    return result;
  }
}
