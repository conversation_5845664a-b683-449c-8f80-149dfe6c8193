import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';



class PropertyzonesListFilter {
  final Optional<String> id;
  final Optional<String> parentId;
  final Optional<bool> includeParent;

  const PropertyzonesListFilter({this.id = const None(), this.parentId = const None(), this.includeParent = False});

  PropertyzonesListFilter copyWith({
    Optional<String>? id,
    Optional<String>? parentId,
    Optional<bool>? includeParent,
  }) =>
      PropertyzonesListFilter(
        id: id ?? this.id,
        parentId: parentId ?? this.parentId,
        includeParent: includeParent ?? this.includeParent,
      );

  String toString() {
    return "PropertyzonesListFilter id:$id";
  }
}

extension PropertyzonesSrv on ApiServices {
  Future<List<PropertyzoneDto>> listPropertyzones({
    PropertyzonesListFilter filter = const PropertyzonesListFilter(),
    int offset = 0,
    int limit = 200,
  }) async {
    final body = await apiGet(
      'propertyzones',
      params: {
        if (filter.id is! None) "id": filter.id.v,
        if (filter.parentId is! None) "parent_id": filter.parentId.v,
        if (filter.includeParent is! None) "include_parent": filter.includeParent.v,
        "offset": offset,
        "limit": limit
      },
      useToken: true,
    );

    return _from(body);
  }

  Future<List<PropertyzoneDto>> getPropertyzonePath([String? zoneId]) async {
    if (zoneId==null) {
      return [];
    } else {
      final body = await apiGet("propertyzones/$zoneId/path", useToken: true);
      return _from(body);
    }
  }

  List<PropertyzoneDto> _from(String body) {
    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => PropertyzoneDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
