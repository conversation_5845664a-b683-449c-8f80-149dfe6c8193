import 'dart:convert';

import 'package:agentor_repositoryns/models/offerversiontype_dto.dart';
import 'package:agentor_repositoryns/services/services.dart';

extension OfferversiontypesSrv on ApiServices {
  Future<List<OfferversiontypeDto>> listOfferversiontypes() async {
    final body = await apiGet(
      'offerversiontypes',
      params: {},
      useToken: true,
    );

    final List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => OfferversiontypeDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
