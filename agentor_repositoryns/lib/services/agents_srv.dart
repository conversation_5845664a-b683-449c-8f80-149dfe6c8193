import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

extension AgentsSrv on ApiServices {
  ///
  /// Gets actual token associated agent.
  ///
  Future<AgentDto?> getMyAgent() => _get("agents/me");

  ///
  /// Gets an agent
  ///
  Future<AgentDto?> getAgent(String id) => _get("agents/$id");

  Future<AgentDto> putAgent(AgentDto agent) async {
    assert(agent.id is! None);
    return apiPut(
      "agents/${agent.id.v}",
      data: agent.copyWith(
        id: None(),
        isInternal: None(),
        isPremium: None(),
        isSeller: None(),
      ),
      useToken: true,
    ).then((body) => json.decode(body) as Map<String, dynamic>).then((json) => AgentDto.fromJson(json));
  }

  ///
  ///
  ///  Raises:
  ///  *  ApiEmailAlredyExistException:  Si el email ya está siendo usado por alguna cuenta
  ///  * ApiException: Excepcion standard
  Future<void> postPresignup(String email) async {
    await apiPost("agents_validationcodes", data: {"email": email}, useToken: false);
  }

  Future<bool> getOnetime(String onetime) async {
    try {
      final body = await apiOTGetJson(
        "valid/$onetime/2",
        treat404AsNull: true,
      );
      return body != null && body['success'] == true;
    } on ApiException catch (err) {
      print("error $err");
      return false;
    }
  }

  Future<OnetimeDto> postOnetime(String onetime, String email) async {
    
      final body = await apiOTPostJson(
        "update/$onetime/2",
        data: {"email": email},
      );
      return OnetimeDto.fromJson(body);
  }

  Future<AgentDto> postAgent(AgentDto agent, String validationCode, ScrtyCredentialsDto credentials) async {
    assert(validationCode.length != 0);
    final body = await apiPostJson(
      "agents",
      data: {
        "agent": agent.copyWith(id: None()),
        "credentials": credentials.copyWith(username: None()),
        "validationcode": validationCode,
      },
      useToken: false,
    );
    return AgentDto.fromJson(body);
  }

  Future<void> postPrePassword(String email) async {
    assert(email.length != 0);

    await apiPost(
      "passwords_validationcodes",
      data: {
        "email": email,
      },
      useToken: false,
    );
  }

  Future<void> postPassword({
    required String email,
    required String password,
    required String validationCode,
  }) async {
    assert(email.length != 0);
    assert(password.length != 0);
    assert(validationCode.length != 0);

    await apiPost(
      "passwords",
      data: {
        "email": email,
        "password": password,
        "validationcode": validationCode,
      },
      useToken: false,
    );
  }

  Future<AgentDto?> _get(String url) async {
    return apiGetJson(
      url,
      useToken: true,
      treat404AsNull: true,
    ).then(
      (json) => json == null ? null : AgentDto.fromJson(json),
    );
  }
}
