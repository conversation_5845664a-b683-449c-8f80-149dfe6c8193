import 'dart:convert';

import 'package:agentor_repositoryns/models/actiontype_dto.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

class ActiontypesListFilter {
  final Optional<String> id;
  final Optional<bool> withService;
  const ActiontypesListFilter({
    this.id = const None(),
    this.withService = const None(),    
  });

  ActiontypesListFilter copyWith({Optional<String>? id, Optional<bool>? withService}) {
    return ActiontypesListFilter(id: id ?? this.id, withService: withService ?? this.withService);
  }

  String toString() {
    return "ActiontypesListFilter id:$id";
  }
}

extension ActiontypesSrv on ApiServices {
  Future<List<ActiontypeDto>> listActiontypes({ActiontypesListFilter filter = const ActiontypesListFilter()}) async {
    final body = await apiGet(
      'agents/me/actiontypes',
      params: {
        if (filter.id is! None) 'id': filter.id.v,
        if (filter.withService is! None) 'withService': filter.withService.v,
      },
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => ActiontypeDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
