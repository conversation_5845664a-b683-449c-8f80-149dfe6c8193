import 'dart:convert';

import 'package:agentor_repositoryns/services/services.dart';


extension TokensSrv on ApiServices {
  Future<String> renewToken(String token) async {
    final body = await apiPut('tokens/actual', useToken: true, token: token);
    final String renewedToken = json.decode(body);
    return renewedToken;
  }

  Future<String> createToken(String username, String password) async {
    final body = await apiPost('tokens', data:{"username":username, "password":password});
    final String token = json.decode(body);
    return token;
  }
}
