import 'dart:convert';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';
part "demands_list_filter.dart";

extension DemandsSrv on ApiServices {
  Future<DemandDto> postDemand(DemandDto demand) async {
    final body = await apiPost(
      'agents/me/demands',
      data: demand.copy()..agent = None(),
      useToken: true,
    );

    Map<String, dynamic> jsonBody = json.decode(body);
    return DemandDto.fromJson(jsonBody);
  }

  Future<DemandDto> putDemand(DemandDto demand) async {
    assert(demand.id.vn != null);

    final body = await apiPut(
      'agents/me/demands/${demand.id.v}',
      data: demand.copy()..agent = None(),
      useToken: true,
    );

    Map<String, dynamic> jsonBody = json.decode(body);
    return DemandDto.fromJson(jsonBody);
  }

  ///
  /// Gets an agent demand
  /// [notFoundAsNull] indicates how to treat 404 (not found) response code:   if [true] function will return [null], otherwise function will trhow an error
  ///
  Future<DemandDto?> getDemand(String demandId) async {
    assert(demandId.trim().length != 0, "Demand id must be a non empty string");
    final data = await apiGetJson(
      'agents/me/demands/${demandId.trim()}',
      useToken: true,
      treat404AsNull: true,
    );
    if (data == null)
      return null;
    else
      return DemandDto.fromJson(data);
  }

  Future<List<DemandDto>> listDemands({
    DemandsListFilter filter = const DemandsListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {

    final Map<String, dynamic> params = {
      if (filter.statuses != null) "status_codes": (filter.statuses ?? {}).map((e) => e.enumToString()).join(","),
      if (filter.demandId is! None) "id": filter.demandId.v,
      if (filter.customerId is! None) "customer_id": filter.customerId.v,
      if (filter.propertytypeCode is! None) "property_type_code": filter.propertytypeCode.v,
      if (filter.rentAllowed is! None) "rent_allowed": filter.rentAllowed.v,
      if (filter.rentAmountMin is! None) "rent_amount_min": filter.rentAmountMin.v,
      if (filter.rentAmountMax is! None) "rent_amount_max": filter.rentAmountMax.v,
      if (filter.saleAllowed is! None) "sale_allowed": filter.saleAllowed.v,
      if (filter.saleAmountMin is! None) "sale_amount_min": filter.saleAmountMin.v,
      if (filter.saleAmountMax is! None) "sale_amount_max": filter.saleAmountMax.v,
      if (filter.propertyM2Min is! None) "property_m2_min": filter.propertyM2Min.v,
      if (filter.propertyM2Max is! None) "property_m2_max": filter.propertyM2Max.v,
      if (filter.propertyAddrCityCode is! None) "property_address_city_code": filter.propertyAddrCityCode.v,
      "offset": offset,
      "limit": limit
    };

    final body = await apiGet('agents/me/demands',
        params: params,
        useToken: true);

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => DemandDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
