part of "./demands_srv.dart";

class DemandsListFilter {
  final Set<DemandstatusCode>? statuses;
  final Optional<String> demandId;
  final Optional<String> customerId;
  final Optional<String> propertytypeCode;
  final Optional<double> propertyM2Min;
  final Optional<double> propertyM2Max;
  final Optional<String> propertyAddrCityCode;
  final Optional<bool> saleAllowed;
  final Optional<double> saleAmountMin;
  final Optional<double> saleAmountMax;
  final Optional<bool> rentAllowed;
  final Optional<double> rentAmountMin;
  final Optional<double> rentAmountMax;

  const DemandsListFilter({
    this.statuses = DemandstatusCodes.notHistoricOnes,
    this.demandId = const None(),
    this.customerId = const None(),
    this.propertytypeCode = const None(),
    this.saleAllowed = const None(),
    this.saleAmountMin = const None(),
    this.saleAmountMax = const None(),
    this.rentAllowed = const None(),
    this.rentAmountMin = const None(),
    this.rentAmountMax = const None(),
    this.propertyM2Min = const None(),
    this.propertyM2Max = const None(),
    this.propertyAddrCityCode = const None(),
  });

  DemandsListFilter copyWith({
    Set<DemandstatusCode>? statuses,
    Optional<String>? customerId,
    Optional<String>? demandId,
    Optional<String>? propertytypeCode,
    Optional<double>? propertyM2Min,
    Optional<double>? propertyM2Max,
    Optional<String>? propertyAddrCityCode,
    Optional<bool>? saleAllowed,
    Optional<double>? saleAmountMin,
    Optional<double>? saleAmountMax,
    Optional<bool>? rentAllowed,
    Optional<double>? rentAmountMin,
    Optional<double>? rentAmountMax,
  }) =>
      DemandsListFilter(
        statuses: statuses ?? this.statuses,
        demandId: demandId ?? this.demandId,
        customerId: customerId ?? this.customerId,
        propertytypeCode: propertytypeCode ?? this.propertytypeCode,
        propertyM2Min: propertyM2Min ?? this.propertyM2Min,
        propertyM2Max: propertyM2Max ?? this.propertyM2Max,
        propertyAddrCityCode: propertyAddrCityCode ?? this.propertyAddrCityCode,
        saleAllowed: saleAllowed ?? this.saleAllowed,
        saleAmountMin: saleAmountMin ?? this.saleAmountMin,
        saleAmountMax: saleAmountMax ?? this.saleAmountMax,
        rentAllowed: rentAllowed ?? this.rentAllowed,
        rentAmountMin: rentAmountMin ?? this.rentAmountMin,
        rentAmountMax: rentAmountMax ?? this.rentAmountMax,
      );

  String toString() => "DemandsListFilter $statuses, $demandId, $customerId, $propertytypeCode, $propertytypeCode, $saleAllowed, $rentAllowed, $propertyAddrCityCode";
}
