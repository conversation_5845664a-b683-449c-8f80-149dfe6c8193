import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

class StreettypesListFilter {
  final Optional<String> id;

  const StreettypesListFilter({
    this.id = const None(),
  });

  StreettypesListFilter copyWith({Optional<String>? id}) {
    return StreettypesListFilter(id: id ?? this.id);
  }

  String toString() {
    return "StreettypesListFilter id:$id";
  }
}

extension StreettypesSrv on ApiServices {
  Future<List<StreettypeDto>> listActiontypes({StreettypesListFilter filter = const StreettypesListFilter()}) async {
    final body = await apiGet(
      'agents/me/streettypes',
      params: {
        if (filter.id is! None) "id": filter.id.v,
      },
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => StreettypeDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
