import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';

extension EcomAccountsSrv on ApiServices {
  ///
  /// Gets actual token associated agent.
  ///
  Future<EcomAccountDto?> getMyEcomAccount() => _get("agents/me/my_ecom_account");

  Future<EcomAccountDto?> _get(String url) async {
    return apiGetJson(
      url,
      useToken: true,
      treat404AsNull: true,
    ).then(
      (json) => json == null ? null : EcomAccountDto.fromJson(json),
    );
  }
}
