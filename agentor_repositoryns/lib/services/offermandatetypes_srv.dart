import 'dart:convert';

import 'package:agentor_repositoryns/models/offermandatetype_dto.dart';
import 'package:agentor_repositoryns/services/services.dart';

extension OffermandatetypesSrv on ApiServices {
  Future<List<OffermandatetypeDto>> listOffermandatetypes() async {
    final body = await apiGet(
      'agents/me/offermandatetypes',
      params: {},
      useToken: true,
    );

    final List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => OffermandatetypeDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
