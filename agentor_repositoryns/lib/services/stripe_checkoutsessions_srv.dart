
import 'dart:convert';

import 'package:agentor_repositoryns/models/stripe_dtos.dart';
import 'package:agentor_repositoryns/services/services.dart';

extension StripeCheckoutsessionsSrv on ApiServices {
  ///
  /// Crear una sesión de checkout.
  /// 
  ///
  Future<StripeCheckoutSessionDto> postStripeCheckoutsession(String priceCode, String successUrl, String cancelUrl) async {
    final body = await apiPost('agents/me/stripe/checkout_session', useToken: true, data: {
      "success_url": successUrl,
      "cancel_url": cancelUrl,
      "priceCode": priceCode
    } );

    Map<String, dynamic> jsonBody = json.decode(body);
    return StripeCheckoutSessionDto.fromJson(jsonBody);
  }
}
