import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';

extension SupportrequestsSrv on ApiServices {
  Future<SupportrequestDto> postSupportrequest(SupportrequestDto supportrequest) =>
      apiPost('agents/me/supportrequests', data: supportrequest, useToken: true)
          .then((body) => json.decode(body) as Map<String, dynamic>)
          .then((map) => SupportrequestDto.fromJson(map));
}
