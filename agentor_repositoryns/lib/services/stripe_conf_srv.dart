
import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';


extension StripeConfSrv on ApiServices {
  ///
  /// Obtener la configuración de Stripe del servidor (la clave pública)
  ///
  Future<StripeConfDto> getStripeConf() async {
    final body = await apiGet('agents/me/stripe/conf', useToken: true);
    Map<String,dynamic> jsonBody = json.decode(body);
    return StripeConfDto.fromJson(jsonBody);
    
  }
}
