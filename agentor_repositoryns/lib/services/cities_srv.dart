import 'dart:convert';

import 'package:agentor_repositoryns/models/city_dto.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

enum CitiesUsageContext {
  me,
  meAndCoworers, 
}
class CitiesListFilter {
  final Optional<String> search;
  final Optional<String> code;

  /// Si != None, indica que queremos ciudades que estén en uso bajo un contexto determinado:
  /// me -> Solo ciudades usadas en mis ofertas.
  /// meAndCoworers -> Ciudades usadas por mis ofertas o ofertas compartidas conmigo (de "coworkers"... se excluye servicios Cloud y otros especiales)
  final Optional<CitiesUsageContext> usedBy;

  const CitiesListFilter({
    this.search = const None(),
    this.code = const None(),
    this.usedBy = const None(),
    
  });

  CitiesListFilter copyWith({
    Optional<String>? search,
    Optional<String>? code,
    Optional<CitiesUsageContext>? usedBy,
  }) {
    return CitiesListFilter(
      search: search ?? this.search,
      code: code ?? this.code,
      usedBy: usedBy ?? this.usedBy,
      
    );
  }

  String toString() {
    return "CitiesListFilter search:$search, code:$code, inUseBy: $usedBy";
  }
}

extension CitiesSrv on ApiServices {
  Future<List<CityDto>> getCities({required CitiesListFilter filter, int offset = 0, int limit = 30}) async {
    final body = await apiGet("cities",
        params: {
          if (filter.search.vn != null) "search": filter.search.v,
          if (filter.code.vn != null) "code": filter.code.v,
          if (filter.usedBy.vn != null) "used_by": filter.usedBy.v == CitiesUsageContext.me ? "me" : "me_and_coworkers",
          "offset": offset,
          "limit": limit,
        },
        useToken: true);
    List<dynamic> jsonResponse = json.decode(body);
    return jsonResponse.map((dynamic item) => CityDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
