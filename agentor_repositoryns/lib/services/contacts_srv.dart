import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/api_services.dart';
import 'package:agentor_repositoryns/optional.dart';

class ContactsListFilter {
  final Optional<String> search;
  final Optional<bool> isDemandCustomer;
  final Optional<bool> isOfferCustomer;
  final Optional<bool> isBankServicer;
  final Optional<bool> hasSite;

  const ContactsListFilter({
    this.search = const None(),
    this.isDemandCustomer = const None(),
    this.isOfferCustomer = const None(),
    this.isBankServicer = const None(),
    this.hasSite = const None(),
  });

  ContactsListFilter copyWith({
    Optional<String>? search,
    Optional<bool>? isDemandCustomer,
    Optional<bool>? isOfferCustomer,
    Optional<bool>? isBankServicer,
    Optional<bool>? hasSite,
  }) {
    return ContactsListFilter(
      search: search ?? this.search,
      isDemandCustomer: isDemandCustomer ?? this.isDemandCustomer,
      isOfferCustomer: isOfferCustomer ?? this.isOfferCustomer,
      isBankServicer: isBankServicer ?? this.isBankServicer,
      hasSite: hasSite ?? this.hasSite,
    );
  }

  String toString() {
    return "ContactsListFilter search:${this.search.vn}, isDemandCustomer:${isDemandCustomer.vn}, isOfferCustomer:${isOfferCustomer.vn}, isBankServicer:${isBankServicer.vn}, hasSite:${hasSite.vn}";
  }
}

extension ContactsSrv on ApiServices {
  Future<List<ContactDto>> listContacts({
    ContactsListFilter filter = const ContactsListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {
    final body = await apiGet(
      'agents/me/contacts',
      params: {
        if (filter.search.vn != null) "search": filter.search.v,
        if (filter.isDemandCustomer.vn != null) "isDemandCustomer": filter.isDemandCustomer.v,
        if (filter.isOfferCustomer.vn != null) "isOfferCustomer": filter.isOfferCustomer.v,
        if (filter.isBankServicer.vn != null) "isBankServicer": filter.isBankServicer.v,
        if (filter.hasSite.vn != null) "hasSite": filter.hasSite.v,
        "offset": offset,
        "limit": limit
      },
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => ContactDto.fromJson(item as Map<String, dynamic>)).toList();
  }

  Future<ContactDto?> getContact(String id) async {
    final result = await apiGetJson(
      'agents/me/contacts/$id',
      useToken: true,
      treat404AsNull: true,
    );
    if (result == null)
      return null;
    else
      return ContactDto.fromJson(result);
  }

  Future<ContactDto> postContact(ContactDto contact) async {
    final body = await apiPost('agents/me/contacts', data: contact, useToken: true);

    Map<String, dynamic> jsonBody = json.decode(body);
    return ContactDto.fromJson(jsonBody);
  }

  Future<ContactDto> putContact(ContactDto contact) async {
    assert(contact.id is! None);

    final body =
        await apiPut('agents/me/contacts/${contact.id.v}', data: contact.copyWith(id: None()), useToken: true);

    Map<String, dynamic> jsonBody = json.decode(body);
    return ContactDto.fromJson(jsonBody);
  }
}
