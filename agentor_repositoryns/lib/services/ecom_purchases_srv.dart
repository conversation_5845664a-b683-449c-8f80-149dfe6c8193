import 'dart:convert';
import 'package:agentor_repositoryns/agentor_repositoryns.dart';

enum EcomPurchasesListOrderBy {
  date_desc,
  date_asc,
}

class EcomPurchasesListFilter {
  final Optional<String> id;
  final Optional<bool> serviceActive;
  final Optional<String> buyerAgentId;
  final Optional<String> sellerAgentId;
  final Optional<String> productId;
  final EcomPurchasesListOrderBy orderBy;

  const EcomPurchasesListFilter({
    this.id = const None(),
    this.serviceActive = const None(),
    this.buyerAgentId = const None(),
    this.sellerAgentId = const None(),
    this.productId = const None(),
    this.orderBy = EcomPurchasesListOrderBy.date_desc,
  });

  EcomPurchasesListFilter copyWith({
    Optional<String>? id,
    Optional<bool>? serviceActive,
    Optional<String>? buyerAgentId,
    Optional<String>? sellerAgentId,
    Optional<String>? productId,
    EcomPurchasesListOrderBy? orderBy,
  }) {
    return EcomPurchasesListFilter(
      id: id ?? this.id,
      serviceActive: serviceActive ?? this.serviceActive,
      buyerAgentId: buyerAgentId ?? this.buyerAgentId,
      sellerAgentId: sellerAgentId ?? this.sellerAgentId,
      productId: productId ?? this.productId,
      orderBy: orderBy ?? this.orderBy,
    );
  }

  String toString() => "OffersListFilter  :$id, $serviceActive, $buyerAgentId, $sellerAgentId, $productId, $orderBy";
}

extension EcomPurchasesSrv on ApiServices {
  ///
  /// List purchases of the session agent (purchases of the ecom_account associated to the agent).
  /// Agent can act as seller or buyer... To filter "only selled" or "only buyed" you must use filter.sellerAgentId or filter.buyerAgentId respectively
  ///
  ///
  Future<List<EcomPurchaseDto>> listPurchases({
    EcomPurchasesListFilter filter = const EcomPurchasesListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {
    final body = await apiGet(
      'agents/me/ecom/purchases',
      params: {
        if (filter.buyerAgentId.vn != null) "buyer_agent_id": filter.buyerAgentId.v,
        if (filter.sellerAgentId.vn != null) "seller_agent_id": filter.buyerAgentId.v,
        if (filter.productId.vn != null) "product_id": filter.productId.v,
        if (filter.orderBy == EcomPurchasesListOrderBy.date_asc) "oby_date": "asc",
        if (filter.orderBy == EcomPurchasesListOrderBy.date_desc) "oby_date": "desc",
        "offset": offset,
        "limit": limit
      },
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => EcomPurchaseDto.fromJson(item as Map<String, dynamic>)).toList();
  }

  ///
  /// Gets actual token associated agent.
  ///
  Future<EcomAccountDto?> getMyEcomAccount() => _get("agents/me/my_ecom_account");

  Future<EcomAccountDto?> _get(String url) async {
    return apiGetJson(
      url,
      useToken: true,
      treat404AsNull: true,
    ).then(
      (json) => json == null ? null : EcomAccountDto.fromJson(json),
    );
  }
}
