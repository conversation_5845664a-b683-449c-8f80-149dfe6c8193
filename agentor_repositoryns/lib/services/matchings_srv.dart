
import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

class MatchingsListFilter {
  final Optional<String> offerId;
  final Optional<String> demandId;
  final Optional<bool> includeOfferData;
  final Optional<bool> includeDemandData;

  const MatchingsListFilter({
    this.offerId = const None(),
    this.demandId = const None(),
    this.includeOfferData = const None(),
    this.includeDemandData = const None(),
  });

  MatchingsListFilter copyWith({
    final Optional<String>? offerId,
    final Optional<String>? demandId,
    final Optional<bool>? includeOfferData,
    final Optional<bool>? includeDemandData,
  }) =>
      MatchingsListFilter(
        offerId: offerId ?? this.offerId,
        demandId: demandId ?? this.demandId,
        includeOfferData: includeOfferData ?? this.includeOfferData,
        includeDemandData: includeDemandData ?? this.includeDemandData,
      );

  String toString() =>
      "MatchingsListFilter offerId:$offerId, demandId:$demandId, includeOfferData:$includeOfferData, includeDemandData:$includeDemandData";
}

extension MatchingsSrv on ApiServices {
  Future<List<MatchingDto>> listMatchings({
    MatchingsListFilter filter = const MatchingsListFilter(),
    int offset = 0,
    int limit = 30,
  }) =>
      apiGetList(
        'agents/me/matchings',
        params: {
          if (filter.offerId is! None) "offer_id": filter.offerId.v,
          if (filter.demandId is! None) "demand_id": filter.demandId.v,
          if (filter.includeOfferData is! None) "include_offer_data": filter.includeOfferData.v,
          if (filter.includeDemandData is! None) "include_demand_data": filter.includeDemandData.v,
          "offset": offset,
          "limit": limit,
        },
        useToken: true,
      ).then((list) => list.map((dynamic item) => MatchingDto.fromJson(item as Map<String, dynamic>)).toList());

}
