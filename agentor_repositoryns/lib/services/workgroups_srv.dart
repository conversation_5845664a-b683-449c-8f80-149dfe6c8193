import 'dart:convert';
import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';



class WorkgroupsListFilter {
  final Optional<bool> includeCanRead;
  final Optional<bool> includeCanPublish;
  final Optional<bool> includeMine;
  final Optional<bool> includeNotMine;

  const WorkgroupsListFilter({
    this.includeCanRead = const None(),
    this.includeCanPublish = const None(),
    this.includeMine = const None(),
    this.includeNotMine = const None(),
  });

  WorkgroupsListFilter copyWith({
    Optional<bool>? includeCanRead,
    Optional<bool>? includeCanPublish,
    Optional<bool>? includeMine,
    Optional<bool>? includeNotMine,
  }) {
    return WorkgroupsListFilter(
      includeCanRead: includeCanRead ?? this.includeCanRead,
      includeCanPublish: includeCanPublish ?? this.includeCanPublish,
      includeMine: includeMine ?? this.includeMine,
      includeNotMine: includeNotMine ?? this.includeNotMine,
    );
  }

  String toString() => "WorkgroupsListFilter $includeCanRead, $includeCanPublish, $includeMine, $includeNotMine";
}

extension WorkgroupsSrv on ApiServices {
  Future<List<WorkgroupDto>> listWorkgroups({
    WorkgroupsListFilter filter = const WorkgroupsListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {
    
    final Map<String, dynamic> params = {
      if (filter.includeMine is! None) "include_mine": filter.includeMine.v,
      if (filter.includeNotMine is! None) "include_not_mine": filter.includeNotMine.v,
      if (filter.includeCanRead is! None) "include_can_read": filter.includeCanRead.v,
      if (filter.includeCanPublish is! None) "include_can_publish": filter.includeCanPublish.v,
      "offset": offset,
      "limit": limit
    };

    final body = await apiGet(
      'workgroups',
      params: params,
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => WorkgroupDto.fromJson(item as Map<String, dynamic>)).toList();
  }
}
