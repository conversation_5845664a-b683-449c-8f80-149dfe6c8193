import 'dart:convert';

import 'package:http/http.dart';

class ApiException implements Exception {
  /// HTTP status code
  final int statusCode;

  /// Descriptive message
  final String? message;

  /// Result code if pressent
  final String? code;

  ApiException(this.statusCode, [this.message, this.code]);
}

class ApiBadRequest extends ApiException {
  ApiBadRequest([message, code]) : super(400, message ?? "Bad request", code);
}

class ApiAcceptedRequest extends ApiException {
  ApiAcceptedRequest([message, code]): super(202, message ?? "Accepted", code);
}
class ApiUnauthorized extends ApiException {
  ApiUnauthorized([message, code]) : super(401, message ?? "Unauthorized", code);
}

class ApiForbiddenException extends ApiException {
  ApiForbiddenException([message, code]) : super(403, message ?? "Forbidden", code);
}

class ApiNotFoundException extends ApiException {
  ApiNotFoundException([message, code]) : super(404, message ?? "Not found", code);
}

class ApiUnprocessableEntity extends ApiException {
  ApiUnprocessableEntity([message, code]) : super(422, message ?? "Unprocessable entity", code);
}

class ApiInsufficientFunds extends ApiException {
  ApiInsufficientFunds([message, code]) : super(422, message ?? "Insufficient funds", code);
}

// Custom exceptions

class ApiEmailAlredyExistException extends ApiBadRequest {
  ApiEmailAlredyExistException([String? message, String? code]) : super(message, code);
}

class ApiMobileAlredyExistException extends ApiBadRequest {
  ApiMobileAlredyExistException([String? message, String? code]) : super(message, code);
}

class ApiUnknownValidationCodeException extends ApiBadRequest {
  ApiUnknownValidationCodeException([String? message, String? code]) : super(message, code);
}

extension HttpResponseApiException on Response {
  ApiException? toException([String? message]) {
    final contentType = this.headers["content-type"];
    if (contentType != null && contentType.contains("json") && this.body.length > 1 && this.body[0] == '{')
      try {
        Map<String, dynamic> jsonResponse = json.decode(this.body);
        return _toException(
          this.statusCode,
          message ?? jsonResponse["message"] ?? null,
          jsonResponse["code"] ?? null,
        );
      } on Exception {
        return _toException(this.statusCode, message);
      }
    else
      return _toException(this.statusCode, message);
  }
}

extension HttpStreamResponseApiException on StreamedResponse {
  ApiException? toException([String? message]) {
    return _toException(this.statusCode, message);
  }
}

ApiException? _toException(int statusCode, [String? message, String? code]) {
  switch (statusCode) {
    case 400:
      switch (code) {
        case "err_01": // const c_error_emailAlredyExists = "err_01";
          return ApiEmailAlredyExistException(message);
        case "err_02": // const c_error_unknownValidationcode = "err_02";
          return ApiUnknownValidationCodeException(message);
        case "err_03":
          return ApiMobileAlredyExistException(message);
        case "err_insufficientfunds":
          return ApiInsufficientFunds(message, code);
      }
      return ApiBadRequest(message, code);
    case 401:
      return ApiUnauthorized(message, code);
    case 403:
      return ApiForbiddenException(message, code);
    case 404:
      return ApiNotFoundException(message, code);
    case 422:
      return ApiUnprocessableEntity(message, code);
    case 200:
      return null;
    case 202:
      // OJO: No es un error, pero es la única forma depropagar que se trata de un código 202
      return ApiAcceptedRequest(message, code);
    default:
      if (statusCode > 200 && statusCode < 300) {
        throw ApiException(
            statusCode, "Unsuported API response code $statusCode: we don't know what to do with this.", code);
      } else {
        return ApiException(statusCode, message, code);
      }
  }
}
