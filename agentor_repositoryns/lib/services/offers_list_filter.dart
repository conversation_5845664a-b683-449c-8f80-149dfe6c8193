part of "offers_srv.dart";
class OffersListFilter {
  final String? search;
  final Optional<String> offerId;
  final Set<OfferstatusCode>? statuses;
  final Optional<bool> sourceIndividual;
  final Optional<bool> isVersion;
  final Optional<String> agentId;
  final Optional<String> customerId;
  final Optional<bool?> customerIsBankServicer;
  final Optional<bool> includeMine;
  final Optional<bool> includeNotMine;
  final Optional<bool> includeNotMineFavourites;
  final Optional<String> workgroupId;
  final Optional<String> containerzoneId;
  final Optional<String> propertytypeCode;
  final Optional<double> propertyM2Min;
  final Optional<double> propertyM2Max;
  final Optional<String> propertyAddrCityCode;
  final Optional<bool> saleAllowed;
  final Optional<double> saleAmountMin;
  final Optional<double> saleAmountMax;

  final Optional<bool> rentAllowed;
  final Optional<double> rentAmountMin;
  final Optional<double> rentAmountMax;

  const OffersListFilter({
    this.search,
    this.statuses = OfferstatusCodes.workingOnOnes,
    this.sourceIndividual = const None(),
    this.isVersion = const None(),
    this.offerId = const None(),
    this.agentId = const None(),
    this.customerId = const None(),
    this.customerIsBankServicer = const None(),
    this.workgroupId = const None(),
    this.includeMine = True,
    this.includeNotMine = True,
    this.includeNotMineFavourites = False,
    this.containerzoneId = const None(),
    this.propertytypeCode = const None(),
    this.saleAllowed = const None(),
    this.saleAmountMin = const None(),
    this.saleAmountMax = const None(),
    this.rentAllowed = const None(),
    this.rentAmountMin = const None(),
    this.rentAmountMax = const None(),
    this.propertyM2Min = const None(),
    this.propertyM2Max = const None(),
    this.propertyAddrCityCode = const None(),
  });

  OffersListFilter copyWith({
    String? search,
    Set<OfferstatusCode>? statuses,
    Optional<bool>? sourceIndividual,
    Optional<bool>? isVersion,
    Optional<String>? offerId,
    Optional<String>? agentId,
    Optional<String>? customerId,
    Optional<bool>? customerIsBankServicer,
    Optional<String>? workgroupId,
    Optional<bool>? includeMine,
    Optional<bool>? includeNotMine,
    Optional<bool>? includeNotMineFavourites,
    Optional<String>? containerzoneId,
    Optional<String>? propertytypeCode,
    Optional<double>? propertyM2Min,
    Optional<double>? propertyM2Max,
    Optional<bool>? saleAllowed,
    Optional<double>? saleAmountMin,
    Optional<double>? saleAmountMax,
    Optional<bool>? rentAllowed,
    Optional<double>? rentAmountMin,
    Optional<double>? rentAmountMax,
    Optional<String>? propertyAddrCityCode,
  }) {
    return OffersListFilter(
      search: search ?? this.search,
      statuses: statuses ?? this.statuses,
      sourceIndividual: sourceIndividual ?? this.sourceIndividual,
      isVersion: isVersion ?? this.isVersion,
      offerId: offerId ?? this.offerId,
      agentId: agentId ?? this.agentId,
      customerId: customerId ?? this.customerId,
      customerIsBankServicer:customerIsBankServicer?? this.customerIsBankServicer,
      workgroupId: workgroupId ?? this.workgroupId,
      includeMine: includeMine ?? this.includeMine,
      includeNotMine: includeNotMine ?? this.includeNotMine,
      includeNotMineFavourites: includeNotMineFavourites ?? this.includeNotMineFavourites,
      containerzoneId: containerzoneId ?? this.containerzoneId,
      propertytypeCode: propertytypeCode ?? this.propertytypeCode,
      propertyM2Min: propertyM2Min ?? this.propertyM2Min,
      propertyM2Max: propertyM2Max ?? this.propertyM2Max,
      saleAllowed: saleAllowed ?? this.saleAllowed,
      saleAmountMin: saleAmountMin ?? this.saleAmountMin,
      saleAmountMax: saleAmountMax ?? this.saleAmountMax,
      rentAllowed: rentAllowed ?? this.rentAllowed,
      rentAmountMin: rentAmountMin ?? this.rentAmountMin,
      rentAmountMax: rentAmountMax ?? this.rentAmountMax,
      propertyAddrCityCode: propertyAddrCityCode ?? this.propertyAddrCityCode,
    );
  }

  String toString() =>
      "OffersListFilter $search, :$statuses, ${sourceIndividual.vn}, ${isVersion.vn}, $propertytypeCode, $includeMine, $includeNotMine, $containerzoneId, $propertytypeCode, $saleAllowed, $rentAllowed, $propertyAddrCityCode";
}
