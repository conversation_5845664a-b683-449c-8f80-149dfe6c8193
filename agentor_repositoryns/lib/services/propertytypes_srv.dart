

import 'dart:convert';

import 'package:agentor_repositoryns/models/propertytype_dto.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

class PropertytypesListFilter {
  final Optional<String> code;
  final Optional<bool> includeSubtypes;

  const PropertytypesListFilter({this.code = const None(), this.includeSubtypes = False});

  PropertytypesListFilter copyWith({Optional<String>? code, Optional<bool>? includeSubtypes}) {
    return PropertytypesListFilter(
      code: code ?? this.code,
      includeSubtypes: includeSubtypes ?? this.includeSubtypes,
    );
  }

  String toString() {
    return "PropertytypesListFilter code:$code";
  }
}

extension PropertytypesSrv on ApiServices {
  
  Future<List<PropertytypeDto>> listPropertytypes({
    PropertytypesListFilter filter = const PropertytypesListFilter(),
  }) async {
    final body = await apiGet(
      'propertytypes',
      params: {
        if (filter.code is! None) "code": filter.code.v,
        if (filter.includeSubtypes is! None) "include_subtypes": filter.includeSubtypes.v,
      },
      useToken: true,
    );

    return _from(body);
  }

  List<PropertytypeDto> _from(String body) {
    List jsonBody = json.decode(body);
    return jsonBody.map((v) => PropertytypeDto.fromJson(v)).toList();
  }
}
