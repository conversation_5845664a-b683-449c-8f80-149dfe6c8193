import 'dart:convert';
import 'dart:typed_data';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';
import '../models/models.dart';

part "offers_list_filter.dart";

extension OffersSrv on ApiServices {
  Future<OfferDto> postOffer(OfferDto offer) async {
    final body = await apiPost(
      'agents/me/offers',
      data: offer.copyWith(
        agent: const None(),
        id: const None(),
        property: offer.property.map((p) => p.copyWith(id: None())),
      ),
      useToken: true,
    );

    Map<String, dynamic> jsonBody = json.decode(body);
    return OfferDto.fromJson(jsonBody);
  }

  Future<OfferDto> putOffer(OfferDto offer) async {
    assert(offer.id.vn != null);

    final body = await apiPut(
      'agents/me/offers/${offer.id.v}',
      data: _cleanupForUp(offer),
      useToken: true,
    );

    Map<String, dynamic> jsonBody = json.decode(body);
    return OfferDto.fromJson(jsonBody);
  }

  OfferDto _cleanupForUp(OfferDto offer) {
    final cleanedProperty = offer.property.map(
      (p) => p.copyWith(
        // Eliminar identificador del inmueble
        id: None(),
        // Eliminar identificador del inmueble de los propertymedias
        propertymedias: p.propertymedias.mapList(
          (pm) => pm.copyWith(
            property: const None(),
          ),
        ),
      ),
    );

    final cleanedVersion = offer.version.map(
      (p) => p?.copyWith(
        // No se puede cambiar ni la oferta origen ni el tipo de version
        of: None(),
        type: None(),
      ),
    );

    return offer.copyWith(
        id: None(),
        // null | None | {id}
        customer: offer.customer.map((c) => c == null ? null : c.copyIdOnly()),
        version: cleanedVersion,
        // No debemos tocar el agente
        agent: None(),
        // No debemos tocar el source
        source: None(),
        property: cleanedProperty);
  }

  ///
  /// Gets an agent offer
  /// [notFoundAsNull] indicates how to treat 404 (not found) response code:   if [true] function will return [null], otherwise function will trhow an error
  ///
  Future<OfferDto?> getOffer(String offerId, {bool ensure_last_version = false}) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");

    final result = await apiGetJson(
        'agents/me/offers/${offerId.trim()}${ensure_last_version ? '?ensure_last_version=true' : ''}',
        useToken: true,
        treat404AsNull: true);
    return result == null ? null : OfferDto.fromJson(result);
  }

  Future<List<OfferDto>> listOffers({
    OffersListFilter filter = const OffersListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {
    final Map<String, dynamic> params = {
      if (filter.search != null) "search": filter.search,
      if (filter.statuses != null) "status_codes": (filter.statuses ?? {}).map((e) => e.toJsonString()).join(","),
      if (filter.isVersion is! None) "isVersion": filter.isVersion.v,
      if (filter.sourceIndividual is! None) "source_individual": filter.sourceIndividual.v,
      if (filter.agentId is! None) "agent_id": filter.agentId.v,
      if (filter.offerId is! None) "id": filter.offerId.v,
      if (filter.customerId is! None) "customer_id": filter.customerId.v,
      if (filter.customerIsBankServicer is! None) "customer_isBankServicer": filter.customerIsBankServicer.v,
      if (filter.workgroupId is! None) "workgroup_id": filter.workgroupId.v,
      if (filter.includeMine is! None) "include_mine": filter.includeMine.v,
      if (filter.includeNotMine is! None) "include_not_mine": filter.includeNotMine.v,
      if (filter.includeNotMineFavourites is! None) "include_not_mine_fav": filter.includeNotMineFavourites.v,
      if (filter.containerzoneId is! None) "containerzone_id": filter.containerzoneId.v,
      if (filter.propertytypeCode is! None) "property_type_code": filter.propertytypeCode.v,
      if (filter.rentAllowed is! None) "rent_allowed": filter.rentAllowed.v,
      if (filter.rentAmountMin is! None) "rent_amount_min": filter.rentAmountMin.v,
      if (filter.rentAmountMax is! None) "rent_amount_max": filter.rentAmountMax.v,
      if (filter.saleAllowed is! None) "sale_allowed": filter.saleAllowed.v,
      if (filter.saleAmountMin is! None) "sale_amount_min": filter.saleAmountMin.v,
      if (filter.saleAmountMax is! None) "sale_amount_max": filter.saleAmountMax.v,
      if (filter.propertyM2Min is! None) "property_m2_min": filter.propertyM2Min.v,
      if (filter.propertyM2Max is! None) "property_m2_max": filter.propertyM2Max.v,
      if (filter.propertyAddrCityCode is! None) "property_address_city_code": filter.propertyAddrCityCode.v,
      "offset": offset,
      "limit": limit
    };
    final body = await apiGet(
      'agents/me/offers',
      params: params,
      useToken: true,
    );

    List<dynamic> jsonBody = json.decode(body);
    return jsonBody.map((dynamic item) => OfferDto.fromJson(item as Map<String, dynamic>)).toList();
  }

  Future<void> addOfferToMyFavourites(String offerId) => apiPost('agents/me/favourite_offers', data: {"id": offerId});

  Future<void> removeOfferFromMyFavourites(String offerId) => apiDel('agents/me/favourite_offers/$offerId');

  Future<bool> isOfferInMyFavourites(String offerId) async {
    final body = await apiGet('agents/me/offers/$offerId/is_favourite');
    bool result = json.decode(body);
    return result;
  }

  Future<PropertymediaDto> uploadOfferPropertyMedia(
      String offerId, Uint8List content, String? filename, String mimetype) async {
    final body = await apiUpload('agents/me/offers/${offerId.trim()}/property/medias', content, mimetype,
        filename: filename, useToken: true);
    Map<String, dynamic> jsonBody = json.decode(body);
    return PropertymediaDto.fromJson(jsonBody);
  }

  Future<List<PropertymediaDto>> listOfferPropertymedias(String offerId, {bool notFoundAsNull = false}) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");
    final body = await apiGet('agents/me/offers/${offerId.trim()}/property/medias', useToken: true);
    List<dynamic> jsonResponse = json.decode(body);
    return jsonResponse.map((dynamic item) => PropertymediaDto.fromJson(item as Map<String, dynamic>)).toList();
  }

  Future<int> deleteOfferPropertymedia(String offerId, String mediaKey) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");
    assert(mediaKey.trim().length != 0, "mediaKey must be a non empty string");
    final body = await apiDel('agents/me/offers/${offerId.trim()}/property/medias/${mediaKey.trim()}', useToken: true);
    return json.decode(body);
  }

  Future<PropertymediaDto> updateOfferPropertymedia(
      String offerId, String mediaKey, PropertymediaDto propertymedia) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");
    assert(mediaKey.trim().length != 0, "mediaKey must be a non empty string");
    final body = await apiPut(
      'agents/me/offers/${offerId.trim()}/property/medias/${mediaKey.trim()}',
      data: propertymedia,
      useToken: true,
    );
    return PropertymediaDto.fromJson(json.decode(body));
  }

  Future<WorkgroupDto> publishOffer(String offerId, String workgroupId) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");
    assert(workgroupId.trim().length != 0, "workgroupId must be a non empty string");

    final body = await apiPost(
      'agents/me/offers/${offerId.trim()}/publications',
      data: WorkgroupDto(
        id: Some(workgroupId),
      ),
    );
    final result = WorkgroupDto.fromJson(json.decode(body));
    return result;
  }

  Future<List<WorkgroupDto>> listOfferPublications(String offerId) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");
    final body = await apiGet('agents/me/offers/${offerId.trim()}/publications', useToken: true);

    return (json.decode(body) as List<dynamic>)
        .map((dynamic item) => WorkgroupDto.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<int> unpublishOffer(String offerId, String workgroupId) async {
    assert(offerId.trim().length != 0, "Offer id must be a non empty string");
    assert(workgroupId.trim().length != 0, "workgroupId must be a non empty string");

    final body = await apiDel('agents/me/offers/${offerId.trim()}/publications/${workgroupId.trim()}', useToken: true);
    final result = json.decode(body);
    return result;
  }
}
