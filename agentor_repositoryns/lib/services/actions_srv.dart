import 'dart:convert';

import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/services/services.dart';
import 'package:agentor_repositoryns/optional.dart';

class ActionsListFilter {
  final Optional<String> typeId;
  final Optional<bool?> done;
  final Optional<String?> offerId;
  final Optional<String> contactId;
  final Optional<DateTime> minWhen;
  final Optional<DateTime> maxWhen;
  final Optional<AscOrDesc> orderbyWhen;
  final Optional<bool> includeContactDetails;
  final Optional<bool> includeOfferDetails;

  const ActionsListFilter({
    this.typeId = const None(),
    this.done = const None(),
    this.offerId = const None(),
    this.contactId = const None(),
    this.minWhen = const None(),
    this.maxWhen = const None(),
    this.orderbyWhen = const None(),
    this.includeContactDetails = True,
    this.includeOfferDetails = False,
  });

  ActionsListFilter copyWith({
    Optional<String>? typeId,
    Optional<bool>? done,
    Optional<String>? offerId,
    Optional<String>? contactId,
    Optional<DateTime>? minWhen,
    Optional<DateTime>? maxWhen,
    Optional<AscOrDesc>? orderByWhen,
    Optional<bool>? includeContactDetails,
    Optional<bool>? includeOfferDetails,
  }) =>
      ActionsListFilter(
        typeId: typeId ?? this.typeId,
        done: done ?? this.done,
        offerId: offerId ?? this.offerId,
        contactId: contactId ?? this.contactId,
        minWhen: minWhen ?? this.minWhen,
        maxWhen: maxWhen ?? this.maxWhen,
        orderbyWhen: orderByWhen ?? this.orderbyWhen,
        includeContactDetails: includeContactDetails ?? this.includeContactDetails,
        includeOfferDetails: includeOfferDetails ?? this.includeOfferDetails,
      );

  String toString() =>
      "ActionsListFilter typeId:$typeId, done:$done, offerId:$offerId, contactId:$contactId, minWhen:$minWhen, maxWhen:$maxWhen";
}

extension ActionsSrv on ApiServices {
  Future<List<ActionDto>> listActions({
    ActionsListFilter filter = const ActionsListFilter(),
    int offset = 0,
    int limit = 30,
  }) async {
    final list = await apiGetList(
      'agents/me/actions',
      params: {
        // El valor null es un valor válido (acciones sin oferta)
        if (filter.offerId is! None) "offer_id": filter.offerId.v,
        // El valor null es un valor válido (acciones sin contacto)
        if (filter.contactId is! None) "contact_id": filter.contactId.v,
        if (filter.typeId is! None) "type_id": filter.typeId.v,
        if (filter.done is! None) "done": filter.done.v,
        if (filter.minWhen is! None) "when_min": filter.minWhen.v,
        if (filter.maxWhen is! None) "when_max": filter.maxWhen.v,
        if (filter.orderbyWhen is! None) "oby_when": filter.orderbyWhen.v == AscOrDesc.desc ? "desc" : "asc",
        "details": [
          if (filter.includeOfferDetails.vn ?? false) "offer",
          if (filter.includeContactDetails.vn ?? false) "contact",
        ],
        "offset": offset,
        "limit": limit,
      },
      useToken: true,
    );

    return list.map((dynamic item) => ActionDto.fromJson(item as Map<String, dynamic>)).toList();
  }

  Future<ActionDto?> getAction(
    String id, {
    bool includeOfferDetails = true,
    bool includeContactDetails = true,
  }) {
    return apiGetJson(
      'agents/me/actions/$id',
      useToken: true,
      treat404AsNull: true,
      params: {
        "details": [
          if (includeOfferDetails) "offer",
          if (includeContactDetails) "contact",
        ],
      },
    ).then((json) => json == null ? null : ActionDto.fromJson(json));
  }

  Future<ActionDto> postAction(ActionDto action) => apiPost(
        'agents/me/actions',
        data: _optimizeForUpCr(action),
        useToken: true,
      ).then((body) => json.decode(body) as Map<String, dynamic>).then((map) => ActionDto.fromJson(map));

  Future<ActionDto> putAction(ActionDto action) async {
    assert(action.id is! None);
    return apiPut(
      'agents/me/actions/${action.id.v}',
      data: _optimizeForUpCr(action),
      useToken: true,
    ).then((body) => json.decode(body) as Map<String, dynamic>).then((json) => ActionDto.fromJson(json));
  }

  ActionDto _optimizeForUpCr(ActionDto action) {
    return action.copyWith(
      // id  no viaja en el cuerpo... sino como parte de la URL cuando es necesario
      id: None(),
      // Optimización: solo enviamos id del tipo
      type: action.type.map((t) => t.copyIdOnly()),
      // Optimización: Evitamos enviar toda la oferta... se envía null | None | {id}
      offer: action.offer.map((o) => o == null ? null : o.copyIdOnly()),
      // Optimización: Evitamos enviar todo el contacto... se envía null | None | {id}
      contact: action.contact.map((c) => c == null ? null : c.copyIdOnly()),
    );
  }
}
