import 'dart:convert';
import 'dart:typed_data';

import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import './api_exception.dart';

class ApiServices {
  final String baseUrl;
  String _userToken = "";

  ApiServices({
    this.baseUrl = '/api/',
  });

  String get userToken {
    return this._userToken;
  }

  set userToken(String newValue) {
    this._userToken = newValue;
  }

  String getApiUrl(String subUrl, {Map<String, dynamic> params = const {}}) {
    String strParams = params.keys.map((key) {
      var v = params[key];
      var s = (v is List) ? v.map(valueToString).join(",") : valueToString(v);
      return "$key=${Uri.encodeQueryComponent(s)}";
    }).join("&");

    //return 'http://localhost:8000/api/$subUrl';
    return '$baseUrl$subUrl${strParams.length != 0 ? "?$strParams" : ""}';
  }

  String valueToString(dynamic value) {
    return (value is DateTime) ? value.toUtc().toIso8601String() : "$value";
  }

  Future<String> apiGet(
    String subUrl, {
    Map<String, dynamic> params = const {},
    bool useToken = true,
    String? token,
  }) async {
    String url = getApiUrl(subUrl, params: params);

    final response = await http.get(Uri.parse(url), headers: {
      if (useToken) 'Authorization': 'Bearer ${token ?? _userToken}',
    });
    final apiException = response.toException();
    if (apiException != null) {
      throw apiException;
    } else {
      return response.body;
    }
  }

  Future<List> apiGetList(
    String subUrl, {
    Map<String, dynamic> params = const {},
    bool useToken = true,
    String? token,
  }) async =>
      json.decode(await apiGet(subUrl, params: params, useToken: useToken, token: token)) as List;

  Future<String> apiDel(String subUrl, {bool useToken = true, String? token}) async {
    String url = getApiUrl(subUrl);
    final response = await http.delete(Uri.parse(url), headers: {
      if (useToken) 'Authorization': 'Bearer ${token ?? _userToken}',
    });
    final apiException = response.toException();
    if (apiException != null) {
      throw apiException;
    } else {
      return response.body;
    }
  }


  Future<Map<String, dynamic>?> apiGetJson(
    String subUrl, {
    Map<String, dynamic> params = const {},
    bool useToken = true,
    bool treat404AsNull = true,
  }) async {
    String url = getApiUrl(subUrl, params: params);
    var response = await http.get(Uri.parse(url), headers: {
      if (useToken) 'Authorization': 'Bearer $_userToken',
    });

    final exception = response.toException('Failed to get json resource (statusCode ${response.statusCode})');
    if (exception == null) {
      Map<String, dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse;
    } else if (response.statusCode == 404 && treat404AsNull)
      return null;
    else
      throw exception;
  }

  // OneTimeApi
  Future<Map<String, dynamic>?> apiOTGetJson(
    String subUrl, {
    bool treat404AsNull = false,
  }) async {
    try {
      var uri = Uri.https('link.topbrokers.io', '/onetime/$subUrl');
      //print("apiOTGetJson url: $uri");
      var response = await http.get(uri);
      //print("apiOTGetJson response: ${response.body} statusCode: ${response.statusCode}");
      if (response.statusCode == 200) {
        Map<String, dynamic> jsonResponse = json.decode(response.body);
        return jsonResponse;
      } else if (response.statusCode == 404 && treat404AsNull) {
        return null;
      } else if (response.statusCode == 404) {
        throw ApiException(response.statusCode, 'código erróneo', null);
      } else {
        // Extract error message from response body if available
        String? errorMessage = json.decode(response.body)['message'];
        print("apiOTGetJson errorMessage: $errorMessage");
        throw ApiException(response.statusCode, errorMessage, null);
      }
    } on FormatException catch (e) {
      // Handle FormatException
      print("apiOTGetJson FormatException: ${e.message}");
      throw ApiException(0, 'FormatException: ${e.message}', null);
    } catch (e) {
      // Handle other exceptions
      print("apiOTGetJson error: $e");
      throw ApiException(0, e.toString(), null);
    }
  }


  Future<Map<String, dynamic>> apiOTPostJson(String subUrl, {dynamic data}) async {
    var uri = Uri.https('link.topbrokers.io', '/onetime/$subUrl');
    final response = await http.post(uri, body: data != null ? json.encode(data) : null, headers: {
      "content-type": "application/json",
    });

    final ex = response.toException();
    if (ex == null) {
      Map<String, dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse;
    } else {
      throw ex;
    }
  }

  Future<String> apiPost(String subUrl, {dynamic data, bool useToken = true}) async {
    final url = getApiUrl(subUrl);
    final body = data != null ? json.encode(data) : null;
    final response = await http.post(Uri.parse(url), body: body, headers: {
      "content-type": "application/json",
      if (useToken) 'Authorization': 'Bearer $_userToken',
    });

    final apiException = response.toException();
    if (apiException != null)
      throw apiException;
    else
      return response.body;
  }

  Future<Map<String, dynamic>> apiPostJson(String subUrl, {dynamic data, bool useToken = true}) async {
    String url = getApiUrl(subUrl);
    final response = await http.post(Uri.parse(url), body: data != null ? json.encode(data) : null, headers: {
      "content-type": "application/json",
      if (useToken) 'Authorization': 'Bearer $_userToken',
    });

    final ex = response.toException();
    if (ex == null) {
      Map<String, dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse;
    } else {
      throw ex;
    }
  }

  Future<dynamic> apiUpload(String subUrl, Uint8List content, String mimetype,
      {String? filename, bool useToken = true}) async {
    try {
      String url = getApiUrl(subUrl);
      var request = http.MultipartRequest("POST", Uri.parse(url));
      if (useToken) {
        request.headers.addAll({'Authorization': 'Bearer $_userToken'});
      }
      request.files.add(
          http.MultipartFile.fromBytes("file", content, filename: filename, contentType: MediaType.parse(mimetype)));
      final strmResponse = await request.send();
      final apiException = strmResponse.toException();
      if (apiException != null) {
        throw apiException;
      } else {
        final response = await http.Response.fromStream(strmResponse);
        return response.body;
      }
    } catch (e) {
      print("Error!!! $e");
      throw e;
    }
  }

  Future<String> apiPut(String subUrl, {Object? data, bool useToken = true, String? token}) async {
    String url = getApiUrl(subUrl);
    String? body;
    try {
      body = data != null ? json.encode(data) : null;
    } catch(e){
      
      throw e;
    }
    final response = await http.put(Uri.parse(url), body: body, headers: {
      "content-type": "application/json",
      if (useToken) 'Authorization': 'Bearer ${token ?? _userToken}',
    });
    final apiException = response.toException();
    if (apiException != null)
      throw apiException;
    else
      return response.body;
  }

  Future<Map<String, dynamic>> apiPutJson(String subUrl, {dynamic data, bool useToken = true, String? token}) async {
    String url = getApiUrl(subUrl);
    final response = await http.put(Uri.parse(url), body: data != null ? json.encode(data) : null, headers: {
      "content-type": "application/json",
      if (useToken) 'Authorization': 'Bearer ${token ?? _userToken}}',
    });
    final ex = response.toException('Failed to put json resource (statusCode ${response.statusCode})');
    if (ex == null) {
      Map<String, dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse;
    } else {
      throw ex;
    }
  }
  //return 'http://localhost:8000/api/$subUrl';
  // return '/api/$subUrl';
}
