import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';

const _C_code = "type";
const _C_label = "label";
const _C_disclaimer = "disclaimer";

///
/// Un servicio que puede ser solicitado por un agente
/// Actualmente es una entidad débil asociada al tipo de acción (indica que la acción de ese tipo se refiere a una solicitud de servicio externo)
///
class ServiceDto {
  Optional<String> code;
  Optional<MultilingualStrDto> label;
  Optional<DisclaimerDto?> disclaimer;

  ServiceDto({
    this.code = const None(),
    this.label = const None(),
    this.disclaimer = const None(),
  });

  factory ServiceDto.fromJson(Map json) {
    return ServiceDto(
      code: json.keyToOptional(_C_code),
      label: json.keyToOptional<MultilingualStrDto>(_C_code, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      disclaimer: json.keyToOptional(_C_disclaimer, fTransform: (v) => DisclaimerDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_code: code.v,
      if (label is! None) _C_label: label.v.toJson(),
      if (disclaimer is! None) _C_disclaimer: disclaimer.v?.toJson(),
    };
  }
}
