import 'package:agentor_repositoryns/optional.dart';

const String _c_username = "username";
const String _c_password = "password";

class ScrtyCredentialsDto {
  Optional<String> username;
  Optional<String> password;

  ScrtyCredentialsDto({
    this.username = const None(),
    this.password = const None(),
  });

  factory ScrtyCredentialsDto.fromJson(Map json) => ScrtyCredentialsDto(
        username: json.keyToOptional(_c_username),
        password: json.keyToOptional(_c_password),
      );

  Map<String, dynamic> toJson() => ({
        if (username is! None) _c_username: username.v,
        if (password is! None) _c_password: password.v,
      });

  ScrtyCredentialsDto copyWith({
    Optional<String>? username,
    Optional<String>? password,
  }) =>
      ScrtyCredentialsDto(
        username: username ?? this.username,
        password: password ?? this.password,
      );
}
