import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';


class StreettypeDto {
  static const _C_ID = "id";
  static const _C_LABEL = "label";

  Optional<String> id;
  Optional<MultilingualStrDto> label;
  StreettypeDto({
    this.id = const None(),
    this.label = const None(),
  });

  factory StreettypeDto.fromJson(Map json) {
    return StreettypeDto(
      id: json.keyToOptional(_C_ID),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _C_ID: id.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }

}
