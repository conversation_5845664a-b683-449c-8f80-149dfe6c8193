import 'package:agentor_repositoryns/optional.dart';



class PropertyzoneDto {
  static const _C_ID = "id";
  static const _C_PARENT = "parent";
  static const _C_NAME = "name";
  static const _C_HAS_CHILDREN = "hasChildren";

  Optional<String> id;
  Optional<String> name;
  Optional<PropertyzoneDto?> parent;
  Optional<bool> hasChildren;

  PropertyzoneDto({
    this.id = const None(),
    this.name = const None(),
    this.parent = const None(),
    this.hasChildren = const None(),
  });

  String get composedName {
    return [
      if (parent.vn?.name.vn != null) "${parent.v!.name.v}",
      if (name.vn != null) name.v,
    ].join("/");
  }

  factory PropertyzoneDto.fromJson(Map json) {
    return PropertyzoneDto(
      id: json.keyToOptional(_C_ID),
      name: json.keyToOptional(_C_NAME),
      parent: json.keyToOptional(_C_PARENT, fTransform: (v) => PropertyzoneDto.fromJson(v)),
      hasChildren: json.keyToOptional(_C_HAS_CHILDREN),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _C_ID: id.v,
      if (name is! None) _C_NAME: name.v,
      if (parent is! None) _C_PARENT: parent.v?.toJson(),
      if (hasChildren is! None) _C_HAS_CHILDREN: hasChildren.v,
    };
  }
}
