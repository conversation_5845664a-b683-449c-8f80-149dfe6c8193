import 'package:agentor_repositoryns/models/agent_dto.dart';
import 'package:agentor_repositoryns/models/workgroup_dto.dart';
import 'package:agentor_repositoryns/optional.dart';


class WorkgroupMemberPermissions {
  static const String _c_read = "read";
  static const String _c_publish = " publish";

  Optional<bool> read;
  Optional<bool> publish;

  WorkgroupMemberPermissions({
    this.read = const None(),
    this.publish = const None(),
  });
  WorkgroupMemberPermissions copyWith({
    Optional<bool>? read,
    Optional<bool>? publish,
  }) {
    return WorkgroupMemberPermissions(
      read: read ?? this.read,
      publish: publish ?? this.publish,
    );
  }

  factory WorkgroupMemberPermissions.fromJson(Map json) {
    return WorkgroupMemberPermissions(
      read: json.keyToOptional(_c_read),
      publish: json.keyToOptional(_c_publish),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (read is! None) _c_read: read.v,
      if (publish is! None) _c_publish: publish.v,
    };
  }
}

class WorkgroupMemberDto {
  static const String _c_workgroup = "workgroup";
  static const String _c_agent = "agent";
  static const String _c_can = "can";

  Optional<WorkgroupDto> workgroup;
  Optional<AgentDto> agent;
  Optional<WorkgroupMemberPermissions> can;

  WorkgroupMemberDto({
    this.workgroup = const None(),
    this.agent = const None(),
    this.can = const None(),
  });

  WorkgroupMemberDto copyWith({
    Optional<WorkgroupDto>? workgroup,
    Optional<AgentDto>? agent,
    Optional<WorkgroupMemberPermissions>? can,
  }) {
    return WorkgroupMemberDto(
      workgroup: workgroup ?? this.workgroup,
      agent: agent ?? this.agent,
      can: can ?? this.can,
    );
  }

  factory WorkgroupMemberDto.fromJson(Map json) {
    return WorkgroupMemberDto(
      workgroup: json.keyToOptional(_c_workgroup, fTransform: (v) => WorkgroupDto.fromJson(v)),
      agent: json.keyToOptional(_c_agent, fTransform: (v) => AgentDto.fromJson(v)),
      can: json.keyToOptional(_c_can, fTransform: (v) => WorkgroupMemberPermissions.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (workgroup is! None) _c_workgroup: workgroup.v.toJson(),
      if (agent is! None) _c_agent: agent.v.toJson(),
      if (can is! None) _c_can: can.v.toJson(),
    };
  }
}
