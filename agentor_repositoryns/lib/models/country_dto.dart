import 'package:agentor_repositoryns/optional.dart';

import 'multilingual_str_dto.dart';

const _C_LABEL = "label";
const _C_CODE = "code";

class CountryDto {
  Optional<String> code;
  Optional<MultilingualStrDto> label;

  CountryDto({
    this.code = const None(),
    this.label = const None(),
  });

  factory CountryDto.fromJson(Map json) {
    return CountryDto(
      code: json.keyToOptional(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }

}
