import 'package:agentor_repositoryns/optional.dart';

class OfferRentDto {
  static const _c_allowed = "allowed";
  static const _c_amount = "amount";
  static const _c_marketAmount = "marketAmount";

  Optional<bool> allowed;
  Optional<double?> amount;
  Optional<double?> marketAmount;

  OfferRentDto({
    this.allowed = const None(),
    this.amount = const None(),
    this.marketAmount = const None(),
  });

  factory OfferRentDto.emptyRent() => OfferRentDto.fromJson({'$_c_allowed': true, '$_c_amount': 0.0});

  factory OfferRentDto.emptyNoRent() => OfferRentDto.fromJson({'$_c_allowed': false});

  factory OfferRentDto.fromJson(Map data) {
    return OfferRentDto(
      allowed: data.keyToOptional<bool>(_c_allowed),
      amount: data.keyToOptional<double?>(_c_amount),
      marketAmount: data.keyToOptional<double?>(_c_marketAmount),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (allowed is! None) _c_allowed: allowed.v,
      if (amount is! None) _c_amount: amount.v,
      if (marketAmount is! None) _c_marketAmount: marketAmount.v,
    };
  }
}
