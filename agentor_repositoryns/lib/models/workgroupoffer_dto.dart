import 'package:agentor_repositoryns/models/offer_dto.dart';
import 'package:agentor_repositoryns/models/workgroupmember_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class WorkgroupOfferDto {
  static const String _c_member = "member";
  static const String _c_offer = "offer";

  Optional<WorkgroupMemberDto> member;
  Optional<OfferDto> offer;

  WorkgroupOfferDto({
    this.member = const None(),
    this.offer = const None(),
  });

  WorkgroupOfferDto copyWith({
    Optional<WorkgroupMemberDto>? member,
    Optional<OfferDto>? offer,
  }) {
    return WorkgroupOfferDto(
      member: member ?? this.member,
      offer: offer ?? this.offer,
    );
  }

  factory WorkgroupOfferDto.fromJson(Map json) {
    return WorkgroupOfferDto(
      member: json.keyToOptional(_c_member, fTransform: (v) => WorkgroupMemberDto.fromJson(v)),
      offer: json.keyToOptional(_c_offer, fTransform: (v) => OfferDto.from<PERSON>son(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (member is! None) _c_member: member.v.toJson(),
      if (offer is! None) _c_offer: offer.v.toJson(),
    };
  }
}
