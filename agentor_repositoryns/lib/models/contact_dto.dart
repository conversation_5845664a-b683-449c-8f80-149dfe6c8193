import 'package:agentor_repositoryns/optional.dart';

const String _c_id = "id";
const String _c_firstName = "firstName";
const String _c_lastName = "lastName";
const String _c_email = "email";
const String _c_mobile = "mobile";
const String _c_isBankServicer = "isBankServicer";
const String _c_notes = "notes";
const String _c_siteSlug = "siteSlug";

class ContactDto {
  Optional<String> id;
  Optional<String> firstName;
  Optional<String?> lastName;
  Optional<String?> email;
  Optional<String?> mobile;
  Optional<bool> isBankServicer;
  Optional<String?> notes;
  Optional<String?> siteSlug;

  ContactDto({
    this.id = const None(),
    this.firstName = const None(),
    this.lastName = const None(),
    this.email = const None(),
    this.mobile = const None(),
    this.isBankServicer = const None(),
    this.notes = const None(),
    this.siteSlug = const None(),
  });

  Optional<String> get name {
    if (firstName.vn != null && lastName.vn != null)
      return Some("${firstName.vn} ${lastName.vn}");
    else if (firstName.vn != null)
      return firstName;
    else if (lastName.vn != null)
      return Some(lastName.vn as String);
    else
      return None();
  }

  factory ContactDto.fromJson(Map json) {
    return ContactDto(
      id: json.keyToOptional(_c_id),
      firstName: json.keyToOptional(_c_firstName),
      lastName: json.keyToOptional(_c_lastName),
      email: json.keyToOptional(_c_email),
      mobile: json.keyToOptional(_c_mobile),
      isBankServicer: json.keyToOptional(_c_isBankServicer),
      notes: json.keyToOptional(_c_notes),
      siteSlug: json.keyToOptional(_c_siteSlug)
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (firstName is! None) _c_firstName: firstName.v,
      if (lastName is! None) _c_lastName: lastName.v,
      if (email is! None) _c_email: email.v,
      if (mobile is! None) _c_mobile: mobile.v,
      if (isBankServicer is! None) _c_isBankServicer: isBankServicer.v,
      if (notes is! None) _c_notes: notes.v,
      if (siteSlug is! None) _c_siteSlug: siteSlug.v
    };
  }
  ContactDto copyIdOnly() => ContactDto(id:this.id);
  ContactDto copyWith({
    Optional<String>? id,
    Optional<String>? firstName,
    Optional<String?>? lastName,
    Optional<String?>? email,
    Optional<String?>? mobile,
    Optional<bool>? isBankServicer,
    Optional<String?>? notes,
    Optional<String?>? siteSlug,
  }) {
    return ContactDto(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      isBankServicer: isBankServicer ?? this.isBankServicer,
      notes: notes ?? this.notes,
      siteSlug: siteSlug ?? this.siteSlug,
    );
  }
}
