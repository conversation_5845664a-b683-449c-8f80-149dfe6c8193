import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/models/propertytype_dto.dart';
import 'package:agentor_repositoryns/optional.dart';


class PropertysubtypeDto {
  static const _C_CODE = "code";
  static const _C_LABEL = "label";
  static const _C_TYPE = "subtypes";

  Optional<String> code;
  Optional<MultilingualStrDto> label;
  Optional<PropertytypeDto> type;
  PropertysubtypeDto({
    this.code = const None(),
    this.label = const None(),
    this.type = const None(),
  });

  factory PropertysubtypeDto.fromJson(Map json) {
    return PropertysubtypeDto(
      code: json.keyToOptional(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      type: json.keyToOptional(_C_TYPE, fTransform: (v) => PropertytypeDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
      if (type is! None) _C_TYPE: type.v.toJson(),
    };
  }

}
