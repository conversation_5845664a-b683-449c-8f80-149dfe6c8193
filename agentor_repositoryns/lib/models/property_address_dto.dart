import 'package:agentor_repositoryns/models/city_dto.dart';
import 'package:agentor_repositoryns/models/streettype_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

const _C_CITY = "city";
const _C_STREETTYPE = "streettype";
const _C_STREETNAME = "streetname";
const _C_NUMBER = "number";
const _C_DETAIL = "detail";
const _C_POSTCODE = "postcode";

class PropertyAddressDto {
  Optional<CityDto> city;
  Optional<StreettypeDto?> streettype;
  Optional<String?> streetname;
  Optional<String?> number;
  Optional<String?> detail;
  Optional<String?> postcode;

  PropertyAddressDto({
    this.city = const None(),
    this.streettype = const None(),
    this.streetname = const None(),
    this.number = const None(),
    this.detail = const None(),
    this.postcode = const None(),
  });

  static PropertyAddressDto fromJson(Map<dynamic, dynamic> json) {
    return PropertyAddressDto(
      city: json.keyToOptional(_C_CITY, fTransform: (v) => CityDto.fromJson(v)),
      streettype: json.keyToOptional(_C_STREETTYPE, fTransform: (v) => StreettypeDto.fromJson(v)),
      streetname: json.keyToOptional(_C_STREETNAME),
      number: json.keyToOptional(_C_NUMBER),
      detail: json.keyToOptional(_C_DETAIL),
      postcode: json.keyToOptional(_C_POSTCODE),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (city is! None) _C_CITY: city.v.toJson(),
      if (streettype is! None) _C_STREETTYPE: streettype.v?.toJson(),
      if (streetname is! None) _C_STREETNAME: streetname.v,
      if (number is! None) _C_NUMBER: number.v,
      if (detail is! None) _C_DETAIL: detail.v,
      if (postcode is! None) _C_POSTCODE: postcode.v,
    };
  }

  Optional<String> get composedStreetLine {
    final v = [
      streettype.vn?.label.vn?.localized,
      streetname.vn,
      number.vn,
    ].map((s) => s?.trim()).where((s) => s != null && s.length != 0).join(" ");

    return v == "" ? const None<String>() : Some(v);
  }
}
