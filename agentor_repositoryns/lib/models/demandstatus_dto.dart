import 'package:agentor_repositoryns/json_helper.dart';
import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';

enum DemandstatusCode { active, historic }

class DemandstatusCodes {
  static const notHistoricOnes = const <DemandstatusCode>{DemandstatusCode.active};
}

class DemandstatusDto {
  static const _C_CODE = "code";
  static const _C_LABEL = "label";

  Optional<DemandstatusCode> code;
  Optional<MultilingualStrDto> label;

  DemandstatusDto({
    this.code = const None(),
    this.label = const None(),
  });

  factory DemandstatusDto.fromJson(Map json) {
    return DemandstatusDto(
      code: json.keyToOptional<DemandstatusCode>(_C_CODE, fTransform: (v) => JsonHelper.unserializeValue<String>(v).toDemandStatusCode()),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v.enumToString(),
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }

  
}
