import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';
import '../datetime_helper.dart';

const String _c_id = "id";
const String _c_type = "type";
const String _c_description = "description";
const String _c_contact = "contact";
const String _c_offer = "offer";
const String _c_when = "when";
const String _c_done = "done";

class ActionDto {
  Optional<String> id;
  Optional<ActiontypeDto> type;
  Optional<String?> description;
  Optional<ContactDto?> contact;
  Optional<OfferDto?> offer;

  /// Si la acción tiene asociado una solicitud de servicio
  Optional<ServiceDto?> requestedService;
  Optional<DateTime> when;
  Optional<bool> done;

  ActionDto({
    this.id = const None(),
    this.type = const None(),
    this.description = const None(),
    this.contact = const None(),
    this.offer = const None(),
    this.requestedService = const None(),
    this.when = const None(),
    this.done = const None(),
  });

  factory ActionDto.fromJson(Map json) {
    return ActionDto(
      id: json.keyToOptional<String>(_c_id),
      type: json.keyToOptional<ActiontypeDto>(_c_type, fTransform: (v) => ActiontypeDto.fromJson(v)),
      description: json.keyToOptional<String?>(_c_description),
      contact: json.keyToOptional<ContactDto?>(_c_contact, fTransform: (v) => ContactDto.fromJson(v)),
      offer: json.keyToOptional<OfferDto?>(_c_offer, fTransform: (o) => OfferDto.fromJson(o)),
      when: json.keyToOptional<DateTime>(_c_when),
      done: json.keyToOptional<bool>(_c_done),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (type is! None) _c_type: type.v.toJson(),
      if (description is! None) _c_description: description.v,
      if (contact is! None) _c_contact: contact.v?.toJson(),
      if (offer is! None) _c_offer: offer.v?.toJson(),
      if (when is! None) _c_when: when.v.toJson(),
      if (done is! None) _c_done: done.v,
    };
  }

  ActionDto copyWith({
    Optional<String>? id,
    Optional<ActiontypeDto>? type,
    Optional<String?>? description,
    Optional<ContactDto?>? contact,
    Optional<OfferDto?>? offer,
    Optional<DateTime>? when,
    Optional<bool>? done,
  }) {
    return ActionDto(
      id: id ?? this.id,
      type: type ?? this.type,
      description: description ?? this.description,
      contact: contact ?? this.contact,
      offer: offer ?? this.offer,
      when: when ?? this.when,
      done: done ?? this.done,
    );
  }
}
