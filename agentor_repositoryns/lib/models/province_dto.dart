import 'package:agentor_repositoryns/models/country_dto.dart';
import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';


const _C_CODE = "code";
const _C_LABEL = "label";
const _C_COUNTRY = "country";

class ProvinceDto {
  Optional<String> code;
  Optional<MultilingualStrDto> label;
  Optional<CountryDto> country;

  ProvinceDto({
    this.code = const None(),
    this.label = const None(),
    this.country = const None(),
  });

  factory ProvinceDto.fromJson(Map json) {
    return ProvinceDto(
      code: json.keyToOptional(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      country: json.keyToOptional(_C_COUNTRY, fTransform: (v) => CountryDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
      if (country is! None) _C_COUNTRY: country.v.toJson(),
    };
  }
}
