import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';
import 'model_enum_extensions.dart';

enum SalefeetypeCode { percent, fixed }

class SalefeetypeDto {
  static const _C_CODE = "code";
  static const _C_LABEL = "label";

  Optional<SalefeetypeCode> code;
  Optional<MultilingualStrDto> label;
  SalefeetypeDto({
    this.code = const None(),
    this.label = const None(),
  });

  factory SalefeetypeDto.fromJson(Map data) {
    return SalefeetypeDto(
      code: data.keyToOptional<String>(_C_CODE).map((s) => s.toSalefeetypeCode()),
      label: data.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v.enumToString(),
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }

}
