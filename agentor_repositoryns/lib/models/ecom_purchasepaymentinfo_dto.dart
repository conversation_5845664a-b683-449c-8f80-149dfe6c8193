import 'package:agentor_repositoryns/optional.dart';

class EcomProductPaymenInfoDto {
  static const String _c_days = "days";
  static const String _c_amount = "amount";

  Optional<double> amount;
  Optional<int> days;

  EcomProductPaymenInfoDto({
    this.amount = const None(),
    this.days = const None(),
  });

  factory EcomProductPaymenInfoDto.fromJson(Map hash) {
    return EcomProductPaymenInfoDto(
      amount: hash.keyToOptional<double>(_c_amount),
      days: hash.keyToOptional<int>(_c_days),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (amount is! None) _c_amount: amount.v,
      if (days is! None) _c_days: days.v,
    };
  }
}