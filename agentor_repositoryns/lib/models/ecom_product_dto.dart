import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';

class EcomProductDto {
  static const String _c_id = "id";
  static const String _c_account = "account";
  static const String _c_name = "name";
  static const String _c_description = "description";
  static const String _c_firstpayment = "firstpayment";
  static const String _c_dailyamount = "dailyamount";

  Optional<String> id;
  Optional<EcomAccountDto> account;
  Optional<MultilingualStrDto> name;
  Optional<String> description;
  Optional<EcomProductPaymenInfoDto> firstPayment;
  Optional<double?> dailyamount;

  EcomProductDto({
    this.id = const None(),
    this.account = const None(),
    this.name = const None(),
    this.description = const None(),
    this.firstPayment = const None(),
    this.dailyamount = const None(),
  });

  factory EcomProductDto.fromJson(Map hash) {
    return EcomProductDto(
      id: hash.keyToOptional(_c_id),
      account: hash.keyToOptional(_c_account, fTransform: (v) => EcomAccountDto.fromJson(v)),
      name: hash.keyToOptional(_c_name, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      description: hash.keyToOptional<String>(_c_description),
      firstPayment: hash.keyToOptional(_c_firstpayment, fTransform: (v) => EcomProductPaymenInfoDto.fromJson(v)),
      dailyamount: hash.keyToOptional<double?>(_c_dailyamount),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (account is! None) _c_account: account.v.toJson(),
      if (name is! None) _c_name: name.v.toJson(),
      if (description is! None) _c_description: description.v,
      if (firstPayment is! None) _c_firstpayment: firstPayment.v.toJson(),
      if (dailyamount is! None) _c_dailyamount: dailyamount.v,
    };
  }
}

