import 'package:agentor_repositoryns/models/agent_dto.dart';
import 'package:agentor_repositoryns/models/contact_dto.dart';
import 'package:agentor_repositoryns/models/currency_dto.dart';
import 'package:agentor_repositoryns/models/offer_rent_dto.dart';
import 'package:agentor_repositoryns/models/offer_sale_dto.dart';
import 'package:agentor_repositoryns/models/offerhistoric_dto.dart';
import 'package:agentor_repositoryns/models/offermandate_dto.dart';
import 'package:agentor_repositoryns/models/offerstatus_dto.dart';
import 'package:agentor_repositoryns/models/property_dto.dart';
import 'package:agentor_repositoryns/models/offer_version_dto.dart';

import 'package:agentor_repositoryns/optional.dart';
import '../datetime_helper.dart';

class AtDto {
  static const _c_at = "at";
  Optional<DateTime> at;
  AtDto({
    this.at = const None(),
  });

  factory AtDto.fromJson(Map data) {
    return AtDto(
      at: data.keyToOptional<DateTime>(_c_at),
    );
  }
  Map<String, dynamic> toJson() {
    return {if (at is! None) _c_at: at.v.toJson()};
  }
}

class MatchingsinfoDto {
  static const _c_has = "has";
  static const _c_notReviewedCount = "notReviewedCount";

  Optional<bool> has;
  Optional<int> notReviewedCount;

  MatchingsinfoDto({
    this.has = const None(),
    this.notReviewedCount = const None(),
  });

  factory MatchingsinfoDto.fromJson(Map data) {
    return MatchingsinfoDto(
      has: data.keyToOptional<bool>(_c_has),
      notReviewedCount: data.keyToOptional<int>(_c_notReviewedCount),
    );
  }
}

class OfferSourceContactDto {
  static const String _c_phone = "phone";
  static const String _c_name = "name";

  Optional<String?> phone;
  Optional<String?> name;
  OfferSourceContactDto({this.phone = const None(), this.name = const None()});
  factory OfferSourceContactDto.fromJson(Map<String, dynamic> json) {
    return OfferSourceContactDto(
      phone: json.keyToOptional<String?>(_c_phone),
      name: json.keyToOptional<String?>(_c_name),
    );
  }

  Map<String, dynamic> toJson() {
    return {if (phone is! None) _c_phone: phone.v, if (name is! None) _c_name: name.v};
  }
}

class OfferSourceDto {
  static const String _c_pageUrl = "pageUrl";
  static const String _c_reference = "reference";
  static const String _c_announcedByAnIndividual = "announcedByAnIndividual";
  static const String _c_contact = "contact";
  static const String _c_updated = "updated";
  Optional<String?> pageUrl;
  Optional<String?> reference;
  Optional<bool?> announcedByAnIndividual;
  Optional<OfferSourceContactDto?> contact;
  Optional<AtDto?> updated;

  OfferSourceDto(
      {this.pageUrl = const None(),
      this.reference = const None(),
      this.announcedByAnIndividual = const None(),
      this.contact = const None(),
      this.updated = const None()});
  factory OfferSourceDto.fromJson(Map<String, dynamic> json) {
    return OfferSourceDto(
        pageUrl: json.keyToOptional<String?>(_c_pageUrl),
        reference: json.keyToOptional<String?>(_c_reference),
        announcedByAnIndividual: json.keyToOptional<bool?>(_c_announcedByAnIndividual),
        contact: json.keyToOptional<OfferSourceContactDto?>(_c_contact,
            fTransform: (contactJson) => OfferSourceContactDto.fromJson(contactJson)),
        updated: json.keyToOptional<AtDto?>(_c_updated, fTransform: (updatedJson) => AtDto.fromJson(updatedJson)));
  }
  Map<String, dynamic> toJson() {
    return {
      if (pageUrl is! None) _c_pageUrl: pageUrl.v,
      if (reference is! None) _c_reference: reference.v,
      if (announcedByAnIndividual is! None) _c_announcedByAnIndividual: announcedByAnIndividual.v,
      if (contact is! None) _c_contact: contact.v?.toJson(),
      if (updated is! None) _c_updated: updated.v?.toJson()
    };
  }
}

class OfferDto {
  static const String _c_id = "id";
  static const String _c_version = "version";
  static const String _c_agent = "agent";
  static const String _c_source = "source";
  static const String _c_status = "status";
  static const String _c_customer = "customer";
  static const String _c_urgency = "urgency";
  static const String _c_sale = "sale";
  static const String _c_rent = "rent";
  static const String _c_currency = "currency";
  static const String _c_mandate = "mandate";
  static const String _c_historic = "historic";
  static const String _c_property = "property";
  static const String _c_matchingsinfo = "matchingsinfo";
  static const String _c_notes = "notes";
  static const String _c_description = "description";

  Optional<String> id;
  Optional<OfferVersionDto?> version;
  Optional<AgentDto> agent;
  Optional<OfferSourceDto> source;
  Optional<OfferstatusDto> status;
  Optional<ContactDto?> customer;
  Optional<int?> urgency;
  Optional<OffermandateDto?> mandate;
  Optional<String?> notes;
  Optional<OfferSaleDto> sale;
  Optional<OfferRentDto> rent;
  Optional<PropertyDto> property;
  Optional<OfferhistoricDto?> historic;
  Optional<CurrencyDto> currency;
  Optional<String?> description;
  Optional<MatchingsinfoDto> matchingsinfo;

  OfferDto({
    this.id = const None(),
    this.version = const None(),
    this.agent = const None(),
    this.source = const None(),
    this.status = const None(),
    this.customer = const None(),
    this.urgency = const None(),
    this.mandate = const None(),
    this.historic = const None(),
    this.sale = const None(),
    this.rent = const None(),
    this.currency = const None(),
    this.property = const None(),
    this.matchingsinfo = const None(),
    this.notes = const None(),
    this.description = const None(),
  });

  Optional<String> get reference {
    return id;
  }

  ///
  /// Computed Offer name useful for showing it on combo-box, lists, ...
  ///
  Optional<String> get name {
    final typeName = property.vn?.type.vn?.label.vn?.localized ?? "";
    final saleName = sale.vn?.allowed.vn ?? false ? "venta" : null;
    final rentName = rent.vn?.allowed.vn ?? false ? "alquiler" : null;
    final address = property.vn?.address.v;
    final cityName = address?.city.vn?.label.vn?.localized;
    final line = [
      address?.streettype.vn?.label.vn?.localized,
      address?.streetname.vn,
      address?.number.vn,
    ].map((s) => s?.trim()).where((s) => s != null && s.length != 0).join(" ");

    return Some(["#${id.v}", typeName, saleName, rentName, cityName, line]
        .map((s) => s?.trim())
        .where((s) => s != null && s.length != 0)
        .join(", "));
  }

  factory OfferDto.fromJson(Map json) {
    try {
      return OfferDto(
        id: json.keyToOptional(_c_id),
        version: json.keyToOptional(_c_version, fTransform: (v) => OfferVersionDto.fromJson(v)),
        agent: json.keyToOptional(_c_agent, fTransform: (a) => AgentDto.fromJson(a)),
        status: json.keyToOptional(_c_status, fTransform: (v) => OfferstatusDto.fromJson(v)),
        source: json.keyToOptional(_c_source, fTransform: (v) => OfferSourceDto.fromJson(v)),
        customer: json.keyToOptional<ContactDto?>(_c_customer, fTransform: (v) => ContactDto.fromJson(v)),
        urgency: json.keyToOptional<int?>(_c_urgency),
        sale: json.keyToOptional(_c_sale, fTransform: (v) => OfferSaleDto.fromJson(v)),
        rent: json.keyToOptional(_c_rent, fTransform: (v) => OfferRentDto.fromJson(v)),
        currency: json.keyToOptional(_c_currency, fTransform: (v) => CurrencyDto.fromJson(v)),
        mandate: json.keyToOptional<OffermandateDto?>(_c_mandate, fTransform: (v) => OffermandateDto.fromJson(v)),
        historic: json.keyToOptional<OfferhistoricDto?>(_c_historic, fTransform: (v) => OfferhistoricDto.fromJson(v)),
        property: json.keyToOptional(_c_property, fTransform: (v) => PropertyDto.fromJson(v)),
        matchingsinfo: json.keyToOptional(_c_matchingsinfo, fTransform: (v) => MatchingsinfoDto.fromJson(v)),
        description: json.keyToOptional(_c_description),
        notes: json.keyToOptional<String?>(_c_notes),
      );
    } catch (e) {
      print("*+*+* Error OfferDto.fromJson $e");
      throw e;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (version is! None) _c_version: version.v?.toJson(),
      if (agent is! None) _c_agent: agent.v.toJson(),
      if (status is! None) _c_status: status.v.toJson(),
      if (source is! None) _c_source: source.v.toJson(),
      if (customer is! None) _c_customer: customer.v?.toJson(),
      if (urgency is! None) _c_urgency: urgency.v,
      if (sale is! None) _c_sale: sale.v.toJson(),
      if (rent is! None) _c_rent: rent.v.toJson(),
      if (currency is! None) _c_currency: currency.v.toJson(),
      if (mandate is! None) _c_mandate: mandate.v?.toJson(),
      if (historic is! None) _c_historic: historic.v?.toJson(),
      if (property is! None) _c_property: property.v.toJson(),
      if (notes is! None) _c_notes: notes.v,
      if (description is! None) _c_description: description.v,
    };
  }

  OfferDto copyIdOnly() => OfferDto(id:this.id);
  OfferDto copyWith({
    Optional<String>? id,
    Optional<OfferVersionDto?>? version,
    Optional<AgentDto>? agent,
    Optional<OfferSourceDto>? source,
    Optional<OfferstatusDto>? status,
    Optional<ContactDto?>? customer,
    Optional<int?>? urgency,
    Optional<OffermandateDto?>? mandate,
    Optional<String?>? notes,
    Optional<OfferSaleDto>? sale,
    Optional<OfferRentDto>? rent,
    Optional<PropertyDto>? property,
    Optional<OfferhistoricDto?>? historic,
    Optional<CurrencyDto>? currency,
    Optional<String?>? description,
    Optional<MatchingsinfoDto>? matchingsinfo,
  }) =>
      OfferDto(
        id: id ?? this.id,
        version: version ?? this.version,
        agent: agent ?? this.agent,
        source: source ?? this.source,
        status: status ?? this.status,
        customer: customer ?? this.customer,
        urgency: urgency ?? this.urgency,
        mandate: mandate ?? this.mandate,
        notes: notes ?? this.notes,
        sale: sale ?? this.sale,
        rent: rent ?? this.rent,
        property: property ?? this.property,
        historic: historic ?? this.historic,
        currency: currency ?? this.currency,
        description: description ?? this.description,
        matchingsinfo: matchingsinfo ?? this.matchingsinfo,
      );
  OfferDto copy() => OfferDto.fromJson(this.toJson());
}
