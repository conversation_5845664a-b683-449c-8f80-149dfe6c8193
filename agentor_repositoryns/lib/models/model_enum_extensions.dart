import 'package:agentor_repositoryns/models/agenttype_dto.dart';
import 'package:agentor_repositoryns/models/demandstatus_dto.dart';
import 'package:agentor_repositoryns/models/field_def_dto.dart';
import 'package:agentor_repositoryns/models/offermandatetype_dto.dart';
import 'package:agentor_repositoryns/models/offerstatus_dto.dart';
import 'package:agentor_repositoryns/models/salefeetype_dto.dart';
import 'package:agentor_utils/agentor_utils.dart';

extension ModelEnumsExtensions on String {
  AgenttypeCode toAgenttypeCode() {
    final ending = "." + this;
    return AgenttypeCode.values.firstWhere((element) => element.toString().endsWith(ending));
  }

  OfferstatusCode toOfferStatusCode() {
    final ending = "." + this;
    return OfferstatusCode.values.firstWhere((element) => element.toString().endsWith(ending));
  }

  DemandstatusCode toDemandStatusCode() {
    final ending = "." + this;
    return DemandstatusCode.values.firstWhere((value) => value.toString().endsWith(ending));
  }

  OffermandatetypeCode toOffermandatetypeCode() {
    final ending = "." + this;
    return OffermandatetypeCode.values.firstWhere((element) => element.toString().endsWith(ending));
  }

  SalefeetypeCode toSalefeetypeCode() {
    final ending = "." + this;
    return SalefeetypeCode.values.firstWhere((element) => element.toString().endsWith(ending));
  }

  FieldDefType toFieldDefType() {
    final ending = "." + this;
    return FieldDefType.values.firstWhere((element) => element.toString().endsWith(ending));
  }
}

extension OfferstatusCodeExtension on OfferstatusCode {
  String toJsonString() => this.toString().o((s) => s.substring(s.indexOf(".") + 1));
  String enumToString() => this.toString().o((s) => s.substring(s.indexOf(".") + 1));
}

extension AgenttypeCodeExtension on AgenttypeCode {
  String enumToString() => this.toString().o((s) => s.substring(s.indexOf(".") + 1));
}

extension DemandstatusCodeExtension on DemandstatusCode {
  String enumToString() => this.toString().o((s) => s.substring(s.indexOf(".") + 1));
}

extension OffermandatetypeCodeExtension on OffermandatetypeCode {
  String enumToString() => this.toString().o((s) => s.substring(s.indexOf(".") + 1));
}

extension SalefeetypeCodeExtension on SalefeetypeCode {
  String enumToString() => this.toString().o((str) => str.substring(str.indexOf(".") + 1));
}

extension FieldDefTypeExtension on FieldDefType {
  ///
  /// Gets the string representation of the enum value without the Enum name prefix
  /// i.e.:
  /// FieldDefType.code.toShortString() -> "code"
  /// FieldDefType.code.toString() -> "FieldDefType.code"
  ///
  ///
  String enumToString() => this.toString().o((s) => s.substring(s.indexOf(".") + 1));
}
