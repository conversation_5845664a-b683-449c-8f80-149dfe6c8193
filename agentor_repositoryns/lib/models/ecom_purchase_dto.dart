import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';
import '../datetime_helper.dart';

class EcomPurchaseService {
  static const String _c_type = "type";
  static const String _c_enddate = "enddate";
  static const String _c_workgroup = "workgroup";
  static const String _c_offer = "offer";

  Optional<EcomPurchaseServiceType> type;
  Optional<DateTime?> enddate;
  Optional<WorkgroupDto?> workgroup;
  Optional<OfferDto?> offer;

  EcomPurchaseService({
    this.type = const None(),
    this.enddate = const None(),
    this.workgroup = const None(),
    this.offer = const None(),
  });
  factory EcomPurchaseService.fromJson(Map hash) {
    return EcomPurchaseService(
      type: hash.keyToOptional(_c_type, fTransform: (v) => EcomPurchaseServiceType.fromJson(v)),
      enddate: hash.keyToOptional<DateTime?>(_c_enddate),
      workgroup: hash.keyToOptional(_c_workgroup, fTransform: (v) => WorkgroupDto.fromJson(v)),
      offer: hash.keyToOptional(_c_offer, fTransform: (v) => OfferDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (type is! None) _c_type: type.v.toJson(),
      if (enddate is! None) _c_enddate: enddate.v?.toJson(),
      if (workgroup is! None) _c_workgroup: workgroup.v?.toJson(),
      if (offer is! None) _c_offer: offer.v?.toJson(),
    };
  }
}

class EcomPurchaseServiceType {
  static const String _c_code = "code";

  Optional<String> code;
  EcomPurchaseServiceType({required this.code});

  factory EcomPurchaseServiceType.fromJson(Map hash) {
    return EcomPurchaseServiceType(code: hash.keyToOptional(_c_code));
  }

  Map<String, dynamic> toJson() {
    return {if (code is! None) _c_code: code.v};
  }
}

class EcomPurchaseTotalsDto {
  static const String _c_payed = "payed";
  Optional<double> payed;
  EcomPurchaseTotalsDto({required this.payed});
    factory EcomPurchaseTotalsDto.fromJson(Map hash) {
    return EcomPurchaseTotalsDto(payed: hash.keyToOptional(_c_payed));
  }

  Map<String, dynamic> toJson() {
    return {if (payed is! None) _c_payed: payed.v};
  }
}
class EcomPurchaseDto {
  static const String _c_id = "id";
  static const String _c_product = "product";
  static const String _c_details = "details";
  static const String _c_buyer = "buyer";
  static const String _c_date = "date";
  static const String _c_firstpayment = "firstpayment";
  static const String _c_dailyamount = "dailyamount";
  static const String _c_service = "service";
  static const String _c_totals = "totals";

  Optional<String> id;
  Optional<EcomProductDto> product;
  Optional<String> details;
  Optional<EcomAccountDto> buyer;
  Optional<DateTime> date;
  Optional<EcomProductPaymenInfoDto> firstPayment;
  Optional<double?> dailyamount;
  Optional<EcomPurchaseService?> service;
  Optional<EcomPurchaseTotalsDto?> totals;

  EcomPurchaseDto({
    this.id = const None(),
    this.product = const None(),
    this.details = const None(),
    this.buyer = const None(),
    this.date = const None(),
    this.firstPayment = const None(),
    this.dailyamount = const None(),
    this.service = const None(),
    this.totals = const None(),
  });

  factory EcomPurchaseDto.fromJson(Map hash) {
    return EcomPurchaseDto(
      id: hash.keyToOptional(_c_id),
      product: hash.keyToOptional(_c_product, fTransform: (v) => EcomProductDto.fromJson(v)),
      details: hash.keyToOptional(_c_details),
      buyer: hash.keyToOptional(_c_buyer, fTransform: (v) => EcomAccountDto.fromJson(v)),
      date: hash.keyToOptional<DateTime>(_c_date),
      firstPayment: hash.keyToOptional(_c_firstpayment, fTransform: (v) => EcomProductPaymenInfoDto.fromJson(v)),
      dailyamount: hash.keyToOptional<double?>(_c_dailyamount),
      service: hash.keyToOptional(_c_service, fTransform: (v) => EcomPurchaseService.fromJson(v)),
      totals: hash.keyToOptional(_c_totals, fTransform: (v)=> EcomPurchaseTotalsDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (product is! None) _c_product: product.v.toJson(),
      if (details is! None) _c_details: details.v,
      if (buyer is! None) _c_buyer: buyer.v.toJson(),
      if (date is! None) _c_date: date.v.toJson(),
      if (firstPayment is! None) _c_firstpayment: firstPayment.v.toJson(),
      if (dailyamount is! None) _c_dailyamount: dailyamount.v,
      // Totals no se incluye: es un dato calculado
    };
  }
}

