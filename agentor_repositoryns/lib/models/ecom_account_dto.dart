import 'package:agentor_repositoryns/models/agent_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class EcomAccountDto {
  static const String _c_id = "id";
  static const String _c_agent = "agent";
  static const String _c_balance = "balance";

  Optional<String> id;
  Optional<AgentDto> agent;
  Optional<double> balance;

  EcomAccountDto({
    this.id = const None(),
    this.agent = const None(),
    this.balance = const None(),
  });

  factory EcomAccountDto.fromJson(Map json) {
    return EcomAccountDto(
      id: json.keyToOptional<String>(_c_id),
      agent: json.keyToOptional(_c_agent, fTransform: (v) => AgentDto.fromJson(v)),
      balance: json.keyToOptional<double>(_c_balance),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (agent is! None) _c_agent: agent.v.toJson(),
      if (balance is! None) _c_balance: balance.v,
    };
  }
}
