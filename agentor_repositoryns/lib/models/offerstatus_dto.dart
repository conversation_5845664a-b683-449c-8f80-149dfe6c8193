import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';
import 'model_enum_extensions.dart';

enum OfferstatusCode { news, draft, commercialization, historic }

class OfferstatusCodes {
  static const notHistoricOnes = const <OfferstatusCode>{
    OfferstatusCode.news,
    OfferstatusCode.draft,
    OfferstatusCode.commercialization
  };
  /**
   * Ofertas en las que se está trabajando (en preparación y en comercialización)
   * Se excluyen noticias e historicas
   */
  static const workingOnOnes = const <OfferstatusCode>{
    OfferstatusCode.draft,
    OfferstatusCode.commercialization
  };
}

class OfferstatusDto {
  static const _C_CODE = "code";
  static const _C_LABEL = "label";

  Optional<OfferstatusCode> code;
  Optional<MultilingualStrDto> label;
  OfferstatusDto({
    this.code = const None(),
    this.label = const None(),
  });

  factory OfferstatusDto.fromJson(Map json) {
    return OfferstatusDto(
      code: json.keyToOptional<String>(_C_CODE).map((s) => s.toOfferStatusCode()),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v.enumToString(),
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }
}
