import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/models/propertysubtype_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class PropertytypeDto {
  static const _C_CODE = "code";
  static const _C_LABEL = "label";
  static const _C_SUBTYPES = "subtypes";

  Optional<String> code;
  Optional<MultilingualStrDto> label;

  /// La lista de los subtipos de inmueble asociados al tipo
  /// Podría no haber sido solicitada, con lo cual se espera que su valor sea None()
  Optional<List<PropertysubtypeDto>> subtypes;
  PropertytypeDto({
    this.code = const None(),
    this.label = const None(),
    this.subtypes = const None(),
  });

  factory PropertytypeDto.fromJson(Map json) {
    return PropertytypeDto(
      code: json.keyToOptional<String>(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      subtypes: json.keyToOptional(_C_SUBTYPES, fTransform: (dynamic jsonValue) {
        final values = jsonValue as List?;
        //jsonValue as List<Map<String, dynamic>>; // Conversión peligrosa... si el json está mal formado esto
        if (values == null)
          // values NUNCA debería ser null!!!... si por error se ha colado lo convertimos en una lista vacía
          return [];
        else
          return values.map((value) => PropertysubtypeDto.fromJson(value)).toList();
      }),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
      if (subtypes is! None) _C_SUBTYPES: subtypes.v.map((st) => st.toJson()),
    };
  }

}
