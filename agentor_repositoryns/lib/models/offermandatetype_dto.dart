import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';
import 'model_enum_extensions.dart';

enum OffermandatetypeCode {
  exclusive,
  open,
  verbal,
  flat_rate,
  other,
}

class OffermandatetypeDto {
  static const _C_CODE = "code";
  static const _C_LABEL = "label";

  Optional<OffermandatetypeCode> code;
  Optional<MultilingualStrDto> label;
  OffermandatetypeDto({
    this.code = const None(),
    this.label = const None(),
  });

  factory OffermandatetypeDto.fromJson(Map json) {
    return OffermandatetypeDto(
      code: json.keyToOptional<String>(_C_CODE).map((s) => s.toOffermandatetypeCode()),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v.enumToString(),
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }
}
