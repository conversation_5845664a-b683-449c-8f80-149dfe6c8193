import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class OfferhistoriccauseDto {
  static const _c_code = "code";
  static const _c_label = "label";
  static const _c_success = "success";

  Optional<String> code;
  Optional<MultilingualStrDto> label;
  Optional<bool> success;

  OfferhistoriccauseDto({
    this.code = const None(),
    this.label = const None(),
    this.success = const None(),
  });

  factory OfferhistoriccauseDto.fromJson(Map json) {
    return OfferhistoriccauseDto(
      code: json.keyToOptional<String>(_c_code),
      label: json.keyToOptional(_c_label, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      success: json.keyToOptional<bool>(_c_success),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _c_code: code.v,
      if (label is! None) _c_label: label.v.toJson(),
      if (success is! None) _c_success: success.v,
    };
  }
}
