import 'package:agentor_repositoryns/agentor_repositoryns.dart';

class OfferVersionDto {
  static const _c_of = "of";
  static const _c_type = "type";
  static const _c_disclaimer = "disclaimer";

  Optional<OfferDto> of;
  Optional<OfferversiontypeDto> type;
  Optional<String?> disclaimer;

  OfferVersionDto({
    this.of = const None(),
    this.type = const None(),
    this.disclaimer = const None(),
  });

  OfferVersionDto copyWith({
    Optional<OfferDto>? of,
    Optional<OfferversiontypeDto>? type,
    Optional<String>? disclaimer,
  }) =>
      OfferVersionDto(
        of: of ?? this.of,
        type: type ?? this.type,
        disclaimer: disclaimer ?? this.disclaimer,
      );

  factory OfferVersionDto.fromJson(Map data) {
    return OfferVersionDto(
      of: data.keyToOptional<OfferDto>(_c_of, fTransform: (v) => OfferDto.fromJson(v)),
      type: data.keyToOptional<OfferversiontypeDto>(_c_type, fTransform: (v) => OfferversiontypeDto.fromJson(v)),
      disclaimer: data.keyToOptional(_c_disclaimer),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (of is! None) _c_of: of.v.toJson(),
      if (type is! None) _c_type: type.v.toJson(),
      if (disclaimer is! None) _c_disclaimer: disclaimer.v,
    };
  }
}
