import 'package:agentor_utils/agentor_utils.dart';

class MultilingualStrDto {
  final Map<String, String> _strings;

  MultilingualStrDto({Map<String, String> langAndValues = const {}}) : _strings = langAndValues;

  factory MultilingualStrDto.fromJson(Map json) {
    return MultilingualStrDto(
      langAndValues: json.map((key, value) => MapEntry(key, (value is String) ? value : "")),
    );
  }
  Map<String, dynamic> toJson() => Map.from(_strings);
  String get localized => _strings.localized;
  String getByLang(String? lang) => _strings.getByLang(lang);
  String operator [](String lang) => _strings.getByLang(lang);
}
