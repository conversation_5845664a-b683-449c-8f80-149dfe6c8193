import 'package:agentor_repositoryns/models/salefee_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class OfferSaleDto {
  static const _c_allowed = "allowed";
  static const _c_amount = "amount";
  static const _c_marketAmount = "marketAmount";
  static const _c_fee = "fee";
  static const _c_monthlyPayment = "monthlyPayment";

  Optional<bool> allowed;
  Optional<double?> amount;
  Optional<double?> marketAmount;
  Optional<SaleFeeDto?> fee;
  Optional<double?> monthlyPayment;

  OfferSaleDto({
    this.allowed = const None(),
    this.amount = const None(),
    this.marketAmount = const None(),
    this.fee = const None(),
    this.monthlyPayment = const None(),
  });

  factory OfferSaleDto.emptySale() =>
      OfferSaleDto.fromJson({'$_c_allowed': true, '$_c_amount': 0.0, '$_c_monthlyPayment': 0.0});
  factory OfferSaleDto.emptyNoSale() => OfferSaleDto.fromJson({'$_c_allowed': false});

  factory OfferSaleDto.fromJson(Map data) {
    return OfferSaleDto(
      allowed: data.keyToOptional<bool>(_c_allowed),
      amount: data.keyToOptional<double?>(_c_amount),
      marketAmount: data.keyToOptional<double?>(_c_marketAmount),
      fee: data.keyToOptional<SaleFeeDto?>(_c_fee, fTransform: (v) => SaleFeeDto.fromJson(v)),
      monthlyPayment: data.keyToOptional<double?>(_c_monthlyPayment),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (allowed is! None) _c_allowed: allowed.v,
      if (amount is! None) _c_amount: amount.v,
      if (marketAmount is! None) _c_marketAmount: marketAmount.v,
      if (fee is! None) _c_fee: fee.v?.toJson(),
      if (monthlyPayment is! None) _c_monthlyPayment: monthlyPayment.v,
    };
  }
}
