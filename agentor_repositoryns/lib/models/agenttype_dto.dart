import 'package:agentor_repositoryns/json_helper.dart';
import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';

const _c_code = "code";
const _c_name = "name";

enum AgenttypeCode {
  individual,
  professional
}
class AgenttypeDto {
  Optional<AgenttypeCode> code;
  Optional<String> name;

  AgenttypeDto({
    this.code = const None(),
    this.name = const None(),
  });

  factory AgenttypeDto.fromJson(Map<String, dynamic> json) {
    return AgenttypeDto(
      code: json.keyToOptional<AgenttypeCode>(_c_code, fTransform: (v) => JsonHelper.unserializeValue<String>(v).toAgenttypeCode()),
      name: json.keyToOptional(_c_name),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _c_code: code.v.enumToString(),
      if (name is! None) _c_name: name.v,
    };
  }
}
