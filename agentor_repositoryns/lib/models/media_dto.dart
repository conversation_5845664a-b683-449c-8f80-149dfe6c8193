import 'package:agentor_repositoryns/models/mediafile_dto.dart';
import 'package:agentor_repositoryns/optional.dart';



class MediaDto {
  static const _c_key = "key";
  static const _c_original = "original";
  static const _c_thumbnail = "thumbnail";

  Optional<String> key;
  Optional<MediafileDto> original;
  Optional<MediafileDto?> thumbnail;

  MediaDto({
    this.key = const None(),
    this.original = const None(),
    this.thumbnail = const None(),
  });

  factory MediaDto.fromJson(Map json) {
    return MediaDto(
      key: json.keyToOptional(_c_key),
      original: json.keyToOptional(_c_original, fTransform: (v) => MediafileDto.fromJson(v)),
      thumbnail: json.keyToOptional(_c_thumbnail, fTransform: (v) => MediafileDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (key is! None) _c_key: key.v,
      if (original is! None) _c_original: original.v.toJson(),
      if (thumbnail is! None) _c_thumbnail: thumbnail.v?.toJson(),
    };
  }

  
}
