import 'package:agentor_repositoryns/models/offermandatetype_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

import '../datetime_helper.dart';

class OffermandateDto {
  static const _C_TYPE = "type";
  static const _C_START = "start";
  static const _C_END = "end";

  Optional<OffermandatetypeDto> type;
  Optional<DateTime?> start;
  Optional<DateTime?> end;

  OffermandateDto({
    this.type = const None(),
    this.start = const None(),
    this.end = const None(),
  });

  factory OffermandateDto.fromJson(Map json) {
    return OffermandateDto(
      type: json.keyToOptional(_C_TYPE, fTransform: (v) => OffermandatetypeDto.fromJson(v)),
      start: json.keyToOptional<DateTime?>(_C_START),
      end: json.keyToOptional<DateTime?>(_C_END),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (type is! None) _C_TYPE: type.v.toJson(),
      if (start is! None) _C_START: start.v?.toJson(),
      if (end is! None) _C_END: end.v?.toJson(),
    };
  }
}
