import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/models/province_dto.dart';
import 'package:agentor_repositoryns/optional.dart';


const _C_CODE = "code";
const _C_LABEL = "label";
const _C_PROVINCE = "province";

class CityDto {
  Optional<String> code;
  Optional<MultilingualStrDto> label;
  Optional<ProvinceDto> province;

  CityDto({
    this.code = const None(),
    this.label = const None(),
    this.province = const None(),
  });

  factory CityDto.fromJson(Map json) {
    return CityDto(
      code: json.keyToOptional(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (json) => MultilingualStrDto.fromJson(json)),
      province: json.keyToOptional(_C_PROVINCE, fTransform: (json) => ProvinceDto.fromJson(json)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
      if (province is! None) _C_PROVINCE: province.v.toJson(),
    };
  }

  String toString() => '$code';
}
