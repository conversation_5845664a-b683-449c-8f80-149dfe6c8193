import 'package:agentor_repositoryns/models/agent_dto.dart';
import 'package:agentor_repositoryns/models/ecom_product_dto.dart';

import 'package:agentor_repositoryns/optional.dart';

class WorkgroupDto {
  static const String _c_id = "id";
  static const String _c_owner = "owner";
  static const String _c_name = "name";
  static const String _c_publicationecomproduct = "publicationecomproduct";

  Optional<String> id;
  Optional<AgentDto> owner;
  Optional<String> name;
  Optional<EcomProductDto?> publicationEcomProduct;

  WorkgroupDto({
    this.id = const None(),
    this.owner = const None(),
    this.name = const None(),
    this.publicationEcomProduct = const None(),
  });

  WorkgroupDto copyWith({
    Optional<String>? id,
    Optional<AgentDto>? owner,
    Optional<String>? name,
    Optional<EcomProductDto?>? publicationEcomProduct,
  }) {
    return WorkgroupDto(
      id: id ?? this.id,
      owner: owner ?? this.owner,
      name: name ?? this.name,
      publicationEcomProduct: publicationEcomProduct ?? this.publicationEcomProduct,
    );
  }

  factory WorkgroupDto.fromJson(Map json) {
    return WorkgroupDto(
      id: json.keyToOptional(_c_id),
      owner: json.keyToOptional(_c_owner, fTransform: (v) => AgentDto.fromJson(json)),
      name: json.keyToOptional(_c_name),
      publicationEcomProduct:
          json.keyToOptional(_c_publicationecomproduct, fTransform: (v) => EcomProductDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (owner is! None) _c_owner: owner.v.toJson(),
      if (name is! None) _c_name: name.v,
      if (publicationEcomProduct is! None) _c_publicationecomproduct: publicationEcomProduct.v?.toJson(),
    };
  }
}
