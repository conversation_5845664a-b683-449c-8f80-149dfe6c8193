import 'package:agentor_repositoryns/optional.dart';


const _C_SYMBOL = "symbol";
const _C_CODE = "code";

class CurrencyDto {
  Optional<String> code;
  Optional<String> symbol;

  CurrencyDto({
    this.code = const None(),
    this.symbol = const None(),
  });

  factory CurrencyDto.fromJson(Map json) {
    return CurrencyDto(
      code: json.keyToOptional(_C_CODE),
      symbol: json.keyToOptional(_C_SYMBOL),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (symbol is! None) _C_SYMBOL: symbol.v,
    };
  }
}
