import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

const _C_title = "title";
const _C_detailHtml = "detailHtml";
const _C_requiresAcceptance = "requiresAcceptance";

///
/// Infomración sobre un disclaimer que debe mostrarse al usuario
/// (texto y si debe o no ser aceptado)
/// Es una entidad débil pensada, inicialmente, para mostrarse durante la solicitud de un servicio.
///
class DisclaimerDto {
  Optional<MultilingualStrDto> title;
  Optional<MultilingualStrDto> detailHtml;
  Optional<bool> requiresAcceptance;

  DisclaimerDto({
    this.title = const None(),
    this.detailHtml = const None(),
    this.requiresAcceptance = const None(),
  });
  factory DisclaimerDto.fromJson(Map json) {
    return DisclaimerDto(
      title: json.keyToOptional(_C_title, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      detailHtml: json.keyToOptional(_C_detailHtml, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      requiresAcceptance: json.keyToOptional(_C_requiresAcceptance),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (title is! None) _C_title: title.v.toJson(),
      if (detailHtml is! None) _C_detailHtml: detailHtml.v.toJson(),
      if (requiresAcceptance is! None) _C_requiresAcceptance: requiresAcceptance.v,
    };
  }
}
