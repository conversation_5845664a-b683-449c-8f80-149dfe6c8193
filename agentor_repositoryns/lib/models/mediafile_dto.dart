import 'package:agentor_repositoryns/optional.dart';



class MediafileDto {
  static const _c_url = "url";
  static const _c_mediatype = "mediatype";

  Optional<String> url;
  Optional<String> mediatype;

  MediafileDto({
    this.url = const None(),
    this.mediatype = const None(),
  });

  factory MediafileDto.fromJson(Map json) {
    return MediafileDto(
      url: json.keyToOptional(_c_url),
      mediatype: json.keyToOptional(_c_mediatype),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (url is! None) _c_url: url.v,
      if (mediatype is! None) _c_mediatype: mediatype.v,
    };
  }
}
