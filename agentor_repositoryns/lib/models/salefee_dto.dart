import 'package:agentor_repositoryns/models/salefeetype_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class SaleFeeDto {
  static const _c_type = "type";
  static const _c_value = "value";

  Optional<SalefeetypeDto?> type;
  Optional<double?> value;

  SaleFeeDto({
    this.type = const None(),
    this.value = const None(),
  });

  factory SaleFeeDto.fromJson(Map json) {
    return SaleFeeDto(
      type: json.keyToOptional(_c_type, fTransform: (t) => SalefeetypeDto.fromJson(t)),
      value: json.keyToOptional(_c_value),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (type is! None) _c_type: type.v?.toJson(),
      if (value is! None) _c_value: value.v,
    };
  }
}
