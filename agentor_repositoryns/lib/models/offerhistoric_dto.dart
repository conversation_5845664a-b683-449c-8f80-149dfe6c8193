import 'package:agentor_repositoryns/models/offerhistoriccause_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

import '../datetime_helper.dart';

class OfferhistoricDto {
  static const _c_date = "date";
  static const _c_cause = "cause";

  Optional<DateTime> date;
  Optional<OfferhistoriccauseDto> cause;

  OfferhistoricDto({
    this.date= const None(),
    this.cause= const None(),
  });

  factory OfferhistoricDto.fromJson(Map json) {
    return OfferhistoricDto(
      date: json.keyToOptional(_c_date),
      cause: json.keyToOptional(_c_cause, fTransform: (c) => OfferhistoriccauseDto.fromJson(c)),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (date is! None) _c_date: date.v.toJson(),
      if (cause is! None) _c_cause: cause.v.toJson(),
    };
  }
}
