import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';

const _c_id = "id";
const _c_label = "label";
const _c_service = "service";
const _c_contactIsRequired = "contactIsRequired";
const _c_offerIsRequired = "offerIsRequired";
const _c_agentCanCreate = "agentCanCreate";

class ActiontypeDto {
  Optional<String> id;
  Optional<MultilingualStrDto> label;
  //
  Optional<ServiceDto?> service;
  //
  Optional<bool> contactIsRequired;
  Optional<bool> offerIsRequired;
  Optional<bool> agentCanCreate;

  ActiontypeDto({
    this.id = const None(),
    this.label = const None(),
    this.service = const None(),
    this.contactIsRequired = const None(),
    this.offerIsRequired = const None(),
    this.agentCanCreate = const None(),
  });

  ActiontypeDto copyWith({
    Optional<String>? id,
    Optional<MultilingualStrDto>? label,
    Optional<ServiceDto?>? service,
    Optional<bool>? contactIsRequired,
    Optional<bool>? offerIsRequired,
    Optional<bool>? agentCanCreate,
  }) =>
      ActiontypeDto(
        id: id ?? this.id,
        label: label ?? this.label,
        service: service ?? this.service,
        contactIsRequired: contactIsRequired ?? this.contactIsRequired,
        offerIsRequired: offerIsRequired ?? this.offerIsRequired,
        agentCanCreate: agentCanCreate ?? this.agentCanCreate,
      );

  ActiontypeDto copyIdOnly() => ActiontypeDto(id: this.id);

  factory ActiontypeDto.fromJson(Map json) {
    return ActiontypeDto(
      id: json.keyToOptional(_c_id),
      label: json.keyToOptional(_c_label, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      service: json.keyToOptional(_c_service, fTransform: (v) => ServiceDto.fromJson(v)),
      contactIsRequired: json.keyToOptional(_c_contactIsRequired),
      offerIsRequired: json.keyToOptional(_c_offerIsRequired),
      agentCanCreate: json.keyToOptional(_c_agentCanCreate),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (label is! None) _c_label: label.v.toJson(),
      if (service is! None) _c_service: service.v?.toJson(),
      if (contactIsRequired is! None) _c_contactIsRequired: contactIsRequired.v,
      if (offerIsRequired is! None) _c_offerIsRequired: offerIsRequired.v,
      if (agentCanCreate is! None) _c_agentCanCreate: agentCanCreate.v,
    };
  }
}
