import 'package:agentor_repositoryns/models/agent_dto.dart';
import 'package:agentor_repositoryns/models/contact_dto.dart';
import 'package:agentor_repositoryns/models/currency_dto.dart';
import 'package:agentor_repositoryns/models/demandstatus_dto.dart';
import 'package:agentor_repositoryns/models/offer_dto.dart';
import 'package:agentor_repositoryns/models/offer_rent_dto.dart';
import 'package:agentor_repositoryns/models/offer_sale_dto.dart';
import 'package:agentor_repositoryns/models/property_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class DemandDto {
  static const String _c_id = "id";
  static const String _c_agent = "agent";
  static const String _c_status = "status";
  static const String _c_customer = "customer";
  static const String _c_sale = "sale";
  static const String _c_rent = "rent";
  static const String _c_currency = "currency";
  static const String _c_property = "property";
  static const String _c_matchingsinfo = "matchingsinfo";
  static const String _c_notes = "notes";
  Optional<String> id;
  Optional<AgentDto> agent;
  Optional<DemandstatusDto> status;
  Optional<ContactDto?> customer;
  Optional<OfferSaleDto> sale;
  Optional<OfferRentDto> rent;
  Optional<CurrencyDto?> currency;
  Optional<PropertyDto> property;
  Optional<MatchingsinfoDto> matchingsinfo;
  Optional<String?> notes;

  DemandDto({
    this.id = const None(),
    this.agent = const None(),
    this.status = const None(),
    this.customer = const None(),
    this.sale = const None(),
    this.rent = const None(),
    this.currency = const None(),
    this.property = const None(),
    this.matchingsinfo = const None(),
    this.notes = const None(),
  });

  factory DemandDto.fromJson(Map json) {
    
    final result = DemandDto(
      id: json.keyToOptional(_c_id),
      agent: json.keyToOptional(_c_agent, fTransform: (v) => AgentDto.fromJson(v)),
      status: json.keyToOptional(_c_status, fTransform: (v) => DemandstatusDto.fromJson(v)),
      customer: json.keyToOptional(_c_customer, fTransform: (v) => ContactDto.fromJson(v)),
      sale: json.keyToOptional(_c_sale, fTransform: (v) => OfferSaleDto.fromJson(v)),
      rent: json.keyToOptional(_c_rent, fTransform: (v) => OfferRentDto.fromJson(v)),
      currency: json.keyToOptional(_c_currency, fTransform: (v) => CurrencyDto.fromJson(v)),
      property: json.keyToOptional(_c_property, fTransform: (v) => PropertyDto.fromJson(v)),
      matchingsinfo: json.keyToOptional(_c_matchingsinfo, fTransform: (v) => MatchingsinfoDto.fromJson(v)),
      notes: json.keyToOptional(_c_notes),
    );
    
    return result;
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (agent is! None) _c_agent: agent.v.toJson(),
      if (status is! None) _c_status: status.v.toJson(),
      if (customer is! None) _c_customer: customer.v?.toJson(),
      if (sale is! None) _c_sale: sale.v.toJson(),
      if (rent is! None) _c_rent: rent.v.toJson(),
      if (currency is! None) _c_currency: currency.v?.toJson(),
      if (property is! None) _c_property: property.v.toJson(),
      if (notes is! None) _c_notes: notes.v,
      // matchinginfo no se serializa: es una propiedad "computada"
    };
  }

  DemandDto copy() => DemandDto.fromJson(this.toJson());
}
