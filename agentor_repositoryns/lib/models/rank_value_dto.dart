import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';



const _C_VALUE = "value";
const _C_LABEL = "label";

class RankValueDto {
  Optional<int> value;
  Optional<MultilingualStrDto> label;

  RankValueDto({
    this.value = const None(),
    this.label = const None(),
  });

  factory RankValueDto.fromJson(Map json) {
    return RankValueDto(
      value: json.keyToOptional<int>(_C_VALUE),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (value is! None) _C_VALUE: value.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }

  String toString() {
    return this.value.vn?.toString() ?? "";
  }
}
