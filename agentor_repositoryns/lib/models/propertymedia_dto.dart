import 'package:agentor_repositoryns/models/media_dto.dart';
import 'package:agentor_repositoryns/models/property_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

class PropertymediaDto {
  static const _c_property = "property";
  static const _c_media = "media";
  static const _c_isFavourite = "isFavourite";

  Optional<PropertyDto> property;
  Optional<MediaDto> media;
  Optional<bool> isFavourite;

  PropertymediaDto({
    this.property = const None(),
    this.media = const None(),
    this.isFavourite = const None(),
  });

  PropertymediaDto copyWith({
    Optional<PropertyDto>? property,
    Optional<MediaDto>? media,
    Optional<bool>? isFavourite,
  }) =>
      PropertymediaDto(
        property: property ?? this.property,
        media: media ?? this.media,
        isFavourite: isFavourite ?? this.isFavourite,
      );

  factory PropertymediaDto.fromJson(Map json) {
    return PropertymediaDto(
      property: json.keyToOptional(_c_property, fTransform: (p) => PropertyDto.fromJson(p)),
      media: json.keyToOptional(_c_media, fTransform: (m) => MediaDto.fromJson(m)),
      isFavourite: json.keyToOptional<bool>(_c_isFavourite),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (property is! None) _c_property: property.v.toJson(),
      if (media is! None) _c_media: media.v.toJson(),
      if (isFavourite is! None) _c_isFavourite: isFavourite.v,
    };
  }
}
