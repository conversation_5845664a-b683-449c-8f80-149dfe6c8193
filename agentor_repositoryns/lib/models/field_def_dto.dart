import 'package:agentor_repositoryns/exception_utils.dart';
import 'package:agentor_repositoryns/json_helper.dart';
import 'package:agentor_repositoryns/models/code_value_dto.dart';
import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/models/rank_value_dto.dart';

import 'model_enum_extensions.dart';

const _C_CODE = "code";
const _C_TYPE = "type";
const _C_LABEL = "label";
const _C_VALUES = "values";
const _C_RANKS = "ranks";
const _C_MIN = "min";
const _C_MAX = "max";
const _C_READ_ONLY = "readOnly";

///
/// Definición de campo "definible" utilizado, principalmente, en los atributos de un inmueble
///
class FieldDefDto {
  final String code;
  final FieldDefType type;
  final MultilingualStrDto? label;
  final List<CodeValueDto>? values;
  final List<RankValueDto>? ranks;
  final double? min;
  final double? max;
  final bool? readOnly;

  FieldDefDto({
    required this.code,
    required this.type,
    this.label,
    this.values,
    this.ranks,
    this.min,
    this.max,
    this.readOnly = false,
  })  : assert(type != FieldDefType.code || type == FieldDefType.code && values != null),
        assert(type != FieldDefType.multicode || type == FieldDefType.multicode && values != null);

  FieldDefDto copyWith({
    String? code,
    FieldDefType? type,
    MultilingualStrDto? label,
    List<CodeValueDto>? values,
    List<RankValueDto>? ranks,
    double? min,
    double? max,
    bool? readOnly,
  }) =>
      FieldDefDto(
        code: code ?? this.code,
        type: type ?? this.type,
        label: label ?? this.label,
        values: values ?? this.values,
        ranks: ranks ?? this.ranks,
        min: min ?? this.min,
        max: max ?? this.max,
        readOnly: readOnly ?? this.readOnly,
      );

  factory FieldDefDto.fromJson(Map json) {
    final jsonValues = (json[_C_VALUES] as List<dynamic>?);
    final List<CodeValueDto>? values = jsonValues?.map((jsonValue) => CodeValueDto.fromJson(jsonValue)).toList();
    final jsonRanks = (json[_C_RANKS] as List<dynamic>?);
    final List<RankValueDto>? ranks = jsonRanks?.map((jsonRank) => RankValueDto.fromJson(jsonRank)).toList();
    return FieldDefDto(
      code:  JsonHelper.keyToUnserialized(json,_C_CODE) ?? doThrow("Missing code"),
      type: JsonHelper.keyToUnserialized<String>(json, _C_TYPE)?.toFieldDefType() ?? doThrow("Missing type"),
      label:  JsonHelper.keyToUnserialized(json, _C_LABEL, fTransform:(v)=>MultilingualStrDto.fromJson(v)),
      values: values,
      ranks: ranks,
      min: JsonHelper.keyToUnserialized<double>(json,_C_MIN),
      max: JsonHelper.keyToUnserialized<double>(json, _C_MAX),
      readOnly:  JsonHelper.keyToUnserialized<bool>(json,_C_READ_ONLY),
    );
  }

  Map<String, dynamic> toJson() {
    return {
       _C_CODE: code,
       _C_TYPE: type.enumToString(),
      if (label!=null) _C_LABEL: label?.toJson(),
      if (values!=null) _C_VALUES: values?.map((codeValue) => codeValue.toJson()).toList(),
      if (ranks!=null) _C_RANKS: ranks?.map((rank) => rank.toJson()).toList(),
      if (min!=null) _C_MIN: min,
      if (max!=null) _C_MAX: max,
      if (readOnly!=null) _C_READ_ONLY: readOnly,
    };
  }

  String toString() {
    return code;
  }
}

enum FieldDefType {
  code,
  multicode,
  rank,
  count,
  year,
  m2,
  currency,
  percent,
  yesnot,
  text,
  description,
  phonenumber,
  email,
  date,
  // code pero necesita "search"... no sabemos construirlo de forma automática de momento (debe hacerse manualmente)
  search,
}
