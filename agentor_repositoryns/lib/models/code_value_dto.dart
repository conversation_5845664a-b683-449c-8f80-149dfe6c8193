import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

const _C_CODE = "code";
const _C_LABEL = "label";

class CodeValueDto {
  Optional<String> code;
  Optional<MultilingualStrDto> label;

  CodeValueDto({
    this.code = const None(),
    this.label = const None(),
  });

  factory CodeValueDto.fromJson(Map json) {
    return CodeValueDto(
      code: json.keyToOptional(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (mp) => MultilingualStrDto.fromJson(mp)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
    };
  }

  String toString() {
    return '$code';
  }
}
