import 'package:agentor_repositoryns/optional.dart';

const _c_session_id = "id";

class StripeCheckoutSessionDto {
  final Optional<String> id;
  StripeCheckoutSessionDto({
    this.id = const None(),
  });

  factory StripeCheckoutSessionDto.fromJson(Map json) {
    return StripeCheckoutSessionDto(
      id: json.keyToOptional(_c_session_id),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_session_id: id.v,
    };
  }
}

const _c_conf_publicKey = "publicKey";

class StripeConfDto {
  final String publicKey;
  StripeConfDto({
    required this.publicKey,
  });
  factory StripeConfDto.fromJson(Map json) {
    return StripeConfDto(publicKey: json[_c_conf_publicKey]);
  }
  Map<String, dynamic> toJson() {
    return {
      _c_conf_publicKey: publicKey,
    };
  }
}
