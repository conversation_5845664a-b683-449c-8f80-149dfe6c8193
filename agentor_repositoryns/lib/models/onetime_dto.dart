import 'package:agentor_repositoryns/optional.dart';

const _c_message = "message";
const _c_code = "code";
const _c_success = "success";


class OnetimeDto {
  Optional<String> message;
  Optional<int> code;
  Optional<bool> success;

  OnetimeDto({
    this.message = const None(),
    this.code = const None(),
    this.success = const None(),
  });

  factory OnetimeDto.fromJson(Map<String, dynamic> json) {
    return OnetimeDto(
      message: json.keyToOptional<String>(_c_message),
      code: json.keyToOptional<int>(_c_code),
      success: json.keyToOptional<bool>(_c_success),
    );
  }
  Map<String, dynamic> toJson() {
    return {

      if (message is! None) _c_message: message.v,
      if (code is! None) _c_code: code.v,
      if (success is! None) _c_success: success.v,
    };
  }
}