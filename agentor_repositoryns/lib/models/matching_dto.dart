import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';


class MatchingDto {
  static const String _c_id = "id";
  static const String _c_offer = "offer";
  static const String _c_demand = "demand";
  static const String _c_reviewed = "reviewed";

  Optional<String> id;
  Optional<OfferDto> offer;
  Optional<DemandDto> demand;
  Optional<bool> reviewed;

  MatchingDto({
    this.id = const None(),
    this.offer = const None(),
    this.demand = const None(),
    this.reviewed = const None(),
  });

  factory MatchingDto.fromJson(Map json) {
    return MatchingDto(
      id: json.keyToOptional(_c_id),
      offer: json.keyToOptional(_c_offer, fTransform: (v) => OfferDto.fromJson(v)),
      demand: json.keyToOptional(_c_demand, fTransform: (v) => DemandDto.fromJson(v)),
      reviewed: json.keyToOptional(_c_reviewed),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_offer: id.v,
      if (offer is! None) _c_offer: offer.v.toJson(),
      if (demand is! None) _c_demand: demand.v.toJson(),
      if (reviewed is! None) _c_reviewed: reviewed.v,
    };
  }

  MatchingDto copyWith({
    Optional<String>? id,
    Optional<OfferDto>? offer,
    Optional<DemandDto>? demand,
    Optional<bool>? reviewed,
  }) =>
      MatchingDto(
        id: id ?? this.id,
        offer: offer ?? this.offer,
        demand: demand ?? this.demand,
        reviewed: reviewed ?? this.reviewed,
      );
}
