import 'package:agentor_repositoryns/optional.dart';

class SupportrequestDto {
  static const String _c_question = "question";

  Optional<String> question;

  SupportrequestDto({
    this.question = const None(),
  });

  SupportrequestDto copyWith({
    Optional<String>? question,
  }) {
    return SupportrequestDto(
      question: question ?? this.question,
    );
  }

  factory SupportrequestDto.fromJson(Map json) {
    //if (json == null)
    //  return null;
    //else
    return SupportrequestDto(
      question: json.keyToOptional(_c_question),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (question is! None) _c_question: question.v,
    };
  }
}
