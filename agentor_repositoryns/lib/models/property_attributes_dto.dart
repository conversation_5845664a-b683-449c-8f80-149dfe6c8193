import 'package:agentor_repositoryns/json_helper.dart';
import 'package:agentor_repositoryns/optional.dart';


class PropertyAttributesDto {
  static const _c_totalSurfaceM2 = 'totalSurfaceM2';
  static const _c_usefulSurfaceM2 = 'usefulSurfaceM2';
  static const _c_solarSurfaceM2 = 'solarSurfaceM2';
  static const _c_constructionYear = 'constructionYear';
  static const _c_statusCode = 'statusCode';
  static const _c_conservationStatusCode = 'conservationStatusCode';
  static const _c_individualBedroomsCount = 'individualBedroomsCount';
  static const _c_doubleBedroomsCount = 'doubleBedroomsCount';
  static const _c_suiteBedroomsCount = 'suiteBedroomsCount';
  static const _c_totalBedroomsCount = 'totalBedroomsCount';
  static const _c_bathroomsCount = 'bathroomsCount';
  static const _c_bathroomsNotes = 'bathroomsNotes';
  static const _c_toiletsCount = 'toiletsCount';
  static const _c_toiletsNotes = 'toiletsNotes';
  static const _c_buddleHas = 'buddleHas';
  static const _c_kitchenHas = 'kitchenHas';
  static const _c_dinningRoomHas = 'dinningRoomHas';
  static const _c_dinningRoomNotes = 'dinningRoomNotes';
  static const _c_storageRoomHas = 'storageRoomHas';
  static const _c_balconyHas = 'balconyHas';
  static const _c_balconyNotes = 'balconyNotes';
  static const _c_terraceHas = 'terraceHas';
  static const _c_builtInCabinetsCount = 'builtInCabinetsCount';
  static const _c_doubleGlassesHas = 'doubleGlassesHas';
  static const _c_externalJoineryCode = 'externalJoineryCode';
  static const _c_externalJoineryNotes = 'externalJoineryNotes';
  static const _c_groundCodes = 'groundCodes';
  static const _c_waterSupplyHas = 'waterSupplyHas';
  static const _c_waterSupplyNotes = 'waterSupplyNotes';
  static const _c_powerSupplyHas = 'powerSupplyHas';
  static const _c_powerSupplyNotes = 'powerSupplyNotes';
  static const _c_gasSupplyHas = 'gasSupplyHas';
  static const _c_airConditioningCode = 'airConditioningCode';
  static const _c_heatingCode = 'heatingCode';
  static const _c_fireplaceHas = 'fireplaceHas';
  static const _c_intercomHas = 'intercomHas';
  static const _c_intercomNotes = 'intercomNotes';
  static const _c_reinforcedDoorHas = 'reinforcedDoorHas';
  static const _c_reinforcedDoorNotes = 'reinforcedDoorNotes';
  static const _c_alarmSystemHas = 'alarmSystemHas';
  static const _c_elevatorHas = 'elevatorHas';
  static const _c_handicappedAccessibleIs = 'handicappedAccessibleIs';
  static const _c_furnishedIs = 'furnishedIs';
  static const _c_gardenCode = 'gardenCode';
  static const _c_outsideAreaCode = 'outsideAreaCode';
  static const _c_outsideAreaNotes = 'outsideAreaNotes';
  static const _c_swimmingPoolCode = 'swimmingPoolCode';
  static const _c_parkingPlacesCount = 'parkingPlacesCount';
  static const _c_optionalParkingIs = 'optionalParkingIs';
  static const _c_facadeCodes = 'facadeCodes';
  static const _c_orientationCodes = 'orientationCodes';
  static const _c_sunnyIs = 'sunnyIs';
  static const _c_sunnyNotes = 'sunnyNotes';
  static const _c_communityFeesAmount = 'communityFeesAmount';
  static const _c_neighborsPerFloorCount = 'neighborsPerFloorCount';
  static const _c_buildingFloorsCount = 'buildingFloorsCount';
  static const _c_floorCode = 'floorCode';
  static const _c_energyCertificateCode = 'energyCertificateCode';
  static const _c_consumptionLevelCode = 'consumptionLevelCode';
  static const _c_emissionLevelCode = 'emissionLevelCode';

  Optional<double> totalSurfaceM2;
  Optional<double?> usefulSurfaceM2;
  Optional<double?> solarSurfaceM2;
  Optional<int?> constructionYear;
  Optional<String?> statusCode;
  Optional<String?> conservationStatusCode;
  Optional<int?> individualBedroomsCount;
  Optional<int?> doubleBedroomsCount;
  Optional<int?> suiteBedroomsCount;
  Optional<int?> totalBedroomsCount;
  Optional<int?> bathroomsCount;
  Optional<String?> bathroomsNotes;
  Optional<int?> toiletsCount;
  Optional<String?> toiletsNotes;
  Optional<bool?> buddleHas;
  Optional<bool?> kitchenHas;
  Optional<bool?> dinningRoomHas;
  Optional<String?> dinningRoomNotes;
  Optional<bool?> storageRoomHas;
  Optional<bool?> balconyHas;
  Optional<String?> balconyNotes;
  Optional<bool?> terraceHas;
  Optional<int?> builtInCabinetsCount;
  Optional<bool?> doubleGlassesHas;
  Optional<String?> externalJoineryCode;
  Optional<String?> externalJoineryNotes;
  Optional<List<String>?> groundCodes;
  Optional<bool?> waterSupplyHas;
  Optional<String?> waterSupplyNotes;
  Optional<bool?> powerSupplyHas;
  Optional<String?> powerSupplyNotes;
  Optional<bool?> gasSupplyHas;
  Optional<String?> airConditioningCode;
  Optional<String?> heatingCode;
  Optional<bool?> fireplaceHas;
  Optional<bool?> intercomHas;
  Optional<String?> intercomNotes;
  Optional<bool?> reinforcedDoorHas;
  Optional<String?> reinforcedDoorNotes;
  Optional<bool?> alarmSystemHas;
  Optional<bool?> elevatorHas;
  Optional<bool?> handicappedAccessibleIs;
  Optional<bool?> furnishedIs;
  Optional<String?> gardenCode;
  Optional<String?> outsideAreaCode;
  Optional<int?> outsideAreaNotes;
  Optional<String?> swimmingPoolCode;
  Optional<int?> parkingPlacesCount;
  Optional<bool?> optionalParkingIs;
  Optional<List<String>?> facadeCodes;
  Optional<List<String>?> orientationCodes;
  Optional<bool?> sunnyIs;
  Optional<String?> sunnyNotes;
  Optional<double?> communityFeesAmount;
  Optional<int?> neighborsPerFloorCount;
  Optional<int?> buildingFloorsCount;
  Optional<String?> floorCode;
  Optional<String?> energyCertificateCode;
  Optional<String?> consumptionLevelCode;
  Optional<String?> emissionLevelCode;

  PropertyAttributesDto({
    this.totalSurfaceM2=const None(),
    this.usefulSurfaceM2=const None(),
    this.solarSurfaceM2=const None(),
    this.constructionYear=const None(),
    this.statusCode=const None(),
    this.conservationStatusCode=const None(),
    this.individualBedroomsCount=const None(),
    this.doubleBedroomsCount=const None(),
    this.suiteBedroomsCount=const None(),
    this.totalBedroomsCount=const None(),
    this.bathroomsCount=const None(),
    this.bathroomsNotes=const None(),
    this.toiletsCount=const None(),
    this.toiletsNotes=const None(),
    this.buddleHas=const None(),
    this.kitchenHas=const None(),
    this.dinningRoomHas=const None(),
    this.dinningRoomNotes=const None(),
    this.storageRoomHas=const None(),
    this.balconyHas=const None(),
    this.balconyNotes=const None(),
    this.terraceHas=const None(),
    this.builtInCabinetsCount=const None(),
    this.doubleGlassesHas=const None(),
    this.externalJoineryCode=const None(),
    this.externalJoineryNotes=const None(),
    this.groundCodes=const None(),
    this.waterSupplyHas=const None(),
    this.waterSupplyNotes=const None(),
    this.powerSupplyHas=const None(),
    this.powerSupplyNotes=const None(),
    this.gasSupplyHas=const None(),
    this.airConditioningCode=const None(),
    this.heatingCode=const None(),
    this.fireplaceHas=const None(),
    this.intercomHas=const None(),
    this.intercomNotes=const None(),
    this.reinforcedDoorHas=const None(),
    this.reinforcedDoorNotes=const None(),
    this.alarmSystemHas=const None(),
    this.elevatorHas=const None(),
    this.handicappedAccessibleIs=const None(),
    this.furnishedIs=const None(),
    this.gardenCode=const None(),
    this.outsideAreaCode=const None(),
    this.outsideAreaNotes=const None(),
    this.swimmingPoolCode=const None(),
    this.parkingPlacesCount=const None(),
    this.optionalParkingIs=const None(),
    this.facadeCodes=const None(),
    this.orientationCodes=const None(),
    this.sunnyIs=const None(),
    this.sunnyNotes=const None(),
    this.communityFeesAmount=const None(),
    this.neighborsPerFloorCount=const None(),
    this.buildingFloorsCount=const None(),
    this.floorCode=const None(),
    this.energyCertificateCode=const None(),
    this.consumptionLevelCode=const None(),
    this.emissionLevelCode=const None(),
  });

  factory PropertyAttributesDto.initWithNulls() {
    const json = {
      // totalSurfaceM2 no admite nulos
      //'$_c_totalSurfaceM2': null,
      '$_c_usefulSurfaceM2': null,
      '$_c_solarSurfaceM2': null,
      '$_c_constructionYear': null,
      '$_c_statusCode': null,
      '$_c_conservationStatusCode': null,
      '$_c_individualBedroomsCount': null,
      '$_c_doubleBedroomsCount': null,
      '$_c_suiteBedroomsCount': null,
      '$_c_totalBedroomsCount': 0,
      '$_c_bathroomsCount': null,
      '$_c_bathroomsNotes': null,
      '$_c_toiletsCount': null,
      '$_c_toiletsNotes': null,
      '$_c_buddleHas': null,
      '$_c_kitchenHas': null,
      '$_c_dinningRoomHas': null,
      '$_c_dinningRoomNotes': null,
      '$_c_storageRoomHas': null,
      '$_c_balconyHas': null,
      '$_c_balconyNotes': null,
      '$_c_terraceHas': null,
      '$_c_builtInCabinetsCount': null,
      '$_c_doubleGlassesHas': null,
      '$_c_externalJoineryCode': null,
      '$_c_externalJoineryNotes': null,
      '$_c_groundCodes': <String>[],
      '$_c_waterSupplyHas': null,
      '$_c_waterSupplyNotes': null,
      '$_c_powerSupplyHas': null,
      '$_c_powerSupplyNotes': null,
      '$_c_gasSupplyHas': null,
      '$_c_airConditioningCode': null,
      '$_c_heatingCode': null,
      '$_c_fireplaceHas': null,
      '$_c_intercomHas': null,
      '$_c_intercomNotes': null,
      '$_c_reinforcedDoorHas': null,
      '$_c_reinforcedDoorNotes': null,
      '$_c_alarmSystemHas': null,
      '$_c_elevatorHas': null,
      '$_c_handicappedAccessibleIs': null,
      '$_c_furnishedIs': null,
      '$_c_gardenCode': null,
      '$_c_outsideAreaCode': null,
      '$_c_outsideAreaNotes': null,
      '$_c_swimmingPoolCode': null,
      '$_c_parkingPlacesCount': null,
      '$_c_optionalParkingIs': null,
      '$_c_facadeCodes': <String>[],
      '$_c_orientationCodes': <String>[],
      '$_c_sunnyIs': null,
      '$_c_sunnyNotes': null,
      '$_c_communityFeesAmount': null,
      '$_c_neighborsPerFloorCount': null,
      '$_c_buildingFloorsCount': null,
      '$_c_floorCode': null,
      '$_c_energyCertificateCode': null,
      '$_c_consumptionLevelCode': null,
      '$_c_emissionLevelCode': null,
    };

    var dto = PropertyAttributesDto.fromJson(json);
    return dto;
  }
  factory PropertyAttributesDto.fromJson(Map<dynamic, dynamic> json) {
    final data = PropertyAttributesDto(
      totalSurfaceM2: json.keyToOptional(_c_totalSurfaceM2),
      usefulSurfaceM2: json.keyToOptional(_c_usefulSurfaceM2),
      solarSurfaceM2: json.keyToOptional(_c_solarSurfaceM2),
      constructionYear: json.keyToOptional(_c_constructionYear),
      statusCode: json.keyToOptional(_c_statusCode),
      conservationStatusCode: json.keyToOptional(_c_conservationStatusCode),
      individualBedroomsCount: json.keyToOptional(_c_individualBedroomsCount),
      doubleBedroomsCount: json.keyToOptional(_c_doubleBedroomsCount),
      suiteBedroomsCount: json.keyToOptional(_c_suiteBedroomsCount),
      totalBedroomsCount: json.keyToOptional(_c_totalBedroomsCount),
      bathroomsCount: json.keyToOptional(_c_bathroomsCount),
      bathroomsNotes: json.keyToOptional(_c_bathroomsNotes),
      toiletsCount: json.keyToOptional(_c_toiletsCount),
      toiletsNotes: json.keyToOptional(_c_toiletsNotes),
      buddleHas: json.keyToOptional(_c_buddleHas),
      kitchenHas: json.keyToOptional(_c_kitchenHas),
      dinningRoomHas: json.keyToOptional(_c_dinningRoomHas),
      dinningRoomNotes: json.keyToOptional(_c_dinningRoomNotes),
      storageRoomHas: json.keyToOptional(_c_storageRoomHas),
      balconyHas: json.keyToOptional(_c_balconyHas),
      balconyNotes: json.keyToOptional(_c_balconyNotes),
      terraceHas: json.keyToOptional(_c_terraceHas),
      builtInCabinetsCount: json.keyToOptional(_c_builtInCabinetsCount),
      doubleGlassesHas: json.keyToOptional(_c_doubleGlassesHas),
      externalJoineryCode: json.keyToOptional(_c_externalJoineryCode),
      externalJoineryNotes: json.keyToOptional(_c_externalJoineryNotes),
      groundCodes: json.keyToOptional(_c_groundCodes, fTransform: (v)=>JsonHelper.unserializeList<String>(v)),
      waterSupplyHas: json.keyToOptional(_c_waterSupplyHas),
      waterSupplyNotes: json.keyToOptional(_c_waterSupplyNotes),
      powerSupplyHas: json.keyToOptional(_c_powerSupplyHas),
      powerSupplyNotes: json.keyToOptional(_c_powerSupplyNotes),
      gasSupplyHas: json.keyToOptional(_c_gasSupplyHas),
      airConditioningCode: json.keyToOptional(_c_airConditioningCode),
      heatingCode: json.keyToOptional(_c_heatingCode),
      fireplaceHas: json.keyToOptional(_c_fireplaceHas),
      intercomHas: json.keyToOptional(_c_intercomHas),
      intercomNotes: json.keyToOptional(_c_intercomNotes),
      reinforcedDoorHas: json.keyToOptional(_c_reinforcedDoorHas),
      reinforcedDoorNotes: json.keyToOptional(_c_reinforcedDoorNotes),
      alarmSystemHas: json.keyToOptional(_c_alarmSystemHas),
      elevatorHas: json.keyToOptional(_c_elevatorHas),
      handicappedAccessibleIs: json.keyToOptional(_c_handicappedAccessibleIs),
      furnishedIs: json.keyToOptional(_c_furnishedIs),
      gardenCode: json.keyToOptional(_c_gardenCode),
      outsideAreaCode: json.keyToOptional(_c_outsideAreaCode),
      outsideAreaNotes: json.keyToOptional(_c_outsideAreaNotes),
      swimmingPoolCode: json.keyToOptional(_c_swimmingPoolCode),
      parkingPlacesCount: json.keyToOptional(_c_parkingPlacesCount),
      optionalParkingIs: json.keyToOptional(_c_optionalParkingIs),
      facadeCodes: json.keyToOptional(_c_facadeCodes, fTransform: (v)=>JsonHelper.unserializeList<String>(v)),
      orientationCodes: json.keyToOptional(_c_orientationCodes, fTransform: (v)=>JsonHelper.unserializeList<String>(v)),
      sunnyIs: json.keyToOptional(_c_sunnyIs),
      sunnyNotes: json.keyToOptional(_c_sunnyNotes),
      communityFeesAmount: json.keyToOptional(_c_communityFeesAmount),
      neighborsPerFloorCount: json.keyToOptional(_c_neighborsPerFloorCount),
      buildingFloorsCount: json.keyToOptional(_c_buildingFloorsCount),
      floorCode: json.keyToOptional(_c_floorCode),
      energyCertificateCode: json.keyToOptional(_c_energyCertificateCode),
      consumptionLevelCode: json.keyToOptional(_c_consumptionLevelCode),
      emissionLevelCode: json.keyToOptional(_c_emissionLevelCode),
    );
    return data;
  }
/*
  static PropertyAttributesDto? keyToOptional(Map<String, dynamic> json, String key) {
    if (json.containsKey(key))
      return PropertyAttributesDto.fromJson(json[key]);
    else
      return null;
  }
*/
  Map<String, dynamic> toJson() {
    return {
      if (totalSurfaceM2 is! None) _c_totalSurfaceM2: totalSurfaceM2.v,
      if (usefulSurfaceM2 is! None) _c_usefulSurfaceM2: usefulSurfaceM2.v,
      if (solarSurfaceM2 is! None) _c_solarSurfaceM2: solarSurfaceM2.v,
      if (constructionYear is! None) _c_constructionYear: constructionYear.v,
      if (statusCode is! None) _c_statusCode: statusCode.v,
      if (conservationStatusCode is! None) _c_conservationStatusCode: conservationStatusCode.v,
      if (individualBedroomsCount is! None) _c_individualBedroomsCount: individualBedroomsCount.v,
      if (doubleBedroomsCount is! None) _c_doubleBedroomsCount: doubleBedroomsCount.v,
      if (suiteBedroomsCount is! None) _c_suiteBedroomsCount: suiteBedroomsCount.v,
      if (totalBedroomsCount is! None) _c_totalBedroomsCount: totalBedroomsCount.v,
      if (bathroomsCount is! None) _c_bathroomsCount: bathroomsCount.v,
      if (bathroomsNotes is! None) _c_bathroomsNotes: bathroomsNotes.v,
      if (toiletsCount is! None) _c_toiletsCount: toiletsCount.v,
      if (toiletsNotes is! None) _c_toiletsNotes: toiletsNotes.v,
      if (buddleHas is! None) _c_buddleHas: buddleHas.v,
      if (kitchenHas is! None) _c_kitchenHas: kitchenHas.v,
      if (dinningRoomHas is! None) _c_dinningRoomHas: dinningRoomHas.v,
      if (dinningRoomNotes is! None) _c_dinningRoomNotes: dinningRoomNotes.v,
      if (storageRoomHas is! None) _c_storageRoomHas: storageRoomHas.v,
      if (balconyHas is! None) _c_balconyHas: balconyHas.v,
      if (balconyNotes is! None) _c_balconyNotes: balconyNotes.v,
      if (terraceHas is! None) _c_terraceHas: terraceHas.v,
      if (builtInCabinetsCount is! None) _c_builtInCabinetsCount: builtInCabinetsCount.v,
      if (doubleGlassesHas is! None) _c_doubleGlassesHas: doubleGlassesHas.v,
      if (externalJoineryCode is! None) _c_externalJoineryCode: externalJoineryCode.v,
      if (externalJoineryNotes is! None) _c_externalJoineryNotes: externalJoineryNotes.v,
      if (groundCodes is! None) _c_groundCodes: groundCodes.v,
      if (waterSupplyHas is! None) _c_waterSupplyHas: waterSupplyHas.v,
      if (waterSupplyNotes is! None) _c_waterSupplyNotes: waterSupplyNotes.v,
      if (powerSupplyHas is! None) _c_powerSupplyHas: powerSupplyHas.v,
      if (powerSupplyNotes is! None) _c_powerSupplyNotes: powerSupplyNotes.v,
      if (gasSupplyHas is! None) _c_gasSupplyHas: gasSupplyHas.v,
      if (airConditioningCode is! None) _c_airConditioningCode: airConditioningCode.v,
      if (heatingCode is! None) _c_heatingCode: heatingCode.v,
      if (fireplaceHas is! None) _c_fireplaceHas: fireplaceHas.v,
      if (intercomHas is! None) _c_intercomHas: intercomHas.v,
      if (intercomNotes is! None) _c_intercomNotes: intercomNotes.v,
      if (reinforcedDoorHas is! None) _c_reinforcedDoorHas: reinforcedDoorHas.v,
      if (reinforcedDoorNotes is! None) _c_reinforcedDoorNotes: reinforcedDoorNotes.v,
      if (alarmSystemHas is! None) _c_alarmSystemHas: alarmSystemHas.v,
      if (elevatorHas is! None) _c_elevatorHas: elevatorHas.v,
      if (handicappedAccessibleIs is! None) _c_handicappedAccessibleIs: handicappedAccessibleIs.v,
      if (furnishedIs is! None) _c_furnishedIs: furnishedIs.v,
      if (gardenCode is! None) _c_gardenCode: gardenCode.v,
      if (outsideAreaCode is! None) _c_outsideAreaCode: outsideAreaCode.v,
      if (outsideAreaNotes is! None) _c_outsideAreaNotes: outsideAreaNotes.v,
      if (swimmingPoolCode is! None) _c_swimmingPoolCode: swimmingPoolCode.v,
      if (parkingPlacesCount is! None) _c_parkingPlacesCount: parkingPlacesCount.v,
      if (optionalParkingIs is! None) _c_optionalParkingIs: optionalParkingIs.v,
      if (facadeCodes is! None) _c_facadeCodes: facadeCodes.v,
      if (orientationCodes is! None) _c_orientationCodes: orientationCodes.v,
      if (sunnyIs is! None) _c_sunnyIs: sunnyIs.v,
      if (sunnyNotes is! None) _c_sunnyNotes: sunnyNotes.v,
      if (communityFeesAmount is! None) _c_communityFeesAmount: communityFeesAmount.v,
      if (neighborsPerFloorCount is! None) _c_neighborsPerFloorCount: neighborsPerFloorCount.v,
      if (buildingFloorsCount is! None) _c_buildingFloorsCount: buildingFloorsCount.v,
      if (floorCode is! None) _c_floorCode: floorCode.v,
      if (energyCertificateCode is! None) _c_energyCertificateCode: energyCertificateCode.v,
      if (consumptionLevelCode is! None) _c_consumptionLevelCode: consumptionLevelCode.v,
      if (emissionLevelCode is! None) _c_emissionLevelCode: emissionLevelCode.v,
    };
  }
}
