import 'package:agentor_repositoryns/models/agenttype_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

const String _c_id = "id";
const String _c_firstName = "firstName";
const String _c_lastName = "lastName";
const String _c_email = "email";
const String _c_mobile = "mobile";
const String _c_type = "type";

final _c_internal_agent_min_id = "9000000000000000000"; // Ej: Crawler de idealista es   9223372036854775807

class AgentDto {
  Optional<String> id;
  Optional<String> firstName;
  Optional<String?> lastName;
  Optional<String> email;
  Optional<String?> mobile;
  Optional<AgenttypeDto> type;
  // Agent roles: campos calculados (no se serializan al hacer toJson)
  Optional<bool> isPremium;
  Optional<bool> isIndividual;
  Optional<bool> isInternal;

  AgentDto({
    this.id = const None(),
    this.firstName = const None(),
    this.lastName = const None(),
    this.email = const None(),
    this.mobile = const None(),
    this.type = const None(),
    this.isPremium = const None(),
    this.isIndividual = const None(),
    this.isInternal = const None(),
  });

  Optional<String> get name {
    if (firstName.vn != null && lastName.vn != null)
      return Some("${firstName.vn} ${lastName.v}");
    else if (firstName.vn != null)
      return firstName;
    else if (lastName.vn != null)
      return Some<String>(lastName.v as String);
    else
      return None();
  }

  factory AgentDto.fromJson(Map json) {
    final email = json.keyToOptional<String>(_c_email);
    final id = json.keyToOptional<String>(_c_id);
    final type = json.keyToOptional(_c_type, fTransform: (v) => AgenttypeDto.fromJson(v));
    final isPremium = Some(
      email.vn != null 
      ? email.vn!.endsWith("agenteunico.net") || email.vn!.endsWith("percentservicios.com")
      : false
    );
    final isIndividual = Some(type.vn?.code.vn == AgenttypeCode.individual);
    final isInternal = id is! None ? Some(id.v.compareTo(_c_internal_agent_min_id) >= 0) : None<bool>();
    return AgentDto(
      id: id,
      firstName: json.keyToOptional(_c_firstName),
      lastName: json.keyToOptional<String?>(_c_lastName),
      email: email,
      mobile: json.keyToOptional<String?>(_c_mobile),
      type: type,
      isPremium: isPremium,
      isIndividual: isIndividual,
      isInternal: isInternal,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (firstName is! None) _c_firstName: firstName.v,
      if (lastName is! None) _c_lastName: lastName.v,
      if (email is! None) _c_email: email.v,
      if (mobile is! None) _c_mobile: mobile.v,
      if (type is! None) _c_type: type.v.toJson(),
    };
  }

  AgentDto copyWith({
    Optional<String>? id,
    Optional<String>? firstName,
    Optional<String?>? lastName,
    Optional<String>? email,
    Optional<String?>? mobile,
    Optional<bool>? isPremium,
    Optional<bool>? isSeller,
    Optional<bool>? isInternal,
  }) {
    return AgentDto(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      isPremium: isPremium ?? this.isPremium,
      isIndividual: isSeller ?? this.isIndividual,
      isInternal: isInternal ?? this.isInternal,
    );
  }
}
