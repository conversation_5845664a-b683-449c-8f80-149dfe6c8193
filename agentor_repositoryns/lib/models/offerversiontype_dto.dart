import 'package:agentor_repositoryns/models/multilingual_str_dto.dart';
import 'package:agentor_repositoryns/optional.dart';

const _C_CODE = "code";
const _C_LABEL = "label";
const _C_DISCLAIMER = "disclaimer";

class OfferversiontypeDto {
  Optional<String> code;
  Optional<MultilingualStrDto> label;
  Optional<MultilingualStrDto> disclaimer;

  OfferversiontypeDto({
    this.code = const None(),
    this.label = const None(),
    this.disclaimer = const None(),
  });

  factory OfferversiontypeDto.fromJson(Map json) {
    return OfferversiontypeDto(
      code: json.keyToOptional(_C_CODE),
      label: json.keyToOptional(_C_LABEL, fTransform: (v) => MultilingualStrDto.fromJson(v)),
      disclaimer: json.keyToOptional(_C_DISCLAIMER, fTransform: (v) => MultilingualStrDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (code is! None) _C_CODE: code.v,
      if (label is! None) _C_LABEL: label.v.toJson(),
      if (disclaimer is! None) _C_DISCLAIMER: disclaimer.v.toJson(),
    };
  }
}
