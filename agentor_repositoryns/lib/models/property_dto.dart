import 'package:agentor_repositoryns/models/models.dart';
import 'package:agentor_repositoryns/optional.dart';

class PropertyDto {
  static const _c_id = "id";
  static const _c_type = "type";
  static const _c_subtype = "subtype";
  static const _c_zone = "zone";
  static const _c_cadastralReference = "cadastralReference";
  static const _c_favouritePicture = "favouritePicture"; // Read only!!!
  static const _c_address = "address";
  static const _c_attributes = "attributes";
  static const _c_propertymedias = "propertymedias";

  Optional<String> id;
  Optional<PropertytypeDto> type;
  Optional<PropertysubtypeDto?> subtype;
  Optional<PropertyzoneDto?> zone;
  Optional<String?> cadastralReference;
  Optional<MediaDto?> favouritePicture;
  Optional<PropertyAddressDto> address;
  Optional<PropertyAttributesDto> attributes;
  Optional<List<PropertymediaDto>> propertymedias;

  PropertyDto({
    this.id = const None(),
    this.type = const None(),
    this.subtype = const None(),
    this.zone = const None(),
    this.cadastralReference = const None(),
    this.address = const None(),
    this.attributes = const None(),
    this.favouritePicture = const None(),
    this.propertymedias = const None(),
  });

  PropertyDto copyWith({
    Optional<String>? id,
    Optional<PropertytypeDto>? type,
    Optional<PropertysubtypeDto?>? subtype,
    Optional<PropertyzoneDto?>? zone,
    Optional<String?>? cadastralReference,
    Optional<MediaDto?>? favouritePicture,
    Optional<PropertyAddressDto>? address,
    Optional<PropertyAttributesDto>? attributes,
    Optional<List<PropertymediaDto>>? propertymedias,
  }) =>
      PropertyDto(
        id: id ?? this.id,
        type: type ?? this.type,
        subtype: subtype ?? this.subtype,
        zone: zone ?? this.zone,
        cadastralReference: cadastralReference ?? this.cadastralReference,
        favouritePicture: favouritePicture ?? this.favouritePicture,
        address: address ?? this.address,
        attributes: attributes ?? this.attributes,
        propertymedias: propertymedias ?? this.propertymedias,
      );

  factory PropertyDto.fromJson(Map<dynamic, dynamic> json) {
    return PropertyDto(
      id: json.keyToOptional(_c_id),
      type: json.keyToOptional(_c_type, fTransform: (v) => PropertytypeDto.fromJson(v)),
      subtype: json.keyToOptional(_c_subtype, fTransform: (v) => PropertysubtypeDto.fromJson(v)),
      zone: json.keyToOptional(_c_zone, fTransform: (v) => PropertyzoneDto.fromJson(v)),
      address: json.keyToOptional(_c_address, fTransform: (v) => PropertyAddressDto.fromJson(v)),
      attributes: json.keyToOptional(_c_attributes, fTransform: (v) => PropertyAttributesDto.fromJson(v)),
      favouritePicture: json.keyToOptional(_c_favouritePicture, fTransform: (v) => MediaDto.fromJson(v)),
      cadastralReference: json.keyToOptional(_c_cadastralReference),
      propertymedias: json.keyToOptionalList(_c_propertymedias, fTransform: (v) => PropertymediaDto.fromJson(v)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id is! None) _c_id: id.v,
      if (type is! None) _c_type: type.v.toJson(),
      if (subtype is! None) _c_subtype: subtype.v?.toJson(),
      if (zone is! None) _c_zone: zone.v?.toJson(),
      if (address is! None) _c_address: address.v.toJson(),
      if (attributes is! None) _c_attributes: attributes.v.toJson(),
      if (cadastralReference is! None) _c_cadastralReference: cadastralReference.v,
      if (propertymedias is! None) _c_propertymedias: propertymedias.v.map((media) => media.toJson()).toList(),
      // No incluimos favouritePicture: es una propiedad calculada... en algún momento se puede incluir si en el servidor se interpreta correctamente
    };
  }
}
