import 'package:agentor_repositoryns/json_helper.dart';
import 'package:equatable/equatable.dart';

class Some<T> extends Optional<T> {
  Some(T value) : super._([value]);
}

class None<T> extends Optional<T> {
  const None() : super._(const []);
}

///
/// Nota implementación.  Debería ser Some(true),  pero Some no puede ser usado como const  debido a la conversión value --> [value]
///
const True = Optional._(const [true]);

///
/// Nota implementación.  Debería ser Some(false),  pero Some no puede ser usado como const  debido a la conversión value --> [value]
///
const False = Optional._(const [false]);
//class YFalse extends YOptional<bool> {
//  const YFalse(): super._(const [false]);
//}

class Optional<T> extends Iterable<T> implements Equatable {
  final List<T> _value;

  const Optional._(this._value);

  bool get isNone => _value.isEmpty;
  bool get isSome => _value.isNotEmpty;
  T get value => _value.isNotEmpty ? this._value[0] : _doThrow("XN has not value");
  T get v => this.value;
  T? get valueOrNull => _value.isNotEmpty ? this._value[0] : null;
  T? get vn => this.valueOrNull;

  T valueOrDefault(T defaultValue) => _value.isNotEmpty ? this._value[0] : defaultValue;
  T vd(T defaultValue) => this.valueOrDefault(defaultValue);

  Optional<R> map<R>(R Function(T) mapper) => _value.isEmpty ? None<R>() : Some<R>(mapper(_value[0]));
  Optional<R> flatMap<R>(Optional<R> Function(T) mapper) => _value.isEmpty ? None<R>() : mapper(_value[0]);

  static R _doThrow<R>(String txt) {
    throw Exception(txt);
  }

  factory Optional.fromJson(dynamic v, {T Function(dynamic)? fTransform}) {
    if (v == null) {
      final isNullable = T.toString().endsWith("?");
      return isNullable ? Some<T>(v as T) : _doThrow<Some<T>>("null is not a valid value for ${T.toString()}");
    } else {
      T value = fTransform != null ? fTransform(v) : JsonHelper.unserializeValue(v);
      return Some<T>(value);
    }
  }

  @override
  Iterator<T> get iterator => this._value.iterator;

  @override
  List<Object?> get props => [this._value];

  @override
  bool? get stringify => false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Optional<T> &&
          (other._value.length == this._value.length) &&
          (other._value.length == 0 || other._value[0] == this._value[0]));

  @override
  int get hashCode => this._value.length | (this._value.length == 0 ? 0 : this._value[0].hashCode);
}

extension OptionalList<T> on Optional<List<T>> {
  /// Mapea los elementos de una lista contenida en un opcional obteniendo un nuevo opcional
  Optional<List<R>> mapList<R>(R Function(T) fTransform) => this.map((List<T> l) => l.map(fTransform).toList());
}

extension MapToOptional on Map<dynamic, dynamic> {
  /**
   * Si el Map contiene la clave indicada, devuelve un objeto Some( x ) donde x es el resultado de aplicar fTransform al valor asociado a la clave
   * Casos especiales:  null
   *   Si T adminte null, se devuelve null sin aplicar fTransform
   *   Si T NO admite null, se lanza una excepción.
   */
  Optional<T> keyToOptional<T>(String key, {T Function(dynamic)? fTransform}) {
    if (this.containsKey(key)) {
      var v = this[key];
      if (v == null) {
        final isNullable = T.toString().endsWith("?");
        if (isNullable)
          return Some<T>(v as T);
        else
          throw Exception("$key: null is not a valid value for ${T.toString()}. Map object is $this");
      } else {
        try {
          T value = fTransform != null ? fTransform(v) : JsonHelper.unserializeValue(v);
          return Some<T>(value);
        } catch (e) {
          print("Error keyToYOptional($key): $e");
          throw e;
        }
      }
    } else {
      return None<T>();
    }
  }

  ///
  /// Dependiendo de si el Map contiene la clave indicada, se devolverá Optional o None
  ///
  /// Si el Map contiene la clave, se asume que el valor es un Iterable (NO NULO) y se devuelve el resultado de aplicar fTransform a todos sus elementos.
  /// En caso de no indicar fTransform, se aplicará la transformación por defecto (solo válida para tipos simples)
  ///
  Optional<List<T>> keyToOptionalList<T>(String key, {T Function(dynamic)? fTransform}) {
    if (this.containsKey(key)) {
      var v = this[key];
      if (v == null) {
        throw Exception("$key: null is not a valid value for List<${T.toString()}>. Map object is $this");
      } else {
        try {
          var values = (v as Iterable<dynamic>)
              .map((listItem) => fTransform != null ? fTransform(listItem) : JsonHelper.unserializeValue<T>(listItem))
              .toList();
          return Some(values);
        } catch (e) {
          print("Error keyToYOptional($key): $e");
          throw e;
        }
      }
    } else {
      return None<List<T>>();
    }
  }
}

extension IterableExtension<T> on Iterable<T> {
  ///
  /// Convierte un Iterable de 0 o 1 elementos en Optional (None/Some)
  /// Se devuelve:
  /// * None() Si iterable no contiene elementos
  /// * Some(first) Si iterable contiene 1 o más elementos
  Optional<T> toOptional() => this.isEmpty ? None<T>() : Some<T>(this.first);

  ///
  /// Primer elemento que cumple una condición
  /// Se devuelve
  /// * None() Si no se encuentra ningún elemento
  /// * Some( x ) Si se encuentra algún elemento.   Solo se devuelve el primer x que cumpla la condición
  ///
  Optional<T> oFirstWhere(bool test(T element)) {
    for (T element in this) {
      if (test(element)) return Some(element);
    }
    return const None();
  }
}

extension OptionalStringExtension on String {
  Optional<String> toOptional() => Some<String>(this);
}

///
/// Encapsula un valor en Some
/// Ej:  "hola".toSome() equivale a  Some("home")
///
extension OptionalExtension<T> on T {
  Optional<T> toSome() => Some<T>(this);
}
