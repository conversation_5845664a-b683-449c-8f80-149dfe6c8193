import 'package:intl/intl.dart';
///
/// Los campos que se refieren a descripciones públicas, siguen una estructura de objeto json
/// { "default":"Property", "es": "Propiedad", "ca": "Propietat"}
/// 
/// "default" es el idioma por defecto (fallback) usado cuando no hay soporte para un idioma concreto
/// 
/// Se ofrecen utilizades para trabajar con esta estructura de datos
///
extension LocalizedString on Map<String, String> {
  ///
  /// Obtener la cadena correspondiente al idioma [lang] indicado
  /// Se emplea mecanismo de fallback para obtener la cadena (idioma, idioma sin país, default, "" )
  /// Si no se indica [lang] Se usa el idioma actual del sistema
  ///
  String getByLang(String? lang) {
    lang = lang ?? Intl.getCurrentLocale();
    return this[lang] ?? this[lang.split("_")[0]] ?? this["default"] ?? "";
  }
  ///
  /// Similar a [getByLang], pero usando siempre el idioma actual del sistema
  ///
  String get localized => this.getByLang(null);
}

