extension StringUtils on String {
  /**
   * Evoca una función usando la string como parámetro.
   * Útil para realizar operaciones encadenadas sobre string en una única expresión 
   * 
   * Ej: Función clásica con variables auxiliares.
   * 
   *  String enumToString(DemandstatusCode value) {
   *    final str = value.toString();
   *    return str.substring( str.indexOf(".") + 1 );
   *  }
   * 
   * Ej: Misma función usando una expresión única.
   * 
   *  String enumToString(DemandstatusCode value) =>
   *     value.toString().o( (str)=> str.substring(str.indexOf(".") + 1) );
   * 
   */
  T o<T>(T Function(String) fn) => fn(this);

  /**
   * Cambia a mayúsculas el primer caracter del string
   * 
   * Nota: Hacemos un trim del string (esto debería cambiarse)
   */
  String get capitalize => this.trim().o((s) => s.length == 0 ? "" : s[0].toUpperCase() + s.substring(1));
}
