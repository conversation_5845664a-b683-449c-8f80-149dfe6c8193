name: topbrokers
description: percent... the real state personal platform

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.1.06

environment:
  sdk: '>=2.12.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: 0.17.0 #-nullsafety.2
  agentor_utils:
    path: agentor_utils
  agentor_repositoryns:
    path: agentor_repositoryns
  form_builder:
    path: form_builder
  agentor_deps:
    path: agentor_deps
  http: ^0.13.3
  # Usado para proveedores no basados en Bloc.  Ojo: flutter_bloc depende de provider... cuidado al cambiar de versión de flutter_bloc
  provider: ^5.0.0 #^4.1.3
  # Blocs (providers/consummers/...)
  flutter_bloc: ^7.0.0 #^6.0.6
  rxdart: ^0.27.0 #^0.24.0
  equatable: ^2.0.0
  #
  # FabIcon que muestra menú circular (opciones principales de la APP)
  #
  fab_circular_menu: ^1.0.2
  #flutter_boom_menu: ^1.0.2
  #
  # Se basa en shared_preferences, por lo que no es un almacenamiento seguro!!!
  # Lo usamos para poder almacenar en navegador.
  #
  #flutter_session: 0.1.1
  #shared_preferences: ^2.0.4
  agentor_session:
    path: agentor_session
  #
  # Lanzar llamada de teléfono o email
  #
  url_launcher: ^6.1.1 #^5.7.7
  #
  image_picker: ^0.7.4 #0.6.7+22
  image_picker_for_web: ^2.0.0 #0.1.0+2
  # Utilizado para decodificar y conocer el tipo de una imagen selecionada con image_picker_for_web
  image: ^3.0.2 #3.0.0-nullsafety.0 # ^2.1.19
  # Nos permite especificar con sintaxis html el contenido de richtext (genera los textspan debidos)
  simple_html_css: ^3.0.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0
  # Google Fonts
  google_fonts: ^2.0.0
  # Generación excel
  excel: 2.0.0-null-safety
  video_player: ^2.2.18
  chewie: ^1.2.2

dev_dependencies:
  flutter_driver:
    sdk: flutter
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  fonts:
    - family: CustomAppIcons
      fonts:
        - asset: fonts/CustomAppIcons.ttf
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  generate: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - images/
    - images/landing/
    - images/landing/buyer/
    - images/landing/seller/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
